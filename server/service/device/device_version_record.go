package device

import (
	"context"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
	deviceReq "github.com/flipped-aurora/gin-vue-admin/server/model/device/request"
	deviceRes "github.com/flipped-aurora/gin-vue-admin/server/model/device/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"strconv"
	"strings"
	"time"
)

type DeviceVersionRecordService struct{}

// CheckUpdate 检查设备是否需要更新版本
func (deviceVersionRecordService *DeviceVersionRecordService) CheckUpdate(ctx context.Context, deviceId string, req deviceReq.CheckUpdateRequest) (res deviceRes.CheckUpdateResponse, err error) {
	// 根据设备ID查询设备信息获取操作系统类型
	var deviceInfo device.Device
	err = global.GVA_DB.Where("device_id = ?", deviceId).First(&deviceInfo).Error
	if err != nil {
		return res, fmt.Errorf("查询设备信息失败: %v", err)
	}

	// 查询该操作系统的最新版本
	var latestVersion system.SysVersion
	err = global.GVA_DB.Where("os_type = ?", deviceInfo.OSName).Order("created_at DESC").First(&latestVersion).Error
	if err != nil {
		return res, fmt.Errorf("查询最新版本失败: %v", err)
	}

	// 比较版本号
	needUpdate := deviceVersionRecordService.compareVersion(req.CurrentVersion, *latestVersion.Version)

	res = deviceRes.CheckUpdateResponse{
		NeedUpdate:     needUpdate,
		LatestVersion:  *latestVersion.Version,
		CurrentVersion: req.CurrentVersion,
		UpdateContent:  *latestVersion.UpdateContent,
		VersionInfo:    &latestVersion,
	}

	return res, nil
}

// RecordUpdate 记录设备版本更新
func (deviceVersionRecordService *DeviceVersionRecordService) RecordUpdate(ctx context.Context, deviceId string, req deviceReq.RecordUpdateRequest) (err error) {
	// 根据设备ID查询设备信息获取操作系统类型
	var deviceInfo device.Device
	err = global.GVA_DB.Where("device_id = ?", deviceId).First(&deviceInfo).Error
	if err != nil {
		return fmt.Errorf("查询设备信息失败: %v", err)
	}

	record := device.DeviceVersionRecord{
		DeviceId:       deviceId,
		OsType:         deviceInfo.OSName,
		CurrentVersion: req.CurrentVersion,
		TargetVersion:  req.TargetVersion,
		UpdateStatus:   req.UpdateStatus,
		UpdateTime:     time.Now(),
		Remark:         req.Remark,
	}

	err = global.GVA_DB.Create(&record).Error
	return err
}

// GetDeviceVersionRecordList 分页获取设备版本更新记录列表
func (deviceVersionRecordService *DeviceVersionRecordService) GetDeviceVersionRecordList(ctx context.Context, info deviceReq.DeviceVersionRecordSearch) (list []device.DeviceVersionRecord, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 创建db
	db := global.GVA_DB.Model(&device.DeviceVersionRecord{})
	var records []device.DeviceVersionRecord

	// 条件搜索
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.DeviceId != "" {
		db = db.Where("device_id = ?", info.DeviceId)
	}
	if info.OsType != "" {
		db = db.Where("os_type = ?", info.OsType)
	}
	if info.UpdateStatus != nil {
		db = db.Where("update_status = ?", *info.UpdateStatus)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Order("created_at DESC").Find(&records).Error
	return records, total, err
}

// GetDeviceVersionRecord 根据ID获取设备版本更新记录
func (deviceVersionRecordService *DeviceVersionRecordService) GetDeviceVersionRecord(ctx context.Context, ID string) (record device.DeviceVersionRecord, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&record).Error
	return
}

// UpdateDeviceVersionRecord 更新设备版本更新记录
func (deviceVersionRecordService *DeviceVersionRecordService) UpdateDeviceVersionRecord(ctx context.Context, record device.DeviceVersionRecord) (err error) {
	err = global.GVA_DB.Model(&device.DeviceVersionRecord{}).Where("id = ?", record.ID).Updates(&record).Error
	return err
}

// DeleteDeviceVersionRecord 删除设备版本更新记录
func (deviceVersionRecordService *DeviceVersionRecordService) DeleteDeviceVersionRecord(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&device.DeviceVersionRecord{}, "id = ?", ID).Error
	return err
}

// compareVersion 比较版本号，返回是否需要更新
// 如果 currentVersion < latestVersion 返回 true，表示需要更新
func (deviceVersionRecordService *DeviceVersionRecordService) compareVersion(currentVersion, latestVersion string) bool {
	// 简单的版本号比较，支持 x.y.z 格式
	currentParts := strings.Split(currentVersion, ".")
	latestParts := strings.Split(latestVersion, ".")

	// 补齐版本号位数
	maxLen := len(currentParts)
	if len(latestParts) > maxLen {
		maxLen = len(latestParts)
	}

	for len(currentParts) < maxLen {
		currentParts = append(currentParts, "0")
	}
	for len(latestParts) < maxLen {
		latestParts = append(latestParts, "0")
	}

	// 逐位比较
	for i := 0; i < maxLen; i++ {
		currentNum, _ := strconv.Atoi(currentParts[i])
		latestNum, _ := strconv.Atoi(latestParts[i])

		if currentNum < latestNum {
			return true // 需要更新
		} else if currentNum > latestNum {
			return false // 不需要更新
		}
		// 相等则继续比较下一位
	}

	return false // 版本相同，不需要更新
}

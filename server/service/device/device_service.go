package device

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/service/websocket"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
	deviceReq "github.com/flipped-aurora/gin-vue-admin/server/model/device/request"
	deviceRes "github.com/flipped-aurora/gin-vue-admin/server/model/device/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemService "github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type DeviceService struct{}

// Redis 缓存相关常量
const (
	CachePrefix            = "device:"
	DeviceCachePrefix      = "device:"       // 单个设备缓存前缀
	UserDevicesCachePrefix = "user_devices:" // 用户设备列表缓存前缀
)

// cacheDeviceInfo 缓存单个设备信息到Redis
func (deviceService *DeviceService) cacheDeviceInfo(userID uint, deviceRecord *device.Device) error {
	if global.GVA_REDIS == nil {
		return nil // Redis未初始化，跳过缓存
	}

	ctx := context.Background()

	// 序列化设备信息
	deviceData, err := json.Marshal(deviceRecord)
	if err != nil {
		global.GVA_LOG.Error("序列化设备信息失败", zap.Error(err))
		return err
	}

	// 缓存单个设备信息：使用统一的键格式 device:{deviceId}
	deviceKey := fmt.Sprintf("%s%s", CachePrefix+DeviceCachePrefix, deviceRecord.DeviceID)
	err = global.GVA_REDIS.Set(ctx, deviceKey, string(deviceData), 0).Err() // 使用0表示永久存储，不设置过期时间
	if err != nil {
		global.GVA_LOG.Error("缓存设备信息失败", zap.String("key", deviceKey), zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("成功缓存设备信息", zap.String("deviceId", deviceRecord.DeviceID), zap.Uint("userId", userID))
	return nil
}

// cacheUserDevices 缓存用户所有设备信息到Redis
func (deviceService *DeviceService) cacheUserDevices(userID uint) error {
	if global.GVA_REDIS == nil {
		return nil // Redis未初始化，跳过缓存
	}

	ctx := context.Background()

	// 查询用户所有设备
	var userDevices []device.Device
	err := global.GVA_DB.Where("user_id = ?", userID).Find(&userDevices).Error
	if err != nil {
		global.GVA_LOG.Error("查询用户设备失败", zap.Uint("userId", userID), zap.Error(err))
		return err
	}

	// 序列化用户设备列表
	devicesData, err := json.Marshal(userDevices)
	if err != nil {
		global.GVA_LOG.Error("序列化用户设备列表失败", zap.Error(err))
		return err
	}

	// 缓存用户设备列表：userId为key
	userDevicesKey := fmt.Sprintf("%s%d", CachePrefix+UserDevicesCachePrefix, userID)
	err = global.GVA_REDIS.Set(ctx, userDevicesKey, string(devicesData), 0).Err() // 使用0表示永久存储，不设置过期时间
	if err != nil {
		global.GVA_LOG.Error("缓存用户设备列表失败", zap.String("key", userDevicesKey), zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("成功缓存用户设备列表", zap.Uint("userId", userID), zap.Int("deviceCount", len(userDevices)))
	return nil
}

// getCachedDeviceInfo 从Redis获取缓存的设备信息
func (deviceService *DeviceService) getCachedDeviceInfo(deviceID string, userID uint) (*device.Device, error) {
	if global.GVA_REDIS == nil {
		return nil, fmt.Errorf("Redis未初始化")
	}

	ctx := context.Background()
	deviceKey := fmt.Sprintf("%s%s", CachePrefix+DeviceCachePrefix, deviceID)

	result, err := global.GVA_REDIS.Get(ctx, deviceKey).Result()
	if err != nil {
		return nil, err
	}

	var deviceRecord device.Device
	err = json.Unmarshal([]byte(result), &deviceRecord)
	if err != nil {
		return nil, err
	}

	return &deviceRecord, nil
}

// getCachedUserDevices 从Redis获取缓存的用户设备列表
func (deviceService *DeviceService) getCachedUserDevices(userID uint) ([]device.Device, error) {
	if global.GVA_REDIS == nil {
		return nil, fmt.Errorf("Redis未初始化")
	}

	ctx := context.Background()
	userDevicesKey := fmt.Sprintf("%s%d", CachePrefix+UserDevicesCachePrefix, userID)

	result, err := global.GVA_REDIS.Get(ctx, userDevicesKey).Result()
	if err != nil {
		return nil, err
	}

	var devices []device.Device
	err = json.Unmarshal([]byte(result), &devices)
	if err != nil {
		return nil, err
	}

	return devices, nil
}

// clearUserDevicesCache 清理用户设备列表缓存
func (deviceService *DeviceService) clearUserDevicesCache(userID uint) error {
	if global.GVA_REDIS == nil {
		return nil // Redis未初始化，跳过缓存清理
	}

	ctx := context.Background()
	userDevicesKey := fmt.Sprintf("%s%d", CachePrefix+UserDevicesCachePrefix, userID)

	err := global.GVA_REDIS.Del(ctx, userDevicesKey).Err()
	if err != nil {
		global.GVA_LOG.Error("清理用户设备列表缓存失败", zap.String("key", userDevicesKey), zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("成功清理用户设备列表缓存", zap.Uint("userId", userID))
	return nil
}

// clearDeviceCache 清理单个设备缓存
func (deviceService *DeviceService) clearDeviceCache(deviceID string) error {
	if global.GVA_REDIS == nil {
		return nil // Redis未初始化，跳过缓存清理
	}

	ctx := context.Background()
	deviceKey := fmt.Sprintf("%s%s", CachePrefix+DeviceCachePrefix, deviceID)

	err := global.GVA_REDIS.Del(ctx, deviceKey).Err()
	if err != nil {
		global.GVA_LOG.Error("清理设备缓存失败", zap.String("key", deviceKey), zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("成功清理设备缓存", zap.String("deviceId", deviceID))
	return nil
}

// ReportDevice 设备上报
func (deviceService *DeviceService) ReportDevice(req *deviceReq.DeviceReportRequest, ipAddress, userAgent string, userID *uint) (*deviceRes.DeviceReportResponse, error) {
	now := time.Now()

	// 生成硬件特征哈希
	hardwareHash := deviceService.generateHardwareHash(req)

	// 序列化上报数据
	reportData, err := json.Marshal(req)
	if err != nil {
		global.GVA_LOG.Error("序列化上报数据失败", zap.Error(err))
		return nil, fmt.Errorf("序列化上报数据失败: %v", err)
	}

	var deviceRecord device.Device
	var isNewDevice bool

	// 查找是否存在该设备
	err = global.GVA_DB.Where("device_id = ?", req.DeviceID).First(&deviceRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 新设备，创建记录
			isNewDevice = true
			deviceRecord = device.Device{
				DeviceID:     req.DeviceID,
				HardwareHash: hardwareHash,
				CPUInfo:      req.CPUInfo,
				MemoryInfo:   req.MemoryInfo,
				DiskInfo:     req.DiskInfo,
				NetworkInfo:  req.NetworkInfo,
				GPUInfo:      req.GPUInfo,
				OSName:       req.OSName,
				OSVersion:    req.OSVersion,
				OSArch:       req.OSArch,
				Hostname:     req.Hostname,
				Username:     req.Username,
				UserHomeDir:  req.UserHomeDir,
				WorkDir:      req.WorkDir,
				AppVersion:   req.AppVersion,
				AppBuildNo:   req.AppBuildNo,
				IPAddress:    ipAddress,
				MACAddress:   req.MACAddress,
				FirstSeenAt:  now,
				LastSeenAt:   now,
				LastReportAt: now,
				Status:       device.DeviceStatusNormal,
				ReportCount:  1,
			}

			// 只有当userID不为空时才赋值UserID
			if userID != nil {
				deviceRecord.UserID = userID
			}

			err = global.GVA_DB.Create(&deviceRecord).Error
			if err != nil {
				global.GVA_LOG.Error("创建设备记录失败", zap.Error(err))
				return nil, fmt.Errorf("创建设备记录失败: %v", err)
			}
		} else {
			global.GVA_LOG.Error("查询设备失败", zap.Error(err))
			return nil, fmt.Errorf("查询设备失败: %v", err)
		}
	} else {
		// 设备已存在，更新信息
		isNewDevice = false
		updateData := map[string]interface{}{
			"hardware_hash":  hardwareHash,
			"cpu_info":       req.CPUInfo,
			"memory_info":    req.MemoryInfo,
			"disk_info":      req.DiskInfo,
			"network_info":   req.NetworkInfo,
			"gpu_info":       req.GPUInfo,
			"os_name":        req.OSName,
			"os_version":     req.OSVersion,
			"os_arch":        req.OSArch,
			"hostname":       req.Hostname,
			"username":       req.Username,
			"user_home_dir":  req.UserHomeDir,
			"work_dir":       req.WorkDir,
			"app_version":    req.AppVersion,
			"app_build_no":   req.AppBuildNo,
			"ip_address":     ipAddress,
			"mac_address":    req.MACAddress,
			"last_seen_at":   now,
			"last_report_at": now,
			"report_count":   gorm.Expr("report_count + 1"),
		}

		// 只有当userID不为空时才赋值UserID
		if userID != nil {
			updateData["user_id"] = userID
		}

		err = global.GVA_DB.Model(&deviceRecord).Where("device_id = ?", req.DeviceID).Updates(updateData).Error
		if err != nil {
			global.GVA_LOG.Error("更新设备记录失败", zap.Error(err))
			return nil, fmt.Errorf("更新设备记录失败: %v", err)
		}
	}

	// 创建上报记录
	reportRecord := device.DeviceReport{
		DeviceID:   req.DeviceID,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
		ReportData: string(reportData),
		ReportAt:   now,
	}

	// 只有当userID不为空时才赋值UserID
	if userID != nil {
		reportRecord.UserID = userID
	}

	err = global.GVA_DB.Create(&reportRecord).Error
	if err != nil {
		global.GVA_LOG.Error("创建上报记录失败", zap.Error(err))
		// 不影响主流程，仅记录日志
	}

	// 只有当userID不为空时才进行Redis缓存
	if userID != nil {
		// 重新查询设备记录以获取完整信息（包括数据库生成的字段）
		var completeDeviceRecord device.Device
		err = global.GVA_DB.Where("device_id = ?", req.DeviceID).First(&completeDeviceRecord).Error
		if err == nil {
			// 缓存单个设备信息
			err = deviceService.cacheDeviceInfo(*userID, &completeDeviceRecord)
			if err != nil {
				global.GVA_LOG.Error("缓存设备信息失败", zap.Error(err))
				// 缓存失败不影响主流程
			}

			// 缓存用户所有设备信息
			err = deviceService.cacheUserDevices(*userID)
			if err != nil {
				global.GVA_LOG.Error("缓存用户设备列表失败", zap.Error(err))
				// 缓存失败不影响主流程
			}
		} else {
			global.GVA_LOG.Error("重新查询设备记录失败", zap.Error(err))
		}
	}

	return &deviceRes.DeviceReportResponse{
		DeviceID:    req.DeviceID,
		IsNewDevice: isNewDevice,
		ReportTime:  now,
		Message:     "设备信息上报成功",
	}, nil
}

// GetDeviceList 获取设备列表
func (deviceService *DeviceService) GetDeviceList(req *deviceReq.DeviceSearchRequest) (*deviceRes.DeviceListResponse, error) {
	var devices []device.Device
	var total int64

	db := global.GVA_DB.Model(&device.Device{})

	// 添加搜索条件
	if req.DeviceID != "" {
		db = db.Where("device_id LIKE ?", "%"+req.DeviceID+"%")
	}
	if req.OSName != "" {
		db = db.Where("os_name = ?", req.OSName)
	}
	if req.Status != 0 {
		db = db.Where("status = ?", req.Status)
	}
	if req.UserID != nil && *req.UserID != 0 {
		db = db.Where("user_id = ?", *req.UserID)
	}
	if req.StartCreatedAt != nil && *req.StartCreatedAt != "" {
		db = db.Where("created_at >= ?", *req.StartCreatedAt)
	}
	if req.EndCreatedAt != nil && *req.EndCreatedAt != "" {
		db = db.Where("created_at <= ?", *req.EndCreatedAt)
	}

	// 获取总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, fmt.Errorf("获取设备总数失败: %v", err)
	}

	// 分页查询
	err = db.Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).
		Order("last_report_at DESC").Find(&devices).Error
	if err != nil {
		return nil, fmt.Errorf("获取设备列表失败: %v", err)
	}

	// 更新设备的IsActive状态，根据WebSocket连接状态来判断
	for i := range devices {
		// 检查设备是否有WebSocket连接
		if websocket.GlobalWebSocketService != nil {
			devices[i].IsActive = websocket.GlobalWebSocketService.IsDeviceConnected(devices[i].DeviceID)
		} else {
			// WebSocket服务未启动，设备状态为不活跃
			devices[i].IsActive = false
		}
	}

	return &deviceRes.DeviceListResponse{
		List:  devices,
		Total: total,
	}, nil
}

// GetDeviceDetail 获取设备详情
func (deviceService *DeviceService) GetDeviceDetail(deviceID string) (*deviceRes.DeviceDetailResponse, error) {
	var deviceRecord device.Device

	// 获取设备信息
	err := global.GVA_DB.Where("device_id = ?", deviceID).First(&deviceRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("设备不存在")
		}
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}

	// 获取上报历史（最近10条）
	var reportHistory []device.DeviceReport
	err = global.GVA_DB.Where("device_id = ?", deviceID).
		Order("report_at DESC").Limit(10).Find(&reportHistory).Error
	if err != nil {
		global.GVA_LOG.Error("获取上报历史失败", zap.Error(err))
		// 不影响主流程
	}

	// 获取用户信息（如果有关联）
	var userInfo *deviceRes.UserInfo
	if deviceRecord.UserID != nil {
		// 这里简化处理，实际应该调用用户服务
		userInfo = &deviceRes.UserInfo{
			ID:       *deviceRecord.UserID,
			Username: "用户" + fmt.Sprintf("%d", *deviceRecord.UserID),
			NickName: "昵称" + fmt.Sprintf("%d", *deviceRecord.UserID),
		}
	}

	return &deviceRes.DeviceDetailResponse{
		Device:        deviceRecord,
		ReportHistory: reportHistory,
		UserInfo:      userInfo,
	}, nil
}

// GetDeviceReportList 获取设备上报记录列表
func (deviceService *DeviceService) GetDeviceReportList(req *deviceReq.DeviceReportSearchRequest) (*deviceRes.DeviceReportListResponse, error) {
	var reports []device.DeviceReport
	var total int64

	db := global.GVA_DB.Model(&device.DeviceReport{})

	// 添加搜索条件
	if req.DeviceID != "" {
		db = db.Where("device_id = ?", req.DeviceID)
	}
	if req.UserID != nil && *req.UserID != 0 {
		db = db.Where("user_id = ?", *req.UserID)
	}
	if req.StartReportAt != nil && *req.StartReportAt != "" {
		db = db.Where("report_at >= ?", *req.StartReportAt)
	}
	if req.EndReportAt != nil && *req.EndReportAt != "" {
		db = db.Where("report_at <= ?", *req.EndReportAt)
	}

	// 获取总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, fmt.Errorf("获取上报记录总数失败: %v", err)
	}

	// 分页查询
	err = db.Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).
		Order("report_at DESC").Find(&reports).Error
	if err != nil {
		return nil, fmt.Errorf("获取上报记录列表失败: %v", err)
	}

	return &deviceRes.DeviceReportListResponse{
		List:  reports,
		Total: total,
	}, nil
}

// GetMyDevices 获取用户的所有设备列表
func (deviceService *DeviceService) GetMyDevices(userID uint) (*deviceRes.DeviceListResponse, error) {
	var devices []device.Device
	var total int64

	db := global.GVA_DB.Model(&device.Device{})

	// 只查询指定用户的设备
	db = db.Where("user_id = ?", userID)

	// 获取总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, fmt.Errorf("获取设备总数失败: %v", err)
	}

	// 查询所有设备（不分页）
	err = db.Order("last_report_at DESC").Find(&devices).Error
	if err != nil {
		return nil, fmt.Errorf("获取设备列表失败: %v", err)
	}

	// 更新设备的IsActive状态，根据WebSocket连接状态来判断
	for i := range devices {
		// 检查设备是否有WebSocket连接
		if websocket.GlobalWebSocketService != nil {
			devices[i].IsActive = websocket.GlobalWebSocketService.IsDeviceConnected(devices[i].DeviceID)
		} else {
			// WebSocket服务未启动，设备状态为不活跃
			devices[i].IsActive = false
		}
	}

	return &deviceRes.DeviceListResponse{
		List:  devices,
		Total: total,
	}, nil
}

// generateHardwareHash 生成硬件特征哈希
func (deviceService *DeviceService) generateHardwareHash(req *deviceReq.DeviceReportRequest) string {
	// 组合关键硬件信息生成哈希
	var builder strings.Builder
	builder.WriteString(req.CPUInfo)
	builder.WriteString(req.MemoryInfo)
	builder.WriteString(req.MACAddress)
	builder.WriteString(req.OSName)
	builder.WriteString(req.OSVersion)

	hash := md5.Sum([]byte(builder.String()))
	return fmt.Sprintf("%x", hash)
}

// AssignDefaultAgent 分配默认智能体
func (deviceService *DeviceService) AssignDefaultAgent(userID string, deviceID string) (*deviceRes.AssignDefaultAgentResponse, error) {
	// 检查Domain配置
	if global.GVA_CONFIG.System.Domain == "" {
		global.GVA_LOG.Error("系统配置中Domain为空")
		return nil, fmt.Errorf("系统配置中Domain为空，请检查配置文件")
	}

	// 构造请求URL
	url := fmt.Sprintf("%s/xiaozhi/agent/assign-default/%s/%s", global.GVA_CONFIG.System.Domain, userID, deviceID)

	// 记录请求信息
	global.GVA_LOG.Info("准备调用分配默认智能体API",
		zap.String("url", url),
		zap.String("userID", userID),
		zap.String("deviceID", deviceID))

	//验证deviceID是否存在
	var deviceRecord device.Device
	err := global.GVA_DB.Where("device_id = ? and user_id = ?", deviceID, userID).First(&deviceRecord).Error
	if err != nil {
		global.GVA_LOG.Error("设备验证失败",
			zap.String("deviceID", deviceID),
			zap.String("userID", userID),
			zap.Error(err))
		return nil, fmt.Errorf("设备不存在或不属于当前用户")
	}

	// 发送POST请求
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		global.GVA_LOG.Error("调用外部API失败", zap.Error(err))
		return nil, fmt.Errorf("调用外部API失败: %v", err)
	}
	defer resp.Body.Close()

	// 记录请求URL和响应状态码
	global.GVA_LOG.Info("调用外部API", zap.String("url", url), zap.Int("status_code", resp.StatusCode))

	// 读取响应体用于调试
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取响应体失败", zap.Error(err))
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 记录响应内容（前200个字符用于调试）
	responsePreview := string(body)
	if len(responsePreview) > 200 {
		responsePreview = responsePreview[:200] + "..."
	}
	global.GVA_LOG.Info("API响应内容", zap.String("response", responsePreview))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("API返回错误状态码",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", string(body)))
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 检查响应是否为JSON格式
	if !strings.HasPrefix(strings.TrimSpace(string(body)), "{") && !strings.HasPrefix(strings.TrimSpace(string(body)), "[") {
		global.GVA_LOG.Error("API返回非JSON格式响应", zap.String("response", string(body)))
		return nil, fmt.Errorf("API返回非JSON格式响应: %s", string(body))
	}

	// 解析外部API响应
	var externalResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			MCPAccessAddress string `json:"mcpAccessAddress"`
			Id               string `json:"id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &externalResp); err != nil {
		global.GVA_LOG.Error("解析响应失败",
			zap.Error(err),
			zap.String("response_body", string(body)),
			zap.String("url", url))
		return nil, fmt.Errorf("解析响应失败: %v, 响应内容: %s", err, string(body))
	}

	// 检查响应状态
	if externalResp.Code != 0 {
		return nil, fmt.Errorf("外部API返回错误: %s", externalResp.Msg)
	}

	// 更新设备记录
	updateData := map[string]interface{}{
		"mcp_access_address": externalResp.Data.MCPAccessAddress,
		"agent_id":           externalResp.Data.Id,
	}

	err = global.GVA_DB.Model(&deviceRecord).Where("device_id = ? and user_id = ?", deviceID, userID).Updates(updateData).Error
	if err != nil {
		global.GVA_LOG.Error("更新设备MCP信息失败", zap.Error(err))
		return nil, fmt.Errorf("更新设备MCP信息失败: %v", err)
	}

	// 转换userID为uint类型以便更新缓存
	userIDUint, err := strconv.ParseUint(userID, 10, 32)
	if err != nil {
		global.GVA_LOG.Error("转换用户ID失败", zap.String("userID", userID), zap.Error(err))
	} else {
		// 重新查询设备记录以获取完整信息
		var updatedDeviceRecord device.Device
		err = global.GVA_DB.Where("device_id = ? and user_id = ?", deviceID, userID).First(&updatedDeviceRecord).Error
		if err == nil {
			// 更新单个设备信息缓存
			err = deviceService.cacheDeviceInfo(uint(userIDUint), &updatedDeviceRecord)
			if err != nil {
				global.GVA_LOG.Error("更新设备缓存失败", zap.Error(err))
				// 缓存失败不影响主流程
			}

			// 更新用户所有设备信息缓存
			err = deviceService.cacheUserDevices(uint(userIDUint))
			if err != nil {
				global.GVA_LOG.Error("更新用户设备列表缓存失败", zap.Error(err))
				// 缓存失败不影响主流程
			}
		} else {
			global.GVA_LOG.Error("重新查询设备记录失败", zap.Error(err))
		}
	}

	// 返回结果
	return &deviceRes.AssignDefaultAgentResponse{
		MCPAccessAddress: externalResp.Data.MCPAccessAddress,
		AgentId:          externalResp.Data.Id,
	}, nil
}

// GetIpLocation 获取IP归属地信息
func (deviceService *DeviceService) GetIpLocation(ip string) (*deviceRes.IpLocationResponse, error) {
	// 构造请求URL
	host := "https://kzipglobal.market.alicloudapi.com"
	path := "/api/ip/query"
	//ip字段前后去空格
	ip = strings.TrimSpace(ip)
	url := fmt.Sprintf("%s%s?ip=%s", host, path, ip)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		global.GVA_LOG.Error("创建IP归属地请求失败", zap.Error(err))
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "APPCODE "+global.GVA_CONFIG.Aliyun.Market.IpQuery.AppCode)
	req.Header.Set("Content-Type", "application/json; charset=utf-8")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		global.GVA_LOG.Error("调用IP归属地API失败", zap.Error(err))
		return nil, fmt.Errorf("调用IP归属地API失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取IP归属地响应失败", zap.Error(err))
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp struct {
		Msg     string `json:"msg"`
		Success bool   `json:"success"`
		Code    int    `json:"code"`
		Data    struct {
			OrderNo  string `json:"orderNo"`
			Nation   string `json:"nation"`
			Province string `json:"province"`
			City     string `json:"city"`
			IP       string `json:"ip"`
			ISP      string `json:"isp"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		global.GVA_LOG.Error("解析IP归属地响应失败",
			zap.Error(err),
			zap.String("response_body", string(body)),
		)
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查API响应状态
	if !apiResp.Success || apiResp.Code != 200 {
		return nil, fmt.Errorf("IP归属地查询失败: %s", apiResp.Msg)
	}

	// 返回结果
	return &deviceRes.IpLocationResponse{
		IP:       apiResp.Data.IP,
		Country:  apiResp.Data.Nation,
		Province: apiResp.Data.Province,
		City:     apiResp.Data.City,
		ISP:      apiResp.Data.ISP,
		OrderNo:  apiResp.Data.OrderNo,
	}, nil
}

func (deviceService *DeviceService) UpdateDeviceRedis(userID *uint) error {
	if userID == nil {
		return fmt.Errorf("用户ID不能为空")
	}

	// 缓存用户所有设备信息
	err := deviceService.cacheUserDevices(*userID)
	if err != nil {
		global.GVA_LOG.Error("缓存用户设备列表失败", zap.Error(err))
		return fmt.Errorf("缓存用户设备列表失败: %v", err)
	}

	//查询用户下的设备
	var devices []device.Device
	err = global.GVA_DB.Where("user_id = ?", userID).Find(&devices).Error
	if err != nil {
		global.GVA_LOG.Error("查询用户设备失败", zap.Uint("userId", *userID), zap.Error(err))
		return fmt.Errorf("查询用户设备失败: %v", err)
	}

	//遍历设备，缓存每个设备
	for _, device := range devices {
		err = deviceService.cacheDeviceInfo(*userID, &device)
		if err != nil {
			global.GVA_LOG.Error("缓存设备信息失败", zap.Error(err))
			// 缓存失败不影响主流程，但记录错误
		}
	}

	global.GVA_LOG.Info("成功更新用户设备缓存", zap.Uint("userId", *userID), zap.Int("deviceCount", len(devices)))
	return nil
}

// BindDevice 登录时绑定用户与设备关系
// 当设备没有userId或设备上的userId不是当前用户id时，则更新userId，并更新缓存
func (deviceService *DeviceService) BindDevice(deviceId string, userID *uint) error {
	// 参数校验
	if deviceId == "" {
		return fmt.Errorf("设备ID不能为空")
	}
	if userID == nil {
		return fmt.Errorf("用户ID不能为空")
	}

	// 查询设备记录
	var deviceRecord device.Device
	err := global.GVA_DB.Where("device_id = ?", deviceId).First(&deviceRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("设备不存在")
		}
		global.GVA_LOG.Error("查询设备失败", zap.String("deviceId", deviceId), zap.Error(err))
		return fmt.Errorf("查询设备失败: %v", err)
	}

	// 检查是否需要绑定：设备没有userId或设备上的userId不是当前用户id
	needBind := false
	var oldUserID *uint
	if deviceRecord.UserID == nil {
		// 设备没有绑定用户
		needBind = true
		global.GVA_LOG.Info("设备未绑定用户，需要绑定",
			zap.String("deviceId", deviceId),
			zap.Uint("userId", *userID))
	} else if *deviceRecord.UserID != *userID {
		// 设备绑定的用户不是当前用户
		needBind = true
		oldUserID = deviceRecord.UserID // 保存旧用户ID，用于清理缓存
		global.GVA_LOG.Info("设备绑定的用户不是当前用户，需要重新绑定",
			zap.String("deviceId", deviceId),
			zap.Uint("oldUserId", *deviceRecord.UserID),
			zap.Uint("newUserId", *userID))
	}

	// 如果需要绑定，则更新设备的userId
	if needBind {
		// 如果有旧用户，先清理旧用户的缓存
		if oldUserID != nil {
			// 清理设备缓存（现在设备缓存不区分用户）
			err = deviceService.clearDeviceCache(deviceId)
			if err != nil {
				global.GVA_LOG.Error("清理设备缓存失败",
					zap.String("deviceId", deviceId),
					zap.Uint("oldUserId", *oldUserID),
					zap.Error(err))
				// 缓存清理失败不影响主流程，但记录错误
			}

			// 清理旧用户的设备列表缓存
			err = deviceService.clearUserDevicesCache(*oldUserID)
			if err != nil {
				global.GVA_LOG.Error("清理旧用户设备列表缓存失败",
					zap.Uint("oldUserId", *oldUserID),
					zap.Error(err))
				// 缓存清理失败不影响主流程，但记录错误
			}
		}

		err = global.GVA_DB.Model(&deviceRecord).Where("device_id = ?", deviceId).Update("user_id", *userID).Error
		if err != nil {
			global.GVA_LOG.Error("更新设备用户绑定失败",
				zap.String("deviceId", deviceId),
				zap.Uint("userId", *userID),
				zap.Error(err))
			return fmt.Errorf("更新设备用户绑定失败: %v", err)
		}

		global.GVA_LOG.Info("成功绑定设备到用户",
			zap.String("deviceId", deviceId),
			zap.Uint("userId", *userID))

		// 重新查询设备记录以获取完整信息（包括数据库生成的字段）
		var completeDeviceRecord device.Device
		err = global.GVA_DB.Where("device_id = ?", deviceId).First(&completeDeviceRecord).Error
		if err != nil {
			global.GVA_LOG.Error("重新查询设备记录失败", zap.String("deviceId", deviceId), zap.Error(err))
			return fmt.Errorf("重新查询设备记录失败: %v", err)
		}

		// 更新新用户的Redis缓存
		// 缓存单个设备信息
		err = deviceService.cacheDeviceInfo(*userID, &completeDeviceRecord)
		if err != nil {
			global.GVA_LOG.Error("缓存设备信息失败", zap.String("deviceId", deviceId), zap.Error(err))
			// 缓存失败不影响主流程，但记录错误
		}

		// 缓存用户所有设备信息
		err = deviceService.cacheUserDevices(*userID)
		if err != nil {
			global.GVA_LOG.Error("缓存用户设备列表失败", zap.Uint("userId", *userID), zap.Error(err))
			// 缓存失败不影响主流程，但记录错误
		}
	}

	return nil
}

// UpdateDeviceName 修改设备名称
func (deviceService *DeviceService) UpdateDeviceName(req *deviceReq.UpdateDeviceNameRequest, userID uint) (*deviceRes.UpdateDeviceNameResponse, error) {
	// 参数校验
	if req.DeviceID == "" {
		return nil, fmt.Errorf("设备ID不能为空")
	}
	if req.DeviceName == "" {
		return nil, fmt.Errorf("设备名称不能为空")
	}

	// 查询设备记录，确保设备存在且属于当前用户
	var deviceRecord device.Device
	err := global.GVA_DB.Where("device_id = ? AND user_id = ?", req.DeviceID, userID).First(&deviceRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("设备不存在或不属于当前用户")
		}
		global.GVA_LOG.Error("查询设备失败",
			zap.String("deviceId", req.DeviceID),
			zap.Uint("userId", userID),
			zap.Error(err))
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}

	// 更新设备名称
	err = global.GVA_DB.Model(&deviceRecord).Where("device_id = ? AND user_id = ?", req.DeviceID, userID).
		Update("device_name", req.DeviceName).Error
	if err != nil {
		global.GVA_LOG.Error("更新设备名称失败",
			zap.String("deviceId", req.DeviceID),
			zap.Uint("userId", userID),
			zap.String("deviceName", req.DeviceName),
			zap.Error(err))
		return nil, fmt.Errorf("更新设备名称失败: %v", err)
	}

	global.GVA_LOG.Info("成功更新设备名称",
		zap.String("deviceId", req.DeviceID),
		zap.Uint("userId", userID),
		zap.String("oldName", deviceRecord.DeviceName),
		zap.String("newName", req.DeviceName))

	// 重新查询设备记录以获取完整信息
	var updatedDeviceRecord device.Device
	err = global.GVA_DB.Where("device_id = ? AND user_id = ?", req.DeviceID, userID).First(&updatedDeviceRecord).Error
	if err != nil {
		global.GVA_LOG.Error("重新查询设备记录失败", zap.String("deviceId", req.DeviceID), zap.Error(err))
		// 不影响主流程，继续返回成功响应
	} else {
		// 更新Redis缓存
		// 缓存单个设备信息
		err = deviceService.cacheDeviceInfo(userID, &updatedDeviceRecord)
		if err != nil {
			global.GVA_LOG.Error("更新设备缓存失败", zap.String("deviceId", req.DeviceID), zap.Error(err))
			// 缓存失败不影响主流程
		}

		// 缓存用户所有设备信息
		err = deviceService.cacheUserDevices(userID)
		if err != nil {
			global.GVA_LOG.Error("更新用户设备列表缓存失败", zap.Uint("userId", userID), zap.Error(err))
			// 缓存失败不影响主流程
		}
	}

	return &deviceRes.UpdateDeviceNameResponse{
		DeviceID:   req.DeviceID,
		DeviceName: req.DeviceName,
		Message:    "设备名称修改成功",
	}, nil
}

// DeviceLogout 指定设备登出
func (deviceService *DeviceService) DeviceLogout(req *deviceReq.DeviceLogoutRequest, userID uint) (*deviceRes.DeviceLogoutResponse, error) {
	// 参数校验
	if req.DeviceID == "" {
		return nil, fmt.Errorf("设备ID不能为空")
	}

	// 查询设备记录，确保设备存在且属于当前用户
	var deviceRecord device.Device
	err := global.GVA_DB.Where("device_id = ? AND user_id = ?", req.DeviceID, userID).First(&deviceRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("设备不存在或不属于当前用户")
		}
		global.GVA_LOG.Error("查询设备失败",
			zap.String("deviceId", req.DeviceID),
			zap.Uint("userId", userID),
			zap.Error(err))
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}

	// 首先查询该设备的所有活跃会话，获取token列表
	var sessions []system.SysUserSession
	err = global.GVA_DB.Where("device_id = ? AND user_id = ? AND is_active = ?", req.DeviceID, userID, true).
		Find(&sessions).Error
	if err != nil {
		global.GVA_LOG.Error("查询设备会话失败",
			zap.String("deviceId", req.DeviceID),
			zap.Uint("userId", userID),
			zap.Error(err))
		return nil, fmt.Errorf("查询设备会话失败: %v", err)
	}

	// 开启事务处理设备登出
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 将设备的userId置空
		err := tx.Model(&device.Device{}).Where("device_id = ? AND user_id = ?", req.DeviceID, userID).
			Update("user_id", nil).Error
		if err != nil {
			global.GVA_LOG.Error("清空设备用户ID失败",
				zap.String("deviceId", req.DeviceID),
				zap.Uint("userId", userID),
				zap.Error(err))
			return fmt.Errorf("清空设备用户ID失败: %v", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 2. 停用该设备的所有活跃会话（使用UserSessionService的DeactivateSession方法）
	// 这样可以正确处理JWT黑名单
	for _, session := range sessions {
		err := systemService.UserSessionServiceApp.DeactivateSession(session.Token)
		if err != nil {
			global.GVA_LOG.Error("停用设备会话失败",
				zap.String("deviceId", req.DeviceID),
				zap.Uint("userId", userID),
				zap.String("token", session.Token),
				zap.Error(err))
			// 继续处理其他会话，不中断流程
		} else {
			global.GVA_LOG.Info("成功停用设备会话",
				zap.String("deviceId", req.DeviceID),
				zap.Uint("userId", userID),
				zap.String("sessionId", fmt.Sprintf("%d", session.ID)))
		}
	}

	global.GVA_LOG.Info("设备登出成功",
		zap.String("deviceId", req.DeviceID),
		zap.Uint("userId", userID),
		zap.Int("stoppedSessions", len(sessions)))

	// 更新缓存
	deviceService.clearDeviceCache(req.DeviceID)
	err = deviceService.cacheUserDevices(userID)
	if err != nil {
		global.GVA_LOG.Error("更新用户设备列表缓存失败", zap.Uint("userId", userID), zap.Error(err))
		// 缓存失败不影响主流程
	}

	return &deviceRes.DeviceLogoutResponse{
		DeviceID: req.DeviceID,
		Message:  "设备登出成功",
	}, nil
}

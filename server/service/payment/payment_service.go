package payment

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/payment"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	paymentUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/payment"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PaymentService struct{}

var PaymentServiceApp = new(PaymentService)

// CreatePayment 创建支付订单
func (p *PaymentService) CreatePayment(req paymentUtils.CreatePaymentRequest) (*paymentUtils.CreatePaymentResponse, error) {
	// 验证JSAPI支付必需的openid参数
	if req.PaymentMethod == payment.PaymentMethodWechat && req.DeviceType == paymentUtils.DeviceTypeWechat {
		if req.OpenID == "" {
			return nil, fmt.Errorf("微信JSAPI支付需要提供用户openid")
		}
	}

	// 确保Extra字段包含设备类型信息
	if req.Extra == nil {
		req.Extra = make(map[string]interface{})
	}
	req.Extra["deviceType"] = string(req.DeviceType)

	// 如果是微信支付且有openid，添加到Extra字段
	if req.PaymentMethod == payment.PaymentMethodWechat && req.OpenID != "" {
		req.Extra["openId"] = req.OpenID
	}

	// 创建支付订单
	order := &payment.PaymentOrder{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Currency:      "CNY",
		Subject:       req.Subject,
		Body:          req.Body,
		PaymentMethod: req.PaymentMethod,
		Status:        payment.PaymentStatusPending,
		NotifyUrl:     req.NotifyUrl,
		ReturnUrl:     req.ReturnUrl,
		ClientIP:      req.ClientIP,
		Extra:         req.Extra,
	}

	// 设置过期时间
	if req.ExpireMinutes > 0 {
		expireTime := time.Now().Add(time.Duration(req.ExpireMinutes) * time.Minute)
		order.ExpireTime = &expireTime
	}

	// 保存到数据库
	if err := global.GVA_DB.Create(order).Error; err != nil {
		global.GVA_LOG.Error("创建支付订单失败", zap.Error(err))
		return nil, fmt.Errorf("创建支付订单失败: %v", err)
	}

	// 获取支付客户端
	paymentClient, err := p.getPaymentClient(req.PaymentMethod)
	if err != nil {
		return nil, fmt.Errorf("获取支付客户端失败: %v", err)
	}

	// 调用第三方支付接口
	paymentURL, err := paymentClient.CreatePayment(order)
	if err != nil {
		// 更新订单状态为失败
		global.GVA_DB.Model(order).Update("status", payment.PaymentStatusFailed)
		return nil, fmt.Errorf("创建第三方支付订单失败: %v", err)
	}

	// 更新第三方响应
	global.GVA_DB.Model(order).Update("third_response", paymentURL)

	response := &paymentUtils.CreatePaymentResponse{
		OrderNo:    order.OrderNo,
		PaymentURL: paymentURL,
		ExpireTime: order.ExpireTime.Format(time.RFC3339),
	}

	// 如果是微信JSAPI支付，解析返回的JSAPI参数
	if req.PaymentMethod == payment.PaymentMethodWechat && req.DeviceType == paymentUtils.DeviceTypeWechat {
		var jsapiParams paymentUtils.JSAPIPaymentParams
		if err := json.Unmarshal([]byte(paymentURL), &jsapiParams); err == nil {
			response.JSAPIParams = &jsapiParams
			response.PaymentURL = "" // JSAPI支付不需要URL
		}
	}

	return response, nil
}

// QueryPayment 查询支付状态
func (p *PaymentService) QueryPayment(orderNo string) (*paymentUtils.PaymentQueryResult, error) {
	// 查询订单
	var order payment.PaymentOrder
	if err := global.GVA_DB.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("订单不存在")
		}
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}

	// 如果订单已经是最终状态，直接返回
	if order.Status == payment.PaymentStatusPaid ||
		order.Status == payment.PaymentStatusCancelled ||
		order.Status == payment.PaymentStatusRefunded {
		return &paymentUtils.PaymentQueryResult{
			OrderNo:       order.OrderNo,
			ThirdOrderNo:  order.ThirdOrderNo,
			Status:        order.Status,
			Amount:        order.Amount,
			PayTime:       order.PayTime,
			ThirdResponse: order.ThirdResponse,
		}, nil
	}

	// 获取支付客户端
	paymentClient, err := p.getPaymentClient(order.PaymentMethod)
	if err != nil {
		return nil, fmt.Errorf("获取支付客户端失败: %v", err)
	}

	// 查询第三方支付状态
	result, err := paymentClient.QueryPayment(orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询第三方支付状态失败: %v", err)
	}

	// 更新订单状态
	updateData := map[string]interface{}{
		"status":         result.Status,
		"third_order_no": result.ThirdOrderNo,
		"third_response": result.ThirdResponse,
	}

	if result.PayTime != nil {
		updateData["pay_time"] = result.PayTime
	}

	if err := global.GVA_DB.Model(&order).Updates(updateData).Error; err != nil {
		global.GVA_LOG.Error("更新订单状态失败", zap.Error(err))
	}

	return result, nil
}

// ProcessNotify 处理异步通知
func (p *PaymentService) ProcessNotify(paymentMethod payment.PaymentMethod, notifyData []byte) error {
	// 获取支付客户端
	paymentClient, err := p.getPaymentClient(paymentMethod)
	if err != nil {
		return fmt.Errorf("获取支付客户端失败: %v", err)
	}

	// 验证通知
	notifyResult, err := paymentClient.VerifyNotify(notifyData)
	if err != nil {
		return fmt.Errorf("验证通知失败: %v", err)
	}

	// 查询订单
	var order payment.PaymentOrder
	if err := global.GVA_DB.Where("order_no = ?", notifyResult.OrderNo).First(&order).Error; err != nil {
		return fmt.Errorf("订单不存在: %v", err)
	}

	// 防重复处理
	if order.Status == payment.PaymentStatusPaid {
		return nil
	}

	// 更新订单状态
	updateData := map[string]interface{}{
		"status":         notifyResult.Status,
		"third_order_no": notifyResult.ThirdOrderNo,
		"third_response": string(notifyData),
	}

	if notifyResult.PayTime != nil {
		updateData["pay_time"] = notifyResult.PayTime
	}

	if err := global.GVA_DB.Model(&order).Updates(updateData).Error; err != nil {
		return fmt.Errorf("更新订单状态失败: %v", err)
	}

	// 如果支付成功，处理业务逻辑
	if notifyResult.Status == payment.PaymentStatusPaid {
		// 动态导入产品服务来避免循环依赖
		go func() {
			// 在新的goroutine中处理，避免阻塞支付回调
			if err := p.handlePaymentSuccess(notifyResult.OrderNo); err != nil {
				global.GVA_LOG.Error("处理支付成功业务逻辑失败",
					zap.String("orderNo", notifyResult.OrderNo),
					zap.Error(err))
			}
		}()
	}

	global.GVA_LOG.Info("处理支付通知成功",
		zap.String("orderNo", notifyResult.OrderNo),
		zap.Any("status", notifyResult.Status))

	return nil
}

// handlePaymentSuccess 处理支付成功后的业务逻辑
func (p *PaymentService) handlePaymentSuccess(orderNo string) error {
	// 检查是否有相关的产品订单项
	var count int64
	if err := global.GVA_DB.Table("order_items").Where("order_no = ?", orderNo).Count(&count).Error; err != nil {
		global.GVA_LOG.Error("检查订单项失败", zap.Error(err))
		return err
	}

	if count == 0 {
		// 没有相关的产品订单项，不需要处理
		global.GVA_LOG.Info("订单无相关产品，跳过处理", zap.String("orderNo", orderNo))
		return nil
	}

	// 有产品订单项，调用产品服务的 ProcessPaymentSuccess 方法
	global.GVA_LOG.Info("开始处理产品订单履约", zap.String("orderNo", orderNo))

	// 动态导入产品服务避免循环依赖
	// 使用反射或者通过接口的方式调用
	return p.callProductProcessPaymentSuccess(orderNo)
}

// callProductProcessPaymentSuccess 调用产品服务的 ProcessPaymentSuccess 方法
func (p *PaymentService) callProductProcessPaymentSuccess(orderNo string) error {
	// 使用事件系统来避免循环依赖
	// 发布支付成功事件，让产品服务监听并处理

	event := global.PaymentSuccessEvent{
		OrderNo: orderNo,
	}

	// 发布事件（同步处理）
	if err := global.GVA_EVENT_MANAGER.Publish(global.EventTypePaymentSuccess, event); err != nil {
		global.GVA_LOG.Error("发布支付成功事件失败",
			zap.String("orderNo", orderNo),
			zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("支付成功事件发布完成", zap.String("orderNo", orderNo))
	return nil
}

// RefundPayment 退款
func (p *PaymentService) RefundPayment(req paymentUtils.RefundRequest) error {
	// 查询原订单
	var order payment.PaymentOrder
	if err := global.GVA_DB.Where("order_no = ?", req.OrderNo).First(&order).Error; err != nil {
		return fmt.Errorf("原订单不存在: %v", err)
	}

	// 检查订单状态
	if order.Status != payment.PaymentStatusPaid {
		return fmt.Errorf("订单状态不允许退款")
	}

	// 检查退款金额
	if req.RefundAmount > order.Amount-order.RefundAmount {
		return fmt.Errorf("退款金额超过可退款金额")
	}

	// 创建退款记录
	refund := &payment.PaymentRefund{
		RefundNo:       generateRefundNo(),
		OrderNo:        req.OrderNo,
		PaymentOrderID: order.ID,
		RefundAmount:   req.RefundAmount,
		RefundReason:   req.RefundReason,
		Status:         payment.RefundStatusPending,
		Remark:         req.Remark,
	}

	if err := global.GVA_DB.Create(refund).Error; err != nil {
		return fmt.Errorf("创建退款记录失败: %v", err)
	}

	// 获取支付客户端
	paymentClient, err := p.getPaymentClient(order.PaymentMethod)
	if err != nil {
		return fmt.Errorf("获取支付客户端失败: %v", err)
	}

	// 调用第三方退款接口
	refundResult, err := paymentClient.RefundPayment(refund)
	if err != nil {
		// 更新退款状态为失败
		global.GVA_DB.Model(refund).Updates(map[string]interface{}{
			"status":         payment.RefundStatusFailed,
			"third_response": err.Error(),
		})
		return fmt.Errorf("第三方退款失败: %v", err)
	}

	// 更新退款记录
	global.GVA_DB.Model(refund).Updates(map[string]interface{}{
		"status":          refundResult.Status,
		"third_refund_no": refundResult.ThirdRefundNo,
		"refund_time":     &refundResult.RefundTime,
		"finish_time":     &refundResult.RefundTime,
	})

	// 更新原订单退款金额
	newRefundAmount := order.RefundAmount + req.RefundAmount
	var newStatus payment.PaymentStatus
	if newRefundAmount >= order.Amount {
		newStatus = payment.PaymentStatusRefunded
	} else {
		newStatus = payment.PaymentStatusPartialRefunded
	}

	global.GVA_DB.Model(&order).Updates(map[string]interface{}{
		"refund_amount": newRefundAmount,
		"status":        newStatus,
	})

	return nil
}

// getPaymentClient 获取支付客户端
func (p *PaymentService) getPaymentClient(paymentMethod payment.PaymentMethod) (paymentUtils.PaymentInterface, error) {
	switch paymentMethod {
	case payment.PaymentMethodWechat:
		if !global.GVA_CONFIG.Payment.Wechat.Enabled {
			return nil, fmt.Errorf("微信支付未启用")
		}

		wechatConfig := paymentUtils.WechatConfig{
			MpAppID:                    global.GVA_CONFIG.Payment.Wechat.MpAppID,
			OpenAppID:                  global.GVA_CONFIG.Payment.Wechat.OpenAppID,
			MchID:                      global.GVA_CONFIG.Payment.Wechat.MchID,
			MchCertificateSerialNumber: global.GVA_CONFIG.Payment.Wechat.MchCertificateSerialNumber,
			APIv3Key:                   global.GVA_CONFIG.Payment.Wechat.APIv3Key,
			PrivateKey:                 global.GVA_CONFIG.Payment.Wechat.PrivateKey,
			NotifyUrl:                  global.GVA_CONFIG.Payment.Wechat.NotifyUrl,
			H5Domain:                   global.GVA_CONFIG.Payment.Wechat.H5Domain,
			H5ReturnUrl:                global.GVA_CONFIG.Payment.Wechat.H5ReturnUrl,
		}
		return paymentUtils.NewWechatPayClient(wechatConfig)

	case payment.PaymentMethodAlipay:
		if !global.GVA_CONFIG.Payment.Alipay.Enabled {
			return nil, fmt.Errorf("支付宝支付未启用")
		}

		alipayConfig := paymentUtils.AlipayConfig{
			AppID:        global.GVA_CONFIG.Payment.Alipay.AppID,
			PrivateKey:   global.GVA_CONFIG.Payment.Alipay.PrivateKey,
			PublicKey:    global.GVA_CONFIG.Payment.Alipay.PublicKey,
			IsProduction: global.GVA_CONFIG.Payment.Alipay.IsProduction,
			NotifyUrl:    global.GVA_CONFIG.Payment.Alipay.NotifyUrl,
			ReturnUrl:    global.GVA_CONFIG.Payment.Alipay.ReturnUrl,
			SignType:     global.GVA_CONFIG.Payment.Alipay.SignType,
		}
		return paymentUtils.NewAlipayClient(alipayConfig)

	default:
		return nil, fmt.Errorf("不支持的支付方式: %s", paymentMethod)
	}
}

// generateRefundNo 生成退款单号
func generateRefundNo() string {
	return "REF" + time.Now().Format("20060102150405") + generateRandomString(6)
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// activateMembershipDirect 直接激活会员（避免循环依赖）
func (p *PaymentService) activateMembershipDirect(tx *gorm.DB, userID uint, orderNo string, item struct {
	ID             uint   `json:"id"`
	OrderNo        string `json:"orderNo"`
	ProductID      uint   `json:"productId"`
	ProductName    string `json:"productName"`
	ProductType    string `json:"productType"`
	UnitPrice      int64  `json:"unitPrice"`
	Quantity       int    `json:"quantity"`
	TotalPrice     int64  `json:"totalPrice"`
	MembershipType string `json:"membershipType"`
	Duration       int    `json:"duration"`
	DurationUnit   string `json:"durationUnit"`
	MonthlyPoints  int    `json:"monthlyPoints"`
	Status         int    `json:"status"`
}) error {
	// 计算会员时间
	startTime := time.Now()
	endTime := startTime.AddDate(0, 0, item.Duration)

	// 检查是否已有同类型的会员
	var existingMembership struct {
		ID       uint      `json:"id"`
		UserID   uint      `json:"userId"`
		Type     string    `json:"membershipType"`
		EndTime  time.Time `json:"endTime"`
		Duration int       `json:"duration"`
		Status   int       `json:"status"`
	}

	err := tx.Table("user_memberships").
		Where("user_id = ? AND membership_type = ? AND status = ?", userID, item.MembershipType, 1).
		First(&existingMembership).Error

	if err == nil {
		// 已有会员，延长时间
		endTime = existingMembership.EndTime.AddDate(0, 0, item.Duration)

		if err := tx.Table("user_memberships").Where("id = ?", existingMembership.ID).Updates(map[string]interface{}{
			"end_time":       endTime,
			"duration":       existingMembership.Duration + item.Duration,
			"monthly_points": item.MonthlyPoints,
		}).Error; err != nil {
			return fmt.Errorf("延长会员时间失败: %v", err)
		}
	} else if err == gorm.ErrRecordNotFound {
		// 创建新会员记录
		membership := map[string]interface{}{
			"user_id":         userID,
			"membership_type": item.MembershipType,
			"product_id":      item.ProductID,
			"order_no":        orderNo,
			"start_time":      startTime,
			"end_time":        endTime,
			"duration":        item.Duration,
			"monthly_points":  item.MonthlyPoints,
			"status":          1, // 激活状态
			"auto_renew":      false,
			"created_at":      time.Now(),
			"updated_at":      time.Now(),
		}

		if err := tx.Table("user_memberships").Create(membership).Error; err != nil {
			return fmt.Errorf("创建会员记录失败: %v", err)
		}
	} else {
		return fmt.Errorf("查询会员记录失败: %v", err)
	}

	// 更新用户VIP等级
	var newVipLevel int
	switch item.MembershipType {
	case "pro":
		newVipLevel = 1
	case "ultra":
		newVipLevel = 2
	}

	// 只有当新的VIP等级更高时才更新
	var user system.SysUser
	if err := tx.First(&user, userID).Error; err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	if newVipLevel > user.VipLevel {
		if err := tx.Model(&user).Update("vip_level", newVipLevel).Error; err != nil {
			return fmt.Errorf("更新用户VIP等级失败: %v", err)
		}
	}

	// 发放首次积分（按月计算）
	if item.MonthlyPoints > 0 {
		months := item.Duration / 30
		if months == 0 {
			months = 1 // 至少发放一个月的积分
		}

		totalPoints := item.MonthlyPoints * months

		// 增加用户积分
		if err := tx.Model(&user).UpdateColumn("points",
			gorm.Expr("points + ?", totalPoints)).Error; err != nil {
			return fmt.Errorf("发放积分失败: %v", err)
		}

		// 记录积分流水
		pointsRecord := map[string]interface{}{
			"user_id":    userID,
			"change":     totalPoints,
			"reason":     fmt.Sprintf("购买会员[%s]赠送积分", item.MembershipType),
			"type":       "order",
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}

		if err := tx.Table("sys_user_points").Create(pointsRecord).Error; err != nil {
			return fmt.Errorf("记录积分流水失败: %v", err)
		}
	}

	return nil
}

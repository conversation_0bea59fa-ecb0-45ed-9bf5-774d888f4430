package translate

import (
	"fmt"
	"math"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/basetypes"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/translate"
	"gorm.io/gorm"
)

type TranslateService struct{}

// BaiduTranslateWithPoints 百度翻译并扣除积分
func (s *TranslateService) BaiduTranslateWithPoints(userID uint, text string, from, to basetypes.Language) (*translate.BaiduTranslateResponse, error) {
	// 1. 调用翻译API
	result, err := translate.BaiduTranslate(text, string(from), string(to))
	if err != nil {
		return nil, err
	}

	// 2. 计算翻译结果的字符数和所需积分
	var totalChars int
	for _, transResult := range result.TransResult {
		totalChars += len([]rune(strings.TrimSpace(transResult.Dst)))
	}

	// 每200字符算1积分，不够的算1积分
	needPoints := int(math.Ceil(float64(totalChars) / 200.0))
	if needPoints == 0 {
		needPoints = 1 // 至少扣除1积分
	}

	// 3. 扣除用户积分
	if err := s.deductUserPoints(userID, needPoints, fmt.Sprintf("百度翻译消耗积分，翻译字符数：%d", totalChars)); err != nil {
		return nil, fmt.Errorf("积分扣除失败: %v", err)
	}

	return result, nil
}

// deductUserPoints 扣除用户积分
func (s *TranslateService) deductUserPoints(userID uint, points int, reason string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 获取用户信息并检查积分是否足够
		var user system.SysUser
		if err := tx.Where("id = ?", userID).First(&user).Error; err != nil {
			return fmt.Errorf("获取用户信息失败: %v", err)
		}

		// 检查总积分是否足够（免费积分 + 普通积分）
		totalPoints := user.FreePoints + user.Points
		if totalPoints < points {
			return fmt.Errorf("积分不足，需要 %d 积分，当前拥有 %d 积分", points, totalPoints)
		}

		// 2. 优先扣除免费积分
		var freePointsUsed, regularPointsUsed int
		if user.FreePoints >= points {
			// 免费积分足够
			freePointsUsed = points
			regularPointsUsed = 0
		} else {
			// 先用完免费积分，再用普通积分
			freePointsUsed = user.FreePoints
			regularPointsUsed = points - user.FreePoints
		}

		// 3. 更新用户积分
		if err := tx.Model(&user).Updates(map[string]interface{}{
			"free_points": user.FreePoints - freePointsUsed,
			"points":      user.Points - regularPointsUsed,
		}).Error; err != nil {
			return fmt.Errorf("更新用户积分失败: %v", err)
		}

		// 4. 记录积分流水
		pointsRecord := integral.SysUserPoints{
			UserID: userID,
			Change: -points, // 负数表示扣除
			Reason: reason,
			Type:   "translate",
		}
		if err := tx.Create(&pointsRecord).Error; err != nil {
			return fmt.Errorf("记录积分流水失败: %v", err)
		}

		return nil
	})
}

func (s *TranslateService) ListLanguages() []map[string]string {
	var langs []map[string]string
	for _, code := range basetypes.AllLanguageCodes {
		langs = append(langs, map[string]string{
			"code":  string(code),
			"cname": basetypes.LanguageNameMap[code],
			"ename": basetypes.LanguageEnameMap[code],
		})
	}
	return langs
}

package authing

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"

	"github.com/Authing/authing-go-sdk/lib/authentication"
	"github.com/Authing/authing-go-sdk/lib/model"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type AuthService struct{}

// getClient 初始化 Authing 认证客户端
func (authService *AuthService) getClient() *authentication.Client {
	config := global.GVA_CONFIG.Authing

	if config.AppId == "" || config.Secret == "" || config.UserPoolId == "" {
		global.GVA_LOG.Error("Authing configuration is missing. Please check your config file.")
		return nil
	}

	client := authentication.NewClient(config.AppId, config.Secret)
	client.UserPoolId = config.UserPoolId

	// 如果配置了自定义域名（私有化部署），则设置 Host
	if config.Host != "" {
		client.Host = config.Host
	}

	return client
}

// validateEmail 验证邮箱格式
func (authService *AuthService) validateEmail(email string) error {
	if email == "" {
		return errors.New("邮箱不能为空")
	}
	// 使用正则表达式验证邮箱格式
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	if !regexp.MustCompile(emailRegex).MatchString(email) {
		return errors.New("邮箱格式不正确")
	}
	return nil
}

// validatePassword 验证密码强度
func (authService *AuthService) validatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("密码长度至少为8位")
	}
	// 检查是否包含数字
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	// 检查是否包含字母
	hasLetter := regexp.MustCompile(`[a-zA-Z]`).MatchString(password)
	// 检查是否包含特殊字符
	hasSpecial := regexp.MustCompile(`[!@#$%^&*(),.?":{}|<>]`).MatchString(password)

	if !hasNumber || !hasLetter || !hasSpecial {
		return errors.New("密码必须包含数字、字母和特殊字符")
	}
	return nil
}

// RegisterByEmailInput 定义邮箱注册的输入结构体
type RegisterByEmailInput struct {
	Email       string    `json:"email" binding:"required"`
	Password    string    `json:"password" binding:"required"`
	Username    string    `json:"username"`
	NickName    string    `json:"nickName"`
	HeaderImg   string    `json:"headerImg"`
	Phone       string    `json:"phone"`
	AuthorityId uint      `json:"authorityId"`
	Enable      int       `json:"enable"`                           // 用户是否被冻结 1正常 2冻结
	Gender      string    `json:"gender"`                           // 性别，可选值为 M（男）、F（女）、U（未知）
	UUID        uuid.UUID `json:"uuid" gorm:"index;comment:用户UUID"` // 用户UUID
}

// RegisterByEmail 使用 Authing SDK 通过邮箱注册用户
func (authService *AuthService) RegisterByEmail(input RegisterByEmailInput) (*system.SysUser, error) {
	// 验证邮箱格式
	if err := authService.validateEmail(input.Email); err != nil {
		return nil, err
	}

	// 验证密码强度
	//if err := authService.validatePassword(input.Password); err != nil {
	//	return nil, err
	//}

	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	// 设置默认的 AuthorityId
	authorityId := 666
	var authorities []system.SysAuthority
	authorities = append(authorities, system.SysAuthority{
		AuthorityId: uint(authorityId),
	})

	// 设置默认性别为未知
	if input.Gender == "" {
		input.Gender = "U"
	}
	if input.Enable == 0 {
		input.Enable = 1 // 默认启用用户
	}
	if input.Password == "" {
		input.Password = "123456" // 设置默认密码
	}
	input.UUID = uuid.New()
	// 1. 检查邮箱是否已注册
	exists, err := client.IsUserExists(&model.IsUserExistsRequest{
		Email: &input.Email,
	})
	if err != nil {
		return nil, fmt.Errorf("check email existence failed: %w", err)
	}
	if exists != nil && *exists {
		return nil, errors.New("邮箱已被注册")
	}

	// 2. 在 Authing 中注册用户
	profile := &model.RegisterProfile{
		Gender: &input.Gender,
	}
	if input.Username != "" {
		profile.Username = &input.Username
	}
	if input.NickName != "" {
		profile.Nickname = &input.NickName
	}

	req := &model.RegisterByEmailInput{
		Email:    input.Email,
		Password: input.Password,
		Profile:  profile,
	}

	authingUser, err := client.RegisterByEmail(req)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing RegisterByEmail failed: %v", err))
		return nil, fmt.Errorf("authing registration failed: %w", err)
	}

	// 3. 创建系统用户
	sysUser := &system.SysUser{
		UUID:        input.UUID,
		Username:    input.Email,
		NickName:    input.Email,
		Password:    utils.BcryptHash(input.Password), // 本地密码也进行加密存储
		HeaderImg:   input.HeaderImg,
		AuthorityId: input.AuthorityId, // 使用系统角色ID
		Authorities: authorities,
		Enable:      input.Enable,
		Phone:       input.Phone,
		Email:       input.Email,
		AuthingId: func() *string {
			if authingUser.Id == "" {
				return nil
			}
			return &authingUser.Id
		}(), // 存储 Authing 用户 ID
	}

	// 4. 在本地数据库创建用户
	err = global.GVA_DB.Create(sysUser).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Local user creation failed: %v", err))

		// 本地创建失败时，删除 Authing 用户
		userIds := []string{authingUser.Id}
		if deleteErr := ManagementServiceApp.DeleteUsers(userIds); deleteErr != nil {
			global.GVA_LOG.Error(fmt.Sprintf("Failed to rollback Authing user: %v", deleteErr))
			// 记录错误但不返回，因为主要错误是本地创建失败
		}

		return nil, fmt.Errorf("local user creation failed: %w", err)
	}

	return sysUser, nil
}

// LoginByEmailInput 定义邮箱登录的输入结构体
type LoginByEmailInput struct {
	Email    string `json:"email" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginByEmail 使用 Authing SDK 通过邮箱登录用户
// 返回 Authing 的 UserInfo 类型，其中包含 token 等信息
func (authService *AuthService) LoginByEmail(input LoginByEmailInput) (*model.User, error) {
	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	loginInput := model.LoginByEmailInput{
		Email:    input.Email,
		Password: input.Password,
	}

	resp, err := client.LoginByEmail(loginInput)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing LoginByEmail failed: %v", err))
		// 可以尝试解析 Authing 返回的更具体的错误信息
		return nil, fmt.Errorf("authing login failed: %w", err)
	}

	// 登录成功，resp 中包含 token 等信息
	return resp, nil
}

// RegisterByPhoneInput 定义手机号注册的输入结构体
type RegisterByPhoneInput struct {
	Phone       string    `json:"phone" binding:"required"`
	Code        string    `json:"code" binding:"required"` // 验证码
	Password    string    `json:"password" binding:"required"`
	Username    string    `json:"username"`
	NickName    string    `json:"nickName"`
	HeaderImg   string    `json:"headerImg"`
	Email       string    `json:"email"`
	AuthorityId uint      `json:"authorityId"`
	Enable      int       `json:"enable"`                           // 用户是否被冻结 1正常 2冻结
	Gender      string    `json:"gender"`                           // 性别，可选值为 M（男）、F（女）、U（未知）
	UUID        uuid.UUID `json:"uuid" gorm:"index;comment:用户UUID"` // 用户UUID
}

// validatePhone 验证手机号格式
func (authService *AuthService) validatePhone(phone string) error {
	if phone == "" {
		return errors.New("手机号不能为空")
	}
	phoneRegex := `^1[3-9]\d{9}$`
	if !regexp.MustCompile(phoneRegex).MatchString(phone) {
		return errors.New("手机号格式不正确")
	}
	return nil
}

// RegisterByPhone 使用 Authing SDK 通过手机号注册用户
func (authService *AuthService) RegisterByPhone(input RegisterByPhoneInput) (*system.SysUser, error) {
	// 验证手机号格式
	if err := authService.validatePhone(input.Phone); err != nil {
		return nil, err
	}

	// 检查手机号是否已存在（本地系统）
	if !errors.Is(global.GVA_DB.Where("phone = ?", input.Phone).First(&system.SysUser{}).Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("手机号已被注册")
	}

	if !errors.Is(global.GVA_DB.Where("user_name = ?", input.Phone).First(&system.SysUser{}).Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("手机号已被注册")
	}

	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	// 设置默认的 AuthorityId
	authorityId := 666
	var authorities []system.SysAuthority
	authorities = append(authorities, system.SysAuthority{
		AuthorityId: uint(authorityId),
	})

	// 设置默认值
	if input.Gender == "" {
		input.Gender = "U"
	}
	if input.Enable == 0 {
		input.Enable = 1 // 默认启用用户
	}
	if input.Password == "" {
		input.Password = "123456" // 设置默认密码
	}
	input.UUID = uuid.New()

	// 1. 检查手机号是否已注册
	exists, err := client.IsUserExists(&model.IsUserExistsRequest{
		Phone: &input.Phone,
	})
	if err != nil {
		return nil, fmt.Errorf("check phone existence failed: %w", err)
	}
	if exists != nil && *exists {
		return nil, errors.New("手机号已被注册")
	}

	// 2. 在 Authing 中注册用户
	//	profile := &model.RegisterProfile{}
	//Gender: &input.Gender,
	// if input.Username != "" {
	// 	profile.Username = &input.Username
	// }
	// if input.NickName != "" {
	// 	profile.Nickname = &input.NickName
	// }

	forceLogin := true
	req := &model.RegisterByPhoneCodeInput{
		Phone:    input.Phone,
		Code:     input.Code,
		Password: &input.Password,
		//		Profile:  profile,
		ForceLogin: &forceLogin,
	}

	authingUser, err := client.RegisterByPhoneCode(req)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing RegisterByPhone failed: %v", err))
		return nil, fmt.Errorf("authing registration failed: %w", err)
	}

	// 3. 创建系统用户
	sysUser := &system.SysUser{
		UUID:        input.UUID,
		Username:    input.Phone, // 使用手机号作为用户名
		NickName:    input.Phone,
		Password:    utils.BcryptHash(input.Password), // 本地密码也进行加密存储
		HeaderImg:   input.HeaderImg,
		AuthorityId: input.AuthorityId,
		Authorities: authorities,
		Enable:      input.Enable,
		Phone:       input.Phone,
		Email:       input.Email,
		AuthingId: func() *string {
			if authingUser.Id == "" {
				return nil
			}
			return &authingUser.Id
		}(), // 存储 Authing 用户 ID
	}

	// 4. 在本地数据库创建用户
	err = global.GVA_DB.Create(sysUser).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Local user creation failed: %v", err))

		// 本地创建失败时，删除 Authing 用户
		userIds := []string{authingUser.Id}
		if deleteErr := ManagementServiceApp.DeleteUsers(userIds); deleteErr != nil {
			global.GVA_LOG.Error(fmt.Sprintf("Failed to rollback Authing user: %v", deleteErr))
			// 记录错误但不返回，因为主要错误是本地创建失败
		}

		return nil, fmt.Errorf("local user creation failed: %w", err)
	}

	return sysUser, nil
}

// LoginByPhonePasswordInput 定义手机号密码登录的输入结构体
type LoginByPhonePasswordInput struct {
	Phone    string `json:"phone" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginByPhonePassword 使用 Authing SDK 通过手机号密码登录用户
func (authService *AuthService) LoginByPhonePassword(input LoginByPhonePasswordInput) (*model.User, error) {
	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	loginInput := model.LoginByPhonePasswordInput{
		Phone:    input.Phone,
		Password: input.Password,
	}

	resp, err := client.LoginByPhonePassword(loginInput)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing LoginByPhonePassword failed: %v", err))
		return nil, fmt.Errorf("authing login failed: %w", err)
	}

	return resp, nil
}

// LoginByPhoneCodeInput 定义手机号验证码登录的输入结构体
type LoginByPhoneCodeInput struct {
	Phone string `json:"phone" binding:"required"`
	Code  string `json:"code" binding:"required"`
}

// LoginByPhoneCode 使用 Authing SDK 通过手机号验证码登录用户
func (authService *AuthService) LoginByPhoneCode(input LoginByPhoneCodeInput) (*model.User, error) {
	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	loginInput := &model.LoginByPhoneCodeInput{
		Phone: input.Phone,
		Code:  input.Code,
	}

	resp, err := client.LoginByPhoneCode(loginInput)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing LoginByPhoneCode failed: %v", err))
		return nil, fmt.Errorf("authing login failed: %w", err)
	}

	return resp, nil
}

// SendSmsCode 发送短信验证码
func (authService *AuthService) SendSmsCode(phone string) error {
	client := authService.getClient()
	if client == nil {
		return errors.New("authing client initialization failed, check configuration")
	}

	_, err := client.SendSmsCode(phone)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing SendSmsCode failed: %v", err))
		return fmt.Errorf("send sms code failed: %w", err)
	}

	return nil
}

// RegisterByUsernameInput 定义用户名注册的输入结构体
type RegisterByUsernameInput struct {
	Username  string `json:"username" binding:"required"`
	Password  string `json:"password" binding:"required"`
	Captcha   string `json:"captcha"`   // 验证码
	CaptchaId string `json:"captchaId"` // 验证码ID
}

// RegisterByUsername 使用 Authing SDK 通过用户名注册用户
func (authService *AuthService) RegisterByUsername(input RegisterByUsernameInput) (*system.SysUser, error) {

	// 验证手机号格式
	if err := authService.validatePhone(input.Username); err != nil {
		return nil, err
	}

	// 检查用户名是否已存在（本地系统）
	if !errors.Is(global.GVA_DB.Where("username = ?", input.Username).First(&system.SysUser{}).Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("手机号已被注册")
	}

	if !errors.Is(global.GVA_DB.Where("phone = ?", input.Username).First(&system.SysUser{}).Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("手机号已被注册")
	}

	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	// 设置默认的 AuthorityId
	authorityId := 666
	var authorities []system.SysAuthority
	authorities = append(authorities, system.SysAuthority{
		AuthorityId: uint(authorityId),
	})

	// 设置默认值
	if input.Password == "" {
		input.Password = "123456" // 设置默认密码
	}
	uuid := uuid.New()

	// 1. 检查用户名是否已注册
	exists, err := client.IsUserExists(&model.IsUserExistsRequest{
		Username: &input.Username,
		Phone:    &input.Username,
	})
	if err != nil {
		return nil, fmt.Errorf("check username existence failed: %w", err)
	}
	if exists != nil && *exists {
		return nil, errors.New("手机号已被注册")
	}

	forceLogin := true

	req := &model.RegisterByUsernameInput{
		Username:   input.Username,
		Password:   input.Password,
		ForceLogin: &forceLogin,
	}

	authingUser, err := client.RegisterByUsername(req)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing RegisterByUsername failed: %v", err))
		return nil, fmt.Errorf("authing registration failed: %w", err)
	}

	// 3. 创建系统用户
	sysUser := &system.SysUser{
		UUID:        uuid,
		Username:    input.Username,
		NickName:    input.Username,
		Phone:       input.Username,
		Password:    utils.BcryptHash(input.Password), // 本地密码也进行加密存储
		AuthorityId: uint(authorityId),
		Authorities: authorities,
		AuthingId: func() *string {
			if authingUser.Id == "" {
				return nil
			}
			return &authingUser.Id
		}(), // 存储 Authing 用户 ID
	}

	// 4. 在本地数据库创建用户
	err = global.GVA_DB.Create(sysUser).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Local user creation failed: %v", err))

		// 本地创建失败时，删除 Authing 用户
		userIds := []string{authingUser.Id}
		if deleteErr := ManagementServiceApp.DeleteUsers(userIds); deleteErr != nil {
			global.GVA_LOG.Error(fmt.Sprintf("Failed to rollback Authing user: %v", deleteErr))
			// 记录错误但不返回，因为主要错误是本地创建失败
		}

		return nil, fmt.Errorf("local user creation failed: %w", err)
	}

	return sysUser, nil
}

// LoginByUsernameInput 定义用户名登录的输入结构体
type LoginByUsernameInput struct {
	Username  string `json:"username" binding:"required"`
	Password  string `json:"password" binding:"required"`
	Captcha   string `json:"captcha"`   // 验证码
	CaptchaId string `json:"captchaId"` // 验证码ID
}

// LoginByUsername 使用 Authing SDK 通过用户名登录用户
func (authService *AuthService) LoginByUsername(input LoginByUsernameInput) (*model.User, error) {
	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	loginInput := model.LoginByUsernameInput{
		Username: input.Username,
		Password: input.Password,
	}

	resp, err := client.LoginByUserName(loginInput)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Authing LoginByUsername failed: %v", err))
		return nil, fmt.Errorf("authing login failed: %w", err)
	}

	return resp, nil
}

// TokenResponse 定义 Token 响应结构体
type TokenResponse struct {
	AccessToken string `json:"access_token"`
	IdToken     string `json:"id_token"`
	ExpiresIn   int    `json:"expires_in"`
	TokenType   string `json:"token_type"`
	Scope       string `json:"scope"`
}

type AuthingErrorResponse struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}

// CodeToToken 使用 Code 换取 Token
func (authService *AuthService) CodeToToken(code string) (*TokenResponse, error) {
	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	// 使用 Code 换取 Token
	tokenRespString, err := client.GetAccessTokenByCode(code)
	if err != nil {
		return nil, fmt.Errorf("exchange code for token failed: %w", err)
	}

	// 首先尝试解析错误响应
	var errorResp AuthingErrorResponse
	if err := json.Unmarshal([]byte(tokenRespString), &errorResp); err == nil {
		if errorResp.Error != "" {
			return nil, fmt.Errorf("%s: %s", errorResp.Error, errorResp.ErrorDescription)
		}
	}

	// 解析成功响应
	var tokenResp TokenResponse
	if err := json.Unmarshal([]byte(tokenRespString), &tokenResp); err != nil {
		return nil, fmt.Errorf("parse token response failed: %w", err)
	}

	// 验证响应中是否包含必要的token
	if tokenResp.AccessToken == "" {
		return nil, errors.New("access token is empty in response")
	}

	return &TokenResponse{
		AccessToken: tokenResp.AccessToken,
		IdToken:     tokenResp.IdToken,
		ExpiresIn:   tokenResp.ExpiresIn,
		TokenType:   tokenResp.TokenType,
	}, nil
}

// GetUserInfoByToken 使用 Access Token 获取用户信息
func (authService *AuthService) GetUserInfoByToken(idToken string) (*model.User, error) {
	client := authService.getClient()
	if client == nil {
		return nil, errors.New("authing client initialization failed, check configuration")
	}

	// 获取用户信息
	userInfo, err := client.GetCurrentUser(&idToken)
	if err != nil {
		return nil, fmt.Errorf("get user info failed: %w", err)
	}
	return userInfo, nil
}

// HandleAuthingCallback 处理 Authing 回调，包括 code 换 token 和获取用户信息
func (authService *AuthService) HandleAuthingCallback(code string) (*system.SysUser, error) {
	// 1. 使用 code 换取 token
	tokenResp, err := authService.CodeToToken(code)
	if err != nil {
		return nil, fmt.Errorf("code to token failed: %w", err)
	}

	// 2. 使用 id_token 获取用户信息
	userInfo, err := authService.GetUserInfoByToken(tokenResp.IdToken)
	if err != nil {
		return nil, fmt.Errorf("get user info failed: %w", err)
	}

	//mcpcn_wechat_openid 和 mcpcn_wechat_unionid
	mcpcnWechatOpenid := ""
	mcpcnWechatUnionid := ""
	for _, identity := range userInfo.Identities {
		provider := utils.PtrToString(identity.Provider)
		if provider == "" || provider != "wechat" {
			continue
		}

		if *identity.Type == "openid" {
			mcpcnWechatOpenid = *identity.UserIdInIdp
		} else if *identity.Type == "unionid" {
			mcpcnWechatUnionid = *identity.UserIdInIdp
		}
	}

	// 3. 检查用户是否已存在于本地数据库
	var sysUser system.SysUser
	err = global.GVA_DB.Where("authing_id <> '' AND authing_id = ?", userInfo.Id).First(&sysUser).Error
	if err == nil {
		//如果mcpcn_openid 和 mcpcn_unionid 不为空，则更新用户信息
		UpdateFlag := false
		if mcpcnWechatOpenid != "" {
			sysUser.McpcnWechatOpenid = mcpcnWechatOpenid
			UpdateFlag = true
		}
		if mcpcnWechatUnionid != "" {
			sysUser.McpcnWechatUnionid = mcpcnWechatUnionid
			UpdateFlag = true
		}
		if UpdateFlag {
			err = global.GVA_DB.Save(&sysUser).Error
		}
		return &sysUser, nil
	} else if err == gorm.ErrRecordNotFound {

		phone := utils.PtrToString(userInfo.Phone)
		email := utils.PtrToString(userInfo.Email)
		username := utils.PtrToString(userInfo.Username)
		if phone == "" && email != "" && username == "" {
			username = email
		} else if phone != "" && email == "" && username == "" {
			username = phone
		}
		// 用户不存在，创建新用户
		sysUser = system.SysUser{
			UUID:        uuid.New(),
			Username:    username,
			NickName:    utils.PtrToString(userInfo.Nickname),
			HeaderImg:   utils.PtrToString(userInfo.Photo),
			Password:    utils.BcryptHash(global.GVA_CONFIG.System.DefaultPassword),
			AuthorityId: 666, // 设置默认角色
			Enable:      1,   // 默认启用
			Phone:       phone,
			Email:       email,
			AuthingId: func() *string {
				if userInfo.Id == "" {
					return nil
				}
				return &userInfo.Id
			}(), // 存储 Authing 用户 ID
			McpcnWechatOpenid:  mcpcnWechatOpenid,
			McpcnWechatUnionid: mcpcnWechatUnionid,
		}

		// 设置默认的权限
		var authorities []system.SysAuthority
		authorities = append(authorities, system.SysAuthority{
			AuthorityId: 666,
		})
		sysUser.Authorities = authorities

		// 创建用户
		err = global.GVA_DB.Create(&sysUser).Error
		if err != nil {
			return nil, fmt.Errorf("create local user failed: %w", err)
		}
		fmt.Println("sysUser.ID:", sysUser.ID)
		return &sysUser, nil
	} else {
		// 其它数据库错误
		return nil, fmt.Errorf("query local user failed: %w", err)
	}
}

// 嵌入登录组件获取的userInfo传给后端
func (authService *AuthService) LoginByUserInfo(token string) (*system.SysUser, error) {

	userInfo, err := authService.GetUserInfoByToken(token)
	if err != nil {
		return nil, fmt.Errorf("get user info failed: %w", err)
	}

	//mcpcn_wechat_openid 和 mcpcn_wechat_unionid
	mcpcnWechatOpenid := ""
	mcpcnWechatUnionid := ""
	for _, identity := range userInfo.Identities {
		//identity.Provider不等于空并且等wechat
		provider := utils.PtrToString(identity.Provider)
		if provider == "" || provider != "wechat" {
			continue
		}

		if *identity.Type == "openid" && provider == "mcpcn" {
			mcpcnWechatOpenid = *identity.UserIdInIdp
		} else if *identity.Type == "unionid" && provider == "mcpcn" {
			mcpcnWechatUnionid = *identity.UserIdInIdp
		}
	}

	// 检查用户是否已存在于本地数据库
	var sysUser system.SysUser
	err = global.GVA_DB.Where("authing_id <> '' AND authing_id = ?", userInfo.Id).First(&sysUser).Error
	if err == nil {
		//如果mcpcn_openid 和 mcpcn_unionid 不为空，则更新用户信息
		UpdateFlag := false
		if mcpcnWechatOpenid != "" {
			sysUser.McpcnWechatOpenid = mcpcnWechatOpenid
			UpdateFlag = true
		}
		if mcpcnWechatUnionid != "" {
			sysUser.McpcnWechatUnionid = mcpcnWechatUnionid
			UpdateFlag = true
		}
		if UpdateFlag {
			err = global.GVA_DB.Save(&sysUser).Error
			if err != nil {
				//如果更新失败，打印不报错，
				global.GVA_LOG.Error(fmt.Sprintf("update user info failed: %v", err))
			}
		}
		return &sysUser, nil
	} else if err == gorm.ErrRecordNotFound {

		phone := utils.PtrToString(userInfo.Phone)
		email := utils.PtrToString(userInfo.Email)
		username := utils.PtrToString(userInfo.Username)
		if phone == "" && email != "" && username == "" {
			username = email
		} else if phone != "" && email == "" && username == "" {
			username = phone
		}
		// 用户不存在，创建新用户
		sysUser = system.SysUser{
			UUID:        uuid.New(),
			Username:    username,
			NickName:    utils.PtrToString(userInfo.Nickname),
			HeaderImg:   utils.PtrToString(userInfo.Photo),
			Password:    utils.BcryptHash(global.GVA_CONFIG.System.DefaultPassword),
			AuthorityId: 666, // 设置默认角色
			Enable:      1,   // 默认启用
			Phone:       phone,
			Email:       email,
			AuthingId: func() *string {
				if userInfo.Id == "" {
					return nil
				}
				return &userInfo.Id
			}(), // 存储 Authing 用户 ID
			McpcnWechatOpenid:  mcpcnWechatOpenid,
			McpcnWechatUnionid: mcpcnWechatUnionid,
		}

		// 设置默认的权限
		var authorities []system.SysAuthority
		authorities = append(authorities, system.SysAuthority{
			AuthorityId: 666,
		})
		sysUser.Authorities = authorities

		// 创建用户
		err = global.GVA_DB.Create(&sysUser).Error
		if err != nil {
			return nil, fmt.Errorf("create local user failed: %w", err)
		}
		fmt.Println("sysUser.ID:", sysUser.ID)
		return &sysUser, nil
	} else {
		// 其它数据库错误
		return nil, fmt.Errorf("query local user failed: %w", err)
	}
}

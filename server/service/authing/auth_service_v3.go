package authing

import (
	"context"
	"errors"
	"fmt"
	"regexp"

	"github.com/Authing/authing-golang-sdk/v3/authentication"
	"github.com/Authing/authing-golang-sdk/v3/dto"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	authingReq "github.com/flipped-aurora/gin-vue-admin/server/model/authing/request"
	systemModel "github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type AuthV3Service struct{}

var AuthV3ServiceApp = new(AuthV3Service)

// getClient 初始化 Authing 认证客户端
func (authService *AuthV3Service) getClient() (*authentication.AuthenticationClient, error) {
	config := global.GVA_CONFIG.Authing

	if config.AppId == "" || config.Secret == "" {
		return nil, errors.New("Authing configuration is missing. Please check your config file.")
	}

	options := &authentication.AuthenticationClientOptions{
		AppId:       config.AppId,
		AppSecret:   config.Secret,
		AppHost:     config.Host,
		RedirectUri: config.RedirectUri,
	}

	client, err := authentication.NewAuthenticationClient(options)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to create Authing client: %v", err))
		return nil, err
	}

	return client, nil
}

// validateEmail 验证邮箱格式
func (authService *AuthV3Service) validateEmail(email string) error {
	if email == "" {
		return errors.New("邮箱不能为空")
	}
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	if !regexp.MustCompile(emailRegex).MatchString(email) {
		return errors.New("邮箱格式不正确")
	}
	return nil
}

// validatePassword 验证密码强度
func (authService *AuthV3Service) validatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("密码长度至少为8位")
	}
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	hasLetter := regexp.MustCompile(`[a-zA-Z]`).MatchString(password)
	hasSpecial := regexp.MustCompile(`[!@#$%^&*(),.?":{}|<>]`).MatchString(password)

	if !hasNumber || !hasLetter || !hasSpecial {
		return errors.New("密码必须包含数字、字母和特殊字符")
	}
	return nil
}

// RegisterInput 定义统一注册的输入结构体
type RegisterInput struct {
	Connection  string    `json:"connection" binding:"required"`    // 注册方式：PASSWORD 或 PASSCODE
	Email       string    `json:"email"`                            // 邮箱
	Phone       string    `json:"phone"`                            // 手机号
	Password    string    `json:"password"`                         // 密码
	Code        string    `json:"code"`                             // 验证码
	Username    string    `json:"username"`                         // 用户名
	NickName    string    `json:"nickName"`                         // 昵称
	HeaderImg   string    `json:"headerImg"`                        // 头像
	AuthorityId uint      `json:"authorityId"`                      // 权限ID
	Enable      int       `json:"enable"`                           // 用户状态：1正常 2冻结
	Gender      string    `json:"gender"`                           // 性别：M（男）、F（女）、U（未知）
	UUID        uuid.UUID `json:"uuid" gorm:"index;comment:用户UUID"` // 用户UUID
}

// Register 统一注册方法
func (authService *AuthV3Service) Register(input RegisterInput) (userInter *systemModel.SysUser, err error) {

	// 检查用户名是否已存在（本地系统）
	if !errors.Is(global.GVA_DB.Where("username = ?", input.Username).First(&systemModel.SysUser{}).Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在（本地系统）
	if !errors.Is(global.GVA_DB.Where("email = ?", input.Email).First(&systemModel.SysUser{}).Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("邮箱已被注册")
	}

	client, err := authService.getClient()
	if err != nil {
		return nil, err
	}

	// 设置默认值
	if input.Gender == "" {
		input.Gender = "U"
	}
	if input.Enable == 0 {
		input.Enable = 1
	}
	if input.UUID == uuid.Nil {
		input.UUID = uuid.New()
	}

	// 构建注册请求
	reqDto := &dto.SignUpDto{
		Connection: input.Connection,
		Profile: dto.SignUpProfileDto{
			Nickname: input.NickName,
			Gender:   input.Gender,
		},
	}

	// 根据注册方式设置payload
	switch input.Connection {
	case "PASSWORD":
		if input.Email != "" {
			if err := authService.validateEmail(input.Email); err != nil {
				return nil, err
			}
			reqDto.PasswordPayload = dto.SignUpByPasswordDto{
				Email:    input.Email,
				Password: input.Password,
			}
		} else if input.Phone != "" {
			reqDto.PasswordPayload = dto.SignUpByPasswordDto{
				Username: input.Phone, // 使用手机号作为用户名
				Password: input.Password,
			}
		} else {
			return nil, errors.New("注册方式错误：密码注册必须提供邮箱或手机号")
		}

	case "PASSCODE":
		if input.Phone == "" {
			return nil, errors.New("注册方式错误：验证码注册必须提供手机号")
		}
		if input.Code == "" {
			return nil, errors.New("注册方式错误：验证码不能为空")
		}
		reqDto.PassCodePayload = dto.SignUpByPassCodeDto{
			Phone:    input.Phone,
			PassCode: input.Code,
		}

	default:
		return nil, errors.New("不支持的注册方式")
	}

	// 调用 Authing SDK 注册
	respDto := client.SignUp(reqDto)
	if respDto.StatusCode != 200 {
		return nil, fmt.Errorf("authing registration failed: %s", respDto.Message)
	}

	// 创建系统用户
	sysUser := &systemModel.SysUser{
		UUID:        input.UUID,
		Username:    respDto.Data.Username,
		NickName:    input.NickName,
		Password:    utils.BcryptHash(input.Password),
		HeaderImg:   input.HeaderImg,
		AuthorityId: input.AuthorityId,
		Enable:      input.Enable,
		Phone:       input.Phone,
		Email:       input.Email,
		AuthingId:   &respDto.Data.UserId,
	}

	// 设置默认的 AuthorityId
	if sysUser.AuthorityId == 0 {
		sysUser.AuthorityId = 666
	}
	var authorities []systemModel.SysAuthority
	authorities = append(authorities, systemModel.SysAuthority{
		AuthorityId: sysUser.AuthorityId,
	})
	sysUser.Authorities = authorities

	// 在本地数据库创建用户
	err = global.GVA_DB.Create(sysUser).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Local user creation failed: %v", err))
		return nil, fmt.Errorf("local user creation failed: %w", err)
	}

	return sysUser, nil
}

// UpdateProfile 更新用户资料
func (authService *AuthV3Service) UpdateProfile(input systemReq.ChangeUserInfo) error {
	client, err := authService.getClient()
	if err != nil {
		return err
	}

	authingId := input.AuthingId
	if authingId == "" {
		return nil
	}
	authingToken, _ := global.GVA_REDIS.Get(context.Background(), "authing_token:"+authingId).Result()

	client.SetAccessToken(authingToken)
	var updateFlag bool = false
	var updateDto dto.UpdateUserProfileDto
	if input.NickName != "" {
		updateDto.Nickname = input.NickName
		updateFlag = true
	}
	if input.HeaderImg != "" {
		updateDto.Photo = input.HeaderImg
		updateFlag = true
	}
	if updateFlag {
		resp := client.UpdateProfile(&updateDto)
		if resp.StatusCode != 200 {
			return fmt.Errorf("authing update profile failed: %s", resp.Message)
		}
	}

	return nil
}

// BindPhone 绑定手机号
func (authService *AuthV3Service) BindPhone(authingId, phone, code string) error {
	client, err := authService.getClient()
	if err != nil {
		return err
	}
	if authingId == "" {
		return errors.New("authingId不能为空")
	}
	authingToken, _ := global.GVA_REDIS.Get(context.Background(), "authing_token:"+authingId).Result()
	client.SetAccessToken(authingToken)

	bindPhoneDto := &dto.BindPhoneDto{
		PhoneNumber: phone,
		PassCode:    code,
	}
	resp := client.BindPhone(bindPhoneDto)
	if resp.StatusCode != 200 {
		return fmt.Errorf("操作失败: %s", resp.Message)
	}
	// 更新本地用户手机号
	err = global.GVA_DB.Model(&systemModel.SysUser{}).Where("authing_id = ?", authingId).Update("phone", phone).Error
	if err != nil {
		return fmt.Errorf("更新失败: %w", err)
	}
	return nil
}

// UpdatePassword 修改密码
func (authService *AuthV3Service) UpdatePassword(input systemReq.ChangePasswordReq) error {
	client, err := authService.getClient()
	if err != nil {
		return err
	}
	if input.AuthingId == "" {
		return errors.New("authingId不能为空")
	}
	authingToken, _ := global.GVA_REDIS.Get(context.Background(), "authing_token:"+input.AuthingId).Result()
	client.SetAccessToken(authingToken)

	updatePasswordDto := &dto.UpdatePasswordDto{
		NewPassword: input.NewPassword,
	}
	if input.Password != "" {
		updatePasswordDto.OldPassword = input.Password
	}
	resp := client.UpdatePassword(updatePasswordDto)
	if resp.StatusCode != 200 {
		return fmt.Errorf("操作失败: %s", resp.Message)
	}

	// 更新本地用户密码
	_ = global.GVA_DB.Model(&systemModel.SysUser{}).Where("id = ?", input.ID).Update("password", utils.BcryptHash(input.NewPassword)).Error
	return nil
}

// VerifyUpdatePhoneRequest 验证更新手机号请求
func (authService *AuthV3Service) VerifyUpdatePhoneRequest(input authingReq.VerifyUpdatePhoneRequest) (string, error) {
	client, err := authService.getClient()
	if err != nil {
		return "", err
	}
	authingToken, _ := global.GVA_REDIS.Get(context.Background(), "authing_token:"+input.AuthingId).Result()
	client.SetAccessToken(authingToken)
	reqDto := &dto.VerifyUpdatePhoneRequestDto{
		PhonePassCodePayload: dto.UpdatePhoneByPhonePassCodeDto{
			NewPhoneNumber:   input.Phone,
			NewPhonePassCode: input.Code,
		},
		VerifyMethod: "PHONE_PASSCODE",
	}
	resp := client.VerifyUpdatePhoneRequest(reqDto)
	if resp.StatusCode != 200 {
		return "", fmt.Errorf("操作失败: %s", resp.Message)
	}
	// 将更新手机号token写入redis
	global.GVA_REDIS.Set(context.Background(), "updatePhoneToken:"+input.AuthingId, resp.Data.UpdatePhoneToken, 0)
	return resp.Data.UpdatePhoneToken, nil
}

// UpdatePhone 更新手机号
func (authService *AuthV3Service) UpdatePhone(authingId, updatePhoneToken string) error {
	client, err := authService.getClient()
	if err != nil {
		return err
	}
	authingToken, _ := global.GVA_REDIS.Get(context.Background(), "authing_token:"+authingId).Result()
	client.SetAccessToken(authingToken)

	if updatePhoneToken == "" {
		updatePhoneToken, _ = global.GVA_REDIS.Get(context.Background(), "updatePhoneToken:"+authingId).Result()
	}
	updatePhoneDto := &dto.UpdatePhoneDto{
		UpdatePhoneToken: updatePhoneToken,
	}
	resp := client.UpdatePhone(updatePhoneDto)
	if resp.StatusCode != 200 {
		return fmt.Errorf("操作失败: %s", resp.Message)
	}
	return nil
}

package authing

import (
	"fmt"

	"github.com/Authing/authing-go-sdk/lib/management"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type ManagementService struct{}

var ManagementServiceApp = new(ManagementService)

// getClient 初始化 Authing 管理模块
func (managementService *ManagementService) getClient() *management.Client {
	config := global.GVA_CONFIG.Authing

	if &config == nil {
		global.GVA_LOG.Error("Authing config is nil. Please check your config initialization.")
		return nil
	}

	if config.AppId == "" || config.Secret == "" || config.UserPoolId == "" {
		global.GVA_LOG.Error("Authing configuration is missing. Please check your config file.")
		return nil
	}

	// 使用管理模块
	client := management.NewClient(config.UserPoolId, config.UserPoolSecret)
	if client == nil {
		global.GVA_LOG.Error(fmt.Sprintf("management.NewClient returned nil"))
		return nil
	}
	if config.Host != "" {
		client.Host = config.Host
	}
	return client
}

// DeleteUsers 批量删除用户
func (managementService *ManagementService) DeleteUsers(userIds []string) error {
	client := managementService.getClient()
	if client == nil {
		global.GVA_LOG.Error("DeleteUsers: Authing client is nil, skip deleting users")
		return nil // 只记录日志，不返回错误
	}

	// 批量删除用户
	_, err := client.BatchDeleteUser(userIds)
	if err != nil {
		return err
	}

	return nil
}

// Logout 使用 Authing 服务退出登录
// @param authingId string 用户authingId
func (managementService *ManagementService) Logout(authingId string) error {
	client := managementService.getClient()
	if client == nil {
		global.GVA_LOG.Error("Logout: Authing client is nil, skip logout")
		return nil // 只记录日志，不返回错误
	}

	_, err := client.LogOut(authingId, nil)
	if err != nil {
		return err
	}

	return nil
}

package mcp

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type ApiKeyService struct{}

var ApiKeyServiceApp = new(ApiKeyService)

// CreateApiKey 创建API密钥
func (s *ApiKeyService) CreateApiKey(name string, c *gin.Context) (*mcp.ApiKey, error) {
	userId := utils.GetUserID(c)
	apiKey := &mcp.ApiKey{
		Name:   name,
		ApiKey: uuid.New().String(),
		UserId: userId,
		Status: mcp.ApiKeyStatusActive,
	}
	err := global.GVA_DB.Create(apiKey).Error
	return apiKey, err
}

// DeleteApiKey 删除API密钥
func (s *ApiKeyService) DeleteApiKey(id uint) error {
	var apiKey mcp.ApiKey
	if err := global.GVA_DB.First(&apiKey, id).Error; err != nil {
		return err
	}
	return global.GVA_DB.Delete(&apiKey).Error
}

// GetApiKeys 获取API密钥列表
func (s *ApiKeyService) GetApiKeys(info request.PageInfo, userId uint) (list []mcp.ApiKey, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&mcp.ApiKey{})

	// 只查询当前用户的API密钥
	db = db.Where("user_id = ?", userId)

	// 如果有关键字搜索
	if info.Keyword != "" {
		db = db.Where("name LIKE ?", "%"+info.Keyword+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Find(&list).Error
	return list, total, err
}

// FindApiKeyByKey 根据apiKey查找并校验状态
func (s *ApiKeyService) FindApiKeyByKey(apiKey string) (*mcp.ApiKey, error) {
	return mcp.FindApiKeyByKey(apiKey)
}

// 校验apiKey是否有效（状态为active且未过期）
func (s *ApiKeyService) ValidateApiKey(apiKey string) (*mcp.ApiKey, error) {
	k, err := mcp.FindApiKeyByKey(apiKey)
	if err != nil {
		return nil, fmt.Errorf("invalid api key: %v", err)
	}
	if k == nil {
		return nil, fmt.Errorf("api key not found")
	}
	if k.Status != mcp.ApiKeyStatusActive {
		return nil, fmt.Errorf("api key is not active")
	}
	if k.ExpiresAt != nil && k.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("api key has expired")
	}
	return k, nil
}

// 查找最新一个API密钥，如果没有则创建一个
func (s *ApiKeyService) FindLatestApiKey(userId uint) (*mcp.ApiKey, error) {
	var apiKey mcp.ApiKey
	if err := global.GVA_DB.Where("user_id = ?", userId).Order("created_at DESC").First(&apiKey).Error; err != nil {
		// 如果没有找到，则创建一个新的API密钥
		if err.Error() == "record not found" {
			newApiKey := &mcp.ApiKey{
				Name:      "api_key",
				ApiKey:    uuid.New().String(),
				UserId:    userId,
				Status:    mcp.ApiKeyStatusActive,
				ExpiresAt: nil,
			}
			if createErr := global.GVA_DB.Create(newApiKey).Error; createErr != nil {
				return nil, createErr
			}
			return newApiKey, nil
		}
		return nil, err
	}
	return &apiKey, nil
}

package mcp

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"gorm.io/gorm"
)

type McpClientService struct{}

// CreateMcpClient 创建mcp客户端记录
func (mcpClientService *McpClientService) CreateMcpClient(ctx context.Context, mcpClient *mcp.McpClient) (err error) {
	err = global.GVA_DB.Create(mcpClient).Error
	return err
}

// DeleteMcpClient 删除mcp客户端记录
func (mcpClientService *McpClientService) DeleteMcpClient(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&mcp.McpClient{}, "id = ?", ID).Error
	return err
}

// DeleteMcpClientByIds 批量删除mcp客户端记录
func (mcpClientService *McpClientService) DeleteMcpClientByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]mcp.McpClient{}, "id in ?", IDs).Error
	return err
}

// UpdateMcpClient 更新mcp客户端记录
func (mcpClientService *McpClientService) UpdateMcpClient(ctx context.Context, mcpClient mcp.McpClient) (err error) {
	err = global.GVA_DB.Model(&mcp.McpClient{}).Where("id = ?", mcpClient.ID).Updates(&mcpClient).Error
	return err
}

// GetMcpClient 根据ID获取mcp客户端记录
func (mcpClientService *McpClientService) GetMcpClient(ctx context.Context, ID string) (mcpClient mcp.McpClient, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&mcpClient).Error
	return
}

// GetMcpClientInfoList 分页获取mcp客户端记录
func (mcpClientService *McpClientService) GetMcpClientInfoList(ctx context.Context, info mcpReq.McpClientSearch) (list []mcp.McpClient, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&mcp.McpClient{})
	var mcpClients []mcp.McpClient
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}
	if info.Developer != nil && *info.Developer != "" {
		db = db.Where("developer LIKE ?", "%"+*info.Developer+"%")
	}
	if info.Category != nil && *info.Category != "" {
		db = db.Where("category = ?", *info.Category)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&mcpClients).Error
	return mcpClients, total, err
}

// UpdateMcpClientDownloadCount 更新mcp客户端下载次数
func (mcpClientService *McpClientService) UpdateMcpClientDownloadCount(ctx context.Context, ID uint) (err error) {
	err = global.GVA_DB.Model(&mcp.McpClient{}).Where("id = ?", ID).Update("download_count", gorm.Expr("download_count + 1")).Error
	return err
}

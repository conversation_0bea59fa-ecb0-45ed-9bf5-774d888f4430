package mcp

import (
	"encoding/json"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"go.uber.org/zap"
)

// ProjectMessageHandler 项目消息处理器
type ProjectMessageHandler struct{}

// NewProjectMessageHandler 创建项目消息处理器
func NewProjectMessageHandler() *ProjectMessageHandler {
	return &ProjectMessageHandler{}
}

// HandleProjectUpdateMessage 处理项目更新消息
func (h *ProjectMessageHandler) HandleProjectUpdateMessage(message string) error {
	var event global.ProjectUpdateEvent
	if err := json.Unmarshal([]byte(message), &event); err != nil {
		global.GVA_LOG.Error("解析项目更新消息失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("处理项目更新消息",
		zap.String("projectUUID", event.ProjectUUID),
		zap.String("callMethod", event.CallMethod),
		zap.String("serverName", event.ServerName))

	// 托管服务类型，自动 kill 相关 server 进程（server_key=UUID）
	if strings.Contains(event.CallMethod, "online") {
		go func(serverKey string) {
			global.GVA_LOG.Info("开始查找并终止服务进程",
				zap.String("serverKey", serverKey),
				zap.String("serverName", event.ServerName))

			pids, err := utils.FindProcessPIDsByServerKey(serverKey)
			if err != nil {
				global.GVA_LOG.Error("查找进程失败",
					zap.String("serverKey", serverKey),
					zap.Error(err))
				return
			}

			if len(pids) == 0 {
				global.GVA_LOG.Info("未找到相关进程",
					zap.String("serverKey", serverKey))
				return
			}

			global.GVA_LOG.Info("找到相关进程",
				zap.String("serverKey", serverKey),
				zap.Ints("pids", pids))

			for _, pid := range pids {
				if err := utils.KillProcess(pid); err != nil {
					global.GVA_LOG.Error("终止进程失败",
						zap.String("serverKey", serverKey),
						zap.Int("pid", pid),
						zap.Error(err))
				} else {
					global.GVA_LOG.Info("成功终止进程",
						zap.String("serverKey", serverKey),
						zap.Int("pid", pid))
				}
			}
		}(event.ProjectUUID)
	} else {
		global.GVA_LOG.Info("项目不是托管服务类型，跳过进程终止",
			zap.String("projectUUID", event.ProjectUUID),
			zap.String("callMethod", event.CallMethod))
	}

	return nil
}

// 全局项目消息处理器实例
var GlobalProjectMessageHandler *ProjectMessageHandler

// InitProjectMessageHandler 初始化项目消息处理器
func InitProjectMessageHandler() {
	GlobalProjectMessageHandler = NewProjectMessageHandler()
	global.GVA_LOG.Info("项目消息处理器初始化完成")
}

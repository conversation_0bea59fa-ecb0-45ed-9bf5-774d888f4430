package mcp

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/service/integral"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SunoTaskService struct{}

// toUint 将字符串或其他整型可表示的值转换为 uint，无法转换则返回错误
func toUint(v interface{}) (uint, error) {
	switch t := v.(type) {
	case string:
		var x uint64
		_, err := fmt.Sscanf(t, "%d", &x)
		if err != nil {
			return 0, err
		}
		return uint(x), nil
	case int:
		if t < 0 {
			return 0, fmt.Errorf("negative")
		}
		return uint(t), nil
	case int64:
		if t < 0 {
			return 0, fmt.Errorf("negative")
		}
		return uint(t), nil
	case uint:
		return t, nil
	case uint64:
		return uint(t), nil
	default:
		return 0, fmt.Errorf("unsupported type")
	}
}

// CreateSunoTask 创建Suno任务
func (s *SunoTaskService) CreateSunoTask(req mcpReq.CreateSunoTaskReq) (sunoTask mcp.SunoTask, err error) {
	var existTask mcp.SunoTask
	if !errors.Is(global.GVA_DB.Where("task_id = ?", req.TaskID).First(&existTask).Error, gorm.ErrRecordNotFound) {
		return sunoTask, errors.New("任务ID已存在")
	}

	sunoTask = mcp.SunoTask{
		TaskID:       req.TaskID,
		UserUUID:     req.UserUUID,
		CallbackType: req.CallbackType,
		Prompt:       req.Prompt,
		ModelName:    req.ModelName,
		Status:       mcp.TaskStatusPending,
	}

	return sunoTask, global.GVA_DB.Create(&sunoTask).Error
}

// ProcessCallback 处理Suno回调
func (s *SunoTaskService) ProcessCallback(callbackReq mcp.SunoCallbackRequest) error {
	global.GVA_LOG.Info("开始处理Suno回调",
		zap.String("taskId", callbackReq.Data.TaskID),
		zap.String("callbackType", callbackReq.Data.CallbackType),
		zap.Int("code", callbackReq.Code),
		zap.String("msg", callbackReq.Msg))

	if callbackReq.Code != 200 {
		// 处理失败回调
		global.GVA_LOG.Warn("收到失败回调", zap.String("taskId", callbackReq.Data.TaskID), zap.String("error", callbackReq.Msg))

		// 1) 先更新任务状态为失败并记录错误
		if err := s.updateTaskStatus(callbackReq.Data.TaskID, mcp.TaskStatusFailed, callbackReq.Msg); err != nil {
			global.GVA_LOG.Error("更新任务失败状态出错", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
		}

		// 2) 查询 server_logs 获取唯一记录（按工具名+taskId 模糊匹配）
		var logRecord mcprouter.ServerLog
		db := global.GVA_DB.Model(&mcprouter.ServerLog{})
		if err := db.Where("tool_name IN ?", []string{"generate_music", "cover_music", "extend_music"}).
			Where("response_result LIKE ?", "%"+callbackReq.Data.TaskID+"%").
			Where("COALESCE(response_error,'') NOT LIKE ?", "%已回滚积分%").
			Order("id DESC").
			Limit(1).
			Find(&logRecord).Error; err != nil {
			global.GVA_LOG.Warn("查询server_logs失败或未找到记录", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
			return nil
		}
		if logRecord.ID == 0 {
			global.GVA_LOG.Warn("未找到可回滚的server_logs记录或已回滚", zap.String("taskId", callbackReq.Data.TaskID))
			return nil
		}

		// 3) 回滚积分
		var userIDUint uint
		if uid, convErr := toUint(logRecord.UserID); convErr == nil {
			userIDUint = uid
		}
		var projectIDUint, projectToolIDUint uint
		if logRecord.ServerUUID != "" {
			var proj mcp.Projects
			if err := global.GVA_DB.Where("uuid = ?", logRecord.ServerUUID).First(&proj).Error; err == nil {
				projectIDUint = uint(proj.ID)
			}
			var pt mcp.ProjectTools
			if err := global.GVA_DB.Where("project_uuid = ? AND name = ?", logRecord.ServerUUID, logRecord.ToolName).First(&pt).Error; err == nil && pt.ID > 0 {
				projectToolIDUint = uint(pt.ID)
			}
		}
		global.GVA_LOG.Info("开始回滚积分", zap.Uint("userID", userIDUint), zap.Int("points", logRecord.Points), zap.Uint("projectID", projectIDUint), zap.Uint("projectToolID", projectToolIDUint))

		sysUserService := integral.SysUserPointsService{}
		if err := sysUserService.AddPoints(
			userIDUint,
			logRecord.Points,
			"返回积分",
			projectIDUint,
			projectToolIDUint,
		); err != nil {
			global.GVA_LOG.Error("返回积分失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
		} else {
			global.GVA_LOG.Info("已回滚积分", zap.Uint("userID", userIDUint), zap.Int("points", logRecord.Points), zap.String("toolName", logRecord.ToolName), zap.String("serverUUID", logRecord.ServerUUID))
		}

		// 4) 标记 suno_tasks 已回滚（如无专用字段，则在错误信息中追加标记）
		if err := global.GVA_DB.Model(&mcp.SunoTask{}).Where("task_id = ?", callbackReq.Data.TaskID).
			Updates(map[string]interface{}{
				"error_message": gorm.Expr("CONCAT(IFNULL(error_message,''), ?)", " | 已回滚积分"),
			}).Error; err != nil {
			global.GVA_LOG.Warn("标记回滚失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
		} else {
			global.GVA_LOG.Info("已在suno_tasks标记回滚", zap.String("taskId", callbackReq.Data.TaskID))
		}

		// 5) 在 server_logs 中也追加标记
		if err := global.GVA_DB.Model(&mcprouter.ServerLog{}).Where("id = ?", logRecord.ID).
			Update("response_error", gorm.Expr("CONCAT(IFNULL(response_error,''), ?)", " | 已回滚积分")).Error; err != nil {
			global.GVA_LOG.Warn("在server_logs追加回滚标记失败", zap.Error(err), zap.Int64("logID", logRecord.ID))
		} else {
			global.GVA_LOG.Info("已在server_logs追加回滚标记", zap.Int64("logID", logRecord.ID))
		}

		return nil
	}

	// 根据回调类型处理
	switch callbackReq.Data.CallbackType {
	case mcp.CallbackTypeComplete, mcp.CallbackTypeFirst:
		// 处理音乐生成完成回调
		global.GVA_LOG.Info("处理音乐生成完成回调", zap.String("taskId", callbackReq.Data.TaskID))
		for _, musicData := range callbackReq.Data.Data {
			err := s.saveOrUpdateTask(callbackReq.Data.TaskID, callbackReq.Data.CallbackType, musicData)
			if err != nil {
				global.GVA_LOG.Error("保存任务数据失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
				return err
			}
		}
	case mcp.CallbackTypeText:
		// 处理文本生成完成回调
		global.GVA_LOG.Info("处理文本生成完成回调", zap.String("taskId", callbackReq.Data.TaskID))
		// 这里可以保存文本生成结果，暂时只记录日志
	case mcp.CallbackTypeError:
		// 处理错误回调
		global.GVA_LOG.Error("收到错误回调", zap.String("taskId", callbackReq.Data.TaskID), zap.String("error", callbackReq.Msg))
		return s.updateTaskStatus(callbackReq.Data.TaskID, mcp.TaskStatusFailed, callbackReq.Msg)
	default:
		// 处理其他类型的回调（兼容旧版本）
		global.GVA_LOG.Info("处理通用回调", zap.String("taskId", callbackReq.Data.TaskID), zap.String("callbackType", callbackReq.Data.CallbackType))
		for _, musicData := range callbackReq.Data.Data {
			err := s.saveOrUpdateTask(callbackReq.Data.TaskID, callbackReq.Data.CallbackType, musicData)
			if err != nil {
				global.GVA_LOG.Error("保存任务数据失败", zap.Error(err), zap.String("taskId", callbackReq.Data.TaskID))
				return err
			}
		}
	}

	global.GVA_LOG.Info("Suno回调处理完成", zap.String("taskId", callbackReq.Data.TaskID))
	return nil
}

// saveOrUpdateTask 保存或更新任务数据
func (s *SunoTaskService) saveOrUpdateTask(taskID, callbackType string, musicData mcp.SunoMusic) error {
	global.GVA_LOG.Info("开始保存或更新任务数据",
		zap.String("taskId", taskID),
		zap.String("callbackType", callbackType),
		zap.String("musicId", musicData.ID),
		zap.String("title", musicData.Title),
		zap.String("audioUrl", musicData.AudioURL))

	var sunoTask mcp.SunoTask

	// 解析创建时间
	var taskCreateTime *time.Time
	if musicData.CreateTime != nil {
		switch v := musicData.CreateTime.(type) {
		case string:
			if v != "" {
				if parsedTime, err := time.Parse("2006-01-02T15:04:05.000Z", v); err == nil {
					taskCreateTime = &parsedTime
				} else if parsedTime, err := time.Parse("2006-01-02 15:04:05", v); err == nil {
					taskCreateTime = &parsedTime
				} else {
					global.GVA_LOG.Warn("无法解析字符串格式的创建时间", zap.String("createTime", v))
				}
			}
		case float64:
			// 处理时间戳
			if v > 0 {
				var parsedTime time.Time
				if v > 1e10 {
					// 毫秒级时间戳
					parsedTime = time.UnixMilli(int64(v))
				} else {
					// 秒级时间戳
					parsedTime = time.Unix(int64(v), 0)
				}
				taskCreateTime = &parsedTime
				global.GVA_LOG.Info("解析时间戳成功", zap.Float64("timestamp", v), zap.Time("parsedTime", parsedTime))
			}
		case int64:
			// 处理时间戳
			if v > 0 {
				var parsedTime time.Time
				if v > 1e10 {
					// 毫秒级时间戳
					parsedTime = time.UnixMilli(v)
				} else {
					// 秒级时间戳
					parsedTime = time.Unix(v, 0)
				}
				taskCreateTime = &parsedTime
				global.GVA_LOG.Info("解析时间戳成功", zap.Int64("timestamp", v), zap.Time("parsedTime", parsedTime))
			}
		default:
			global.GVA_LOG.Warn("不支持的创建时间格式", zap.Any("createTime", musicData.CreateTime), zap.String("type", fmt.Sprintf("%T", musicData.CreateTime)))
		}
	}

	// 查找现有任务
	err := global.GVA_DB.Where("task_id = ?", taskID).First(&sunoTask).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新任务
		global.GVA_LOG.Info("创建新任务记录", zap.String("taskId", taskID))
		sunoTask = mcp.SunoTask{
			TaskID:               taskID,
			MusicID:              musicData.ID,
			AudioURL:             musicData.AudioURL,
			SourceAudioURL:       musicData.SourceAudioURL,
			StreamAudioURL:       musicData.StreamAudioURL,
			SourceStreamAudioURL: musicData.SourceStreamAudioURL,
			ImageURL:             musicData.ImageURL,
			SourceImageURL:       musicData.SourceImageURL,
			Prompt:               musicData.Prompt,
			ModelName:            musicData.ModelName,
			Title:                musicData.Title,
			Tags:                 musicData.Tags,
			Duration:             musicData.Duration,
			CallbackType:         callbackType,
			Status:               mcp.TaskStatusCompleted,
			TaskCreateTime:       taskCreateTime,
		}

		createErr := global.GVA_DB.Create(&sunoTask).Error
		if createErr != nil {
			global.GVA_LOG.Error("创建任务记录失败", zap.Error(createErr), zap.String("taskId", taskID))
			return createErr
		}
		global.GVA_LOG.Info("任务记录创建成功", zap.String("taskId", taskID), zap.Uint("recordId", sunoTask.ID))
		return nil
	} else if err != nil {
		global.GVA_LOG.Error("查询现有任务失败", zap.Error(err), zap.String("taskId", taskID))
		return err
	}

	// 更新现有任务
	global.GVA_LOG.Info("更新现有任务记录", zap.String("taskId", taskID), zap.Uint("recordId", sunoTask.ID))
	updates := map[string]interface{}{
		"music_id":                musicData.ID,
		"audio_url":               musicData.AudioURL,
		"source_audio_url":        musicData.SourceAudioURL,
		"stream_audio_url":        musicData.StreamAudioURL,
		"source_stream_audio_url": musicData.SourceStreamAudioURL,
		"image_url":               musicData.ImageURL,
		"source_image_url":        musicData.SourceImageURL,
		"prompt":                  musicData.Prompt,
		"model_name":              musicData.ModelName,
		"title":                   musicData.Title,
		"tags":                    musicData.Tags,
		"duration":                musicData.Duration,
		"callback_type":           callbackType,
		"status":                  mcp.TaskStatusCompleted,
		"error_message":           "", // 清除错误信息
	}

	if taskCreateTime != nil {
		updates["task_create_time"] = taskCreateTime
	}

	updateErr := global.GVA_DB.Model(&sunoTask).Updates(updates).Error
	if updateErr != nil {
		global.GVA_LOG.Error("更新任务记录失败", zap.Error(updateErr), zap.String("taskId", taskID))
		return updateErr
	}

	global.GVA_LOG.Info("任务记录更新成功", zap.String("taskId", taskID))
	return nil
}

// updateTaskStatus 更新任务状态
func (s *SunoTaskService) updateTaskStatus(taskID, status, errorMsg string) error {
	// 先尝试更新
	updates := map[string]interface{}{
		"status": status,
	}
	if errorMsg != "" {
		updates["error_message"] = errorMsg
	}

	tx := global.GVA_DB.Model(&mcp.SunoTask{}).Where("task_id = ?", taskID).Updates(updates)
	if tx.Error != nil {
		return tx.Error
	}
	if tx.RowsAffected > 0 {
		return nil
	}

	// 若未更新到任何记录，则创建一条新的记录，确保错误被持久化
	newTask := mcp.SunoTask{
		TaskID:       taskID,
		Status:       status,
		ErrorMessage: errorMsg,
	}
	return global.GVA_DB.Create(&newTask).Error
}

// GetSunoTaskByID 根据任务ID获取任务
func (s *SunoTaskService) GetSunoTaskByID(taskID string) (sunoTask mcp.SunoTask, err error) {
	err = global.GVA_DB.Where("task_id = ?", taskID).First(&sunoTask).Error
	return
}

// GetSunoTaskList 获取任务列表
func (s *SunoTaskService) GetSunoTaskList(info mcpReq.SunoTaskSearchReq) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&mcp.SunoTask{})

	var sunoTasks []mcp.SunoTask

	// 构建查询条件
	if info.TaskID != "" {
		db = db.Where("task_id LIKE ?", "%"+info.TaskID+"%")
	}
	if info.Status != "" {
		db = db.Where("status = ?", info.Status)
	}
	if info.CallbackType != "" {
		db = db.Where("callback_type = ?", info.CallbackType)
	}
	if info.UserUUID != "" {
		db = db.Where("user_uuid = ?", info.UserUUID)
	}
	if info.StartTime != "" {
		db = db.Where("created_at >= ?", info.StartTime)
	}
	if info.EndTime != "" {
		db = db.Where("created_at <= ?", info.EndTime)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&sunoTasks).Error
	return sunoTasks, total, err
}

// CallSunoAPI 调用Suno API查询任务进度
func (s *SunoTaskService) CallSunoAPI(taskID string) (interface{}, error) {
	// 检查配置
	if global.GVA_CONFIG.Suno.BaseURL == "" {
		return nil, errors.New("suno api base url 未配置")
	}

	// 构建API URL
	apiURL := fmt.Sprintf("%s/api/v1/task/%s", global.GVA_CONFIG.Suno.BaseURL, taskID)

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	if global.GVA_CONFIG.Suno.APIKey != "" {
		req.Header.Set("Authorization", "Bearer "+global.GVA_CONFIG.Suno.APIKey)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode == 404 {
		return nil, errors.New("任务进行中，请稍后再查询")
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("suno api返回错误状态码: %d", resp.StatusCode)
	}

	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return nil, err
	}

	// 检查API响应中的错误状态
	if result["code"] != nil {
		if code, ok := result["code"].(float64); ok && code != 200 {
			return nil, fmt.Errorf("suno api返回错误: %v", result["msg"])
		}
	}

	return result, nil
}

// QueryTaskProgress 查询任务进度（先查数据库，再查API）
func (s *SunoTaskService) QueryTaskProgress(taskID string) (interface{}, error) {
	// 先查数据库
	sunoTask, err := s.GetSunoTaskByID(taskID)
	if err == nil {
		// 如果任务已完成，直接返回数据库结果
		if sunoTask.Status == mcp.TaskStatusCompleted {
			return sunoTask, nil
		}
		// 如果任务还在进行中，调用API获取最新状态
		if sunoTask.Status == mcp.TaskStatusPending || sunoTask.Status == mcp.TaskStatusRunning {
			apiResult, apiErr := s.CallSunoAPI(taskID)
			if apiErr == nil {
				return apiResult, nil
			}
			// API调用失败，不返回失败状态，转换为进行中并带错误原因（保持返回结构为 SunoTask）
			global.GVA_LOG.Warn("调用Suno API失败，返回pending并附带错误原因", zap.Error(apiErr), zap.String("taskId", taskID))
			return mcp.SunoTask{TaskID: taskID, Status: mcp.TaskStatusPending, ErrorMessage: apiErr.Error()}, nil
		}
		// 如果任务失败，则直接返回数据库中的失败信息
		if sunoTask.Status == mcp.TaskStatusFailed {
			return sunoTask, nil
		}
	}

	// 数据库中没有记录，直接调用Suno API查询
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("数据库中未找到任务，尝试从Suno API查询", zap.String("taskId", taskID))
		apiResult, apiErr := s.CallSunoAPI(taskID)
		if apiErr != nil {
			// 区分“进行中”与真实错误（兼容不同标点）
			msg := apiErr.Error()
			if strings.Contains(msg, "任务进行中") {
				global.GVA_LOG.Warn("Suno API查询返回进行中，返回pending状态", zap.Error(apiErr), zap.String("taskId", taskID))
				return mcp.SunoTask{TaskID: taskID, Status: mcp.TaskStatusPending}, nil
			}

			// 真实错误也不返回失败，改为进行中并附带错误原因（保持返回结构为 SunoTask）
			global.GVA_LOG.Warn("Suno API查询失败，返回pending并附带错误原因", zap.Error(apiErr), zap.String("taskId", taskID))
			return mcp.SunoTask{TaskID: taskID, Status: mcp.TaskStatusPending, ErrorMessage: msg}, nil
		}
		return apiResult, nil
	}

	return nil, err
}

package mcp

import (
	"context"
	"fmt"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"gorm.io/gorm"
)

type UseCaseService struct{}

type ClientInfo struct {
	ID   uint    `json:"id"`
	Name *string `json:"name"`
	Logo string  `json:"logo"`
}

type ProjectInfo struct {
	ID      uint   `json:"id"`
	Name    string `json:"name"`
	LogoUrl string `json:"logoUrl"`
	UUID    string `json:"uuid"`
}

type UseCaseDetail struct {
	mcp.UseCase
	Client  *ClientInfo   `json:"client,omitempty"`
	Servers []ProjectInfo `json:"servers,omitempty"`
	Author  string        `json:"author"`
	Avatar  string        `json:"avatar"`
}

type UseCaseWithUser struct {
	mcp.UseCase
	NickName  string `json:"nickName"`
	HeaderImg string `json:"headerImg"`
}

func (useCaseService *UseCaseService) CreateUseCase(ctx context.Context, useCase *mcp.UseCase) error {
	return global.GVA_DB.Create(useCase).Error
}

func (useCaseService *UseCaseService) DeleteUseCase(ctx context.Context, id uint, userId uint) error {
	return global.GVA_DB.Where("id = ? AND user_id = ?", id, userId).Delete(&mcp.UseCase{}).Error
}

func (useCaseService *UseCaseService) UpdateUseCase(ctx context.Context, useCase *mcp.UseCase) error {
	return global.GVA_DB.Save(useCase).Error
}

func (useCaseService *UseCaseService) GetUseCaseByID(ctx context.Context, id uint) (*UseCaseDetail, error) {
	var useCase mcp.UseCase
	err := global.GVA_DB.Where("id = ? AND status = 1", id).First(&useCase).Error
	if err != nil {
		return nil, err
	}

	// 查询 client 的 id, name, logo
	var client *ClientInfo
	if useCase.ClientId != nil {
		var c ClientInfo
		err := global.GVA_DB.Model(&mcp.McpClient{}).
			Select("id, name, logo").
			Where("id = ?", *useCase.ClientId).
			First(&c).Error
		if err == nil {
			client = &c
		}
	}

	// 查询 servers 的 id, name, logo,uuid
	var servers []ProjectInfo
	if len(useCase.ServerIds) > 0 {
		ids := []uint(useCase.ServerIds)
		err := global.GVA_DB.Model(&mcp.Projects{}).
			Select("id, name, logo_url, uuid").
			Where("id IN ?", ids).
			Find(&servers).Error
		if err != nil {
			servers = nil
		}
	}

	// 查询用户昵称和头像
	type UserInfo struct {
		NickName  string
		HeaderImg string
	}
	var userInfo UserInfo
	if useCase.UserId != 0 {
		_ = global.GVA_DB.Model(&system.SysUser{}).
			Select("nick_name, header_img").
			Where("id = ?", useCase.UserId).
			First(&userInfo).Error
	}

	return &UseCaseDetail{
		UseCase: useCase,
		Client:  client,
		Servers: servers,
		Author:  userInfo.NickName,
		Avatar:  userInfo.HeaderImg,
	}, nil
}

func (useCaseService *UseCaseService) GetUseCaseList(ctx context.Context, params systemReq.SearchApiParams, projectsId int64) ([]UseCaseDetail, int64, error) {
	var list []UseCaseWithUser
	var total int64
	db := global.GVA_DB.Model(&mcp.UseCase{}).
		Select("use_cases.*, sys_users.nick_name, sys_users.header_img").
		Joins("LEFT JOIN sys_users ON use_cases.user_id = sys_users.id").
		Where("use_cases.status = 1")
	if params.Keyword != "" {
		db = db.Where("sys_users.nick_name LIKE ? OR use_cases.summary LIKE ?", "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}
	if projectsId != 0 {
		db = db.Where("JSON_CONTAINS(use_cases.server_ids, ?)", "["+strconv.FormatInt(projectsId, 10)+"]")
	}
	db.Order("use_cases.created_at DESC")
	db.Count(&total)
	err := db.Offset((params.Page - 1) * params.PageSize).Limit(params.PageSize).Find(&list).Error
	if err != nil {
		return nil, total, err
	}
	var result []UseCaseDetail
	for _, uc := range list {
		result = append(result, UseCaseDetail{
			UseCase: uc.UseCase,
			Client:  nil,
			Servers: nil,
			Author:  uc.NickName,
			Avatar:  uc.HeaderImg,
		})
	}
	return result, total, nil
}

func (useCaseService *UseCaseService) MyUseCaseList(ctx context.Context, params systemReq.SearchApiParams, userId uint) ([]UseCaseDetail, int64, error) {

	var list []UseCaseWithUser
	var total int64
	db := global.GVA_DB.Model(&mcp.UseCase{}).
		Select("use_cases.*, sys_users.nick_name, sys_users.header_img").
		Joins("LEFT JOIN sys_users ON use_cases.user_id = sys_users.id").
		Where("use_cases.user_id = ?", userId)
	if params.Keyword != "" {
		db = db.Where("sys_users.nick_name LIKE ? OR use_cases.summary LIKE ?", "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}
	db.Count(&total)
	err := db.Offset((params.Page - 1) * params.PageSize).Limit(params.PageSize).Find(&list).Error
	if err != nil {
		return nil, total, err
	}
	var result []UseCaseDetail
	for _, uc := range list {
		result = append(result, UseCaseDetail{
			UseCase: uc.UseCase,
			Client:  nil,
			Servers: nil,
			Author:  uc.NickName,
			Avatar:  uc.HeaderImg,
		})
	}
	return result, total, nil
}

// ApproveUseCase 审核通过案例
func (useCaseService *UseCaseService) ApproveUseCase(ctx context.Context, id uint) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 查询案例信息
		var useCase mcp.UseCase
		if err := tx.Where("id = ?", id).First(&useCase).Error; err != nil {
			return fmt.Errorf("案例不存在: %v", err)
		}

		// 检查案例状态，避免重复审核
		if useCase.Status == 1 {
			return fmt.Errorf("案例已经审核通过")
		}

		// 2. 更新案例状态为1（审核通过）
		if err := tx.Model(&useCase).Update("status", 1).Error; err != nil {
			return fmt.Errorf("更新案例状态失败: %v", err)
		}

		//// 3. 获取任务信息
		//var task system.SysTask
		//if err := tx.Where("id = ?", config.TASK_SHARE_CASE).First(&task).Error; err != nil {
		//	return fmt.Errorf("获取分享案例任务信息失败: %v", err)
		//}
		//
		//// 4. 使用任务服务为案例创建者添加积分和任务记录
		//taskService := systemService.TaskServiceApp
		//// Remark字段记录UseCase的summary
		//reason := useCase.Summary
		//
		//if err := taskService.AddPointsWithTaskRecord(
		//	useCase.UserId,
		//	task.Points,
		//	reason,
		//	"task",
		//	config.TASK_SHARE_CASE,
		//); err != nil {
		//	return fmt.Errorf("添加积分和任务记录失败: %v", err)
		//}

		return nil
	})
}

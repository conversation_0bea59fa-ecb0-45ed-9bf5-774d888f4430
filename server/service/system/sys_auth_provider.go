package system

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/upload"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AuthProviderService struct{}

// UnifiedLogin 统一登录方法 - 支持多种认证方式
func (aps *AuthProviderService) UnifiedLogin(req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	switch req.Provider {
	case system.ProviderTypeUsername:
		return aps.loginByPassword(req)
	case system.ProviderTypeEmail:
		return aps.loginByEmail(req)
	case system.ProviderTypePhone:
		return aps.loginByPhone(req)
	case system.ProviderTypeWechat:
		return aps.loginByWechat(req)
	default:
		return nil, errors.New("不支持的认证提供商类型")
	}
}

// loginByPassword 用户名密码登录
func (aps *AuthProviderService) loginByPassword(req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	if req.Username == "" || req.Password == "" {
		return nil, errors.New("用户名和密码不能为空")
	}
	//账号密码不需要证提供商

	u := &system.SysUser{Username: req.Username, Password: req.Password}
	var user system.SysUser
	err := global.GVA_DB.Where("username = ?", u.Username).Preload("Authorities").Preload("Authority").First(&user).Error
	if err == nil {
		// 验证密码
		if !utils.BcryptCheck(req.Password, user.Password) {
			return nil, errors.New("密码错误")
		}
	} else {
		return nil, errors.New("用户不存在！")
	}
	return aps.generateLoginResponse(user, req.Provider, false)
}

// fallbackPasswordLogin 兼容旧系统的密码登录
func (aps *AuthProviderService) fallbackPasswordLogin(req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	var user system.SysUser
	err := global.GVA_DB.Where("username = ?", req.Username).
		Preload("Authorities").Preload("Authority").First(&user).Error

	if err != nil {
		return nil, errors.New("用户不存在")
	}

	if !utils.BcryptCheck(req.Password, user.Password) {
		return nil, errors.New("密码错误")
	}

	// 自动迁移到新的认证提供商系统
	go aps.migrateUserToAuthProvider(user.ID, system.ProviderTypeUsername, req.Username, "", "")

	return aps.generateLoginResponse(user, req.Provider, false)
}

// loginByEmail 邮箱登录
func (aps *AuthProviderService) loginByEmail(req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	if req.Email == "" || req.Code == "" {
		return nil, errors.New("邮箱和验证码不能为空")
	}

	// 验证邮箱验证码（这里需要实现验证码验证逻辑）
	if !aps.VerifyEmailCode(req.Email, req.Code) {
		return nil, errors.New("验证码错误或已过期")
	}

	// 首先查找SysAuthProvider记录
	var authProvider system.SysAuthProvider
	err := global.GVA_DB.Where("provider = ? AND provider_id = ?", system.ProviderTypeEmail, req.Email).
		Preload("User").First(&authProvider).Error

	if err == nil {
		// 找到认证提供商记录，直接登录
		now := time.Now()
		authProvider.LastUsedAt = &now
		global.GVA_DB.Save(&authProvider)
		return aps.generateLoginResponse(authProvider.User, system.ProviderTypeEmail, false)
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询认证提供商失败: %w", err)
	}

	// 没有找到认证提供商记录，查找是否有旧用户数据
	var user system.SysUser
	err = global.GVA_DB.Where("email = ?", req.Email).
		Preload("Authorities").Preload("Authority").First(&user).Error

	if err == nil {
		// 找到旧用户数据，为其绑定SysAuthProvider
		return aps.migrateOldUserToAuthProvider(user, system.ProviderTypeEmail, req.Email)
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 没有找到用户，返回错误提示需要先注册
	return nil, errors.New("用户不存在，请先注册")
}

// loginByPhone 手机号登录
func (aps *AuthProviderService) loginByPhone(req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	if req.Phone == "" || req.Code == "" {
		return nil, errors.New("手机号和验证码不能为空")
	}

	// 验证手机验证码
	if !aps.VerifyPhoneCode(req.Phone, req.Code) {
		return nil, errors.New("验证码错误或已过期")
	}

	// 首先查找SysAuthProvider记录
	var authProvider system.SysAuthProvider
	err := global.GVA_DB.Where("provider = ? AND provider_id = ?", system.ProviderTypePhone, req.Phone).
		Preload("User").First(&authProvider).Error

	if err == nil {
		// 找到认证提供商记录，直接登录
		now := time.Now()
		authProvider.LastUsedAt = &now
		global.GVA_DB.Save(&authProvider)
		return aps.generateLoginResponse(authProvider.User, system.ProviderTypePhone, false)
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询认证提供商失败: %w", err)
	}

	// 没有找到认证提供商记录，查找是否有旧用户数据
	var user system.SysUser
	err = global.GVA_DB.Where("phone = ?", req.Phone).
		Preload("Authorities").Preload("Authority").First(&user).Error

	if err == nil {
		// 找到旧用户数据，为其绑定SysAuthProvider
		return aps.migrateOldUserToAuthProvider(user, system.ProviderTypePhone, req.Phone)
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 没有找到用户，返回错误提示需要先注册
	return nil, errors.New("用户不存在，请先注册")
}

// loginByWechat 微信登录 - 使用微信OAuth授权
func (aps *AuthProviderService) loginByWechat(req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	if req.Code == "" {
		return nil, errors.New("微信授权码不能为空")
	}

	// 使用微信OAuth服务处理登录
	wechatService := &WechatOAuthService{}

	// 构造微信授权请求
	wechatReq := systemReq.WechatAuthRequest{
		Code:  req.Code,
		State: req.Metadata, // 使用Metadata字段传递state
	}

	//code换取用户信息
	wechatResp, err := wechatService.LoginByWechat(wechatReq)
	if err != nil {
		return nil, fmt.Errorf("微信授权失败: %w", err)
	}

	switch wechatResp.Action {
	case "login":
		// 存在用户
		//aps.ensureWechatAuthProvider(wechatResp.User)
		return aps.convertWechatToUnifiedResponse(wechatResp), nil

	case "bind":
		// 需要绑定账号，返回候选用户信息
		return aps.handleWechatBindingFlow(wechatResp, req)

	default:
		return nil, errors.New("未知的微信授权响应类型")
	}
}

// createNewUserWithProvider 创建新用户并关联认证提供商
func (aps *AuthProviderService) createNewUserWithProvider(provider, providerId string, req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	// 创建新用户
	newUser := &system.SysUser{
		UUID:        uuid.New(),
		Username:    aps.generateUsername(provider, providerId, req),
		NickName:    aps.generateNickname(req),
		Password:    utils.BcryptHash(global.GVA_CONFIG.System.DefaultPassword),
		AuthorityId: 666, // 默认角色
		Enable:      1,   // 默认启用
		Email:       req.Email,
		Phone:       req.Phone,
	}

	// 设置默认权限
	var authorities []system.SysAuthority
	authorities = append(authorities, system.SysAuthority{AuthorityId: 666})
	newUser.Authorities = authorities

	// 开始事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建用户
	if err := tx.Create(newUser).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 创建认证提供商记录
	authProvider := &system.SysAuthProvider{
		UserId:     newUser.ID,
		Provider:   provider,
		ProviderId: providerId,
		OpenId:     req.OpenId,
		UnionId:    req.UnionId,
		Email:      req.Email,
		Phone:      req.Phone,
		Username:   req.Username,
		Verified:   true, // 默认为已验证
		Metadata:   req.Metadata,
		LastUsedAt: &time.Time{},
	}
	now := time.Now()
	authProvider.LastUsedAt = &now

	if err := tx.Create(authProvider).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建认证提供商记录失败: %w", err)
	}

	tx.Commit()

	return aps.generateLoginResponse(*newUser, provider, true)
}

// generateLoginResponse 生成登录响应
func (aps *AuthProviderService) generateLoginResponse(user system.SysUser, provider string, isNewUser bool) (*systemRes.UnifiedLoginResponse, error) {
	// 检查用户状态
	if user.Enable != 1 {
		return nil, errors.New("用户已被禁用")
	}

	// 设置用户默认路由
	MenuServiceApp.UserAuthorityDefaultRouter(&user)

	return &systemRes.UnifiedLoginResponse{
		User:      user,
		Provider:  provider,
		IsNewUser: isNewUser,
		NeedsBind: false,
	}, nil
}

// BindProvider 绑定认证提供商到当前用户
func (aps *AuthProviderService) BindProvider(userId uint, req systemReq.AuthProviderBindRequest) error {
	// 检查是否已经绑定
	var existing system.SysAuthProvider
	err := global.GVA_DB.Where("user_id = ? AND provider = ?", userId, req.Provider).First(&existing).Error
	if err == nil {
		return errors.New("该认证方式已经绑定到此账号")
	}

	// 检查该认证提供商是否已被其他用户使用
	err = global.GVA_DB.Where("provider = ? AND provider_id = ?", req.Provider, req.ProviderId).First(&existing).Error
	if err == nil {
		return errors.New("该认证方式已被其他用户绑定")
	}

	// 验证绑定请求
	if err := aps.validateBindRequest(req); err != nil {
		return err
	}

	// 创建认证提供商记录
	authProvider := &system.SysAuthProvider{
		UserId:     userId,
		Provider:   req.Provider,
		ProviderId: req.ProviderId,
		OpenId:     req.OpenId,
		UnionId:    req.UnionId,
		Email:      req.Email,
		Phone:      req.Phone,
		Username:   req.Username,
		Verified:   false, // 默认未验证，需要验证流程
		Metadata:   req.Metadata,
	}

	return global.GVA_DB.Create(authProvider).Error
}

// UnbindProvider 解绑认证提供商
func (aps *AuthProviderService) UnbindProvider(userId uint, req systemReq.AuthProviderUnbindRequest) error {
	// 检查用户至少保留一种登录方式
	var count int64
	global.GVA_DB.Model(&system.SysAuthProvider{}).Where("user_id = ?", userId).Count(&count)

	if count <= 1 {
		return errors.New("至少需要保留一种登录方式")
	}

	// 删除认证提供商记录
	result := global.GVA_DB.Where("user_id = ? AND provider = ? AND provider_id = ?",
		userId, req.Provider, req.ProviderId).Delete(&system.SysAuthProvider{})

	if result.Error != nil {
		return fmt.Errorf("解绑失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("未找到要解绑的认证方式")
	}

	return nil
}

// GetUserProviders 获取用户的认证提供商列表
func (aps *AuthProviderService) GetUserProviders(userId uint) (*systemRes.UserProvidersResponse, error) {
	var providers []system.SysAuthProvider
	err := global.GVA_DB.Where("user_id = ?", userId).Find(&providers).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户认证提供商失败: %w", err)
	}

	var providerInfos []systemRes.UserProviderInfo
	for _, provider := range providers {
		info := systemRes.UserProviderInfo{
			Provider:   provider.Provider,
			ProviderId: aps.maskProviderId(provider.ProviderId, provider.Provider),
			Verified:   provider.Verified,
			CanUnbind:  len(providers) > 1, // 至少保留一种登录方式
		}

		if provider.LastUsedAt != nil {
			info.LastUsedAt = provider.LastUsedAt.Format("2006-01-02 15:04:05")
		}

		providerInfos = append(providerInfos, info)
	}

	return &systemRes.UserProvidersResponse{
		Providers: providerInfos,
		Total:     int64(len(providerInfos)),
	}, nil
}

// CheckProviderExist 检查认证提供商是否存在
func (aps *AuthProviderService) CheckProviderExist(req systemReq.CheckProviderExistRequest) (*systemRes.ProviderExistResponse, error) {
	var authProvider system.SysAuthProvider
	query := global.GVA_DB.Where("provider = ?", req.Provider)

	if req.ProviderId != "" {
		query = query.Where("provider_id = ?", req.ProviderId)
	}
	if req.Email != "" {
		query = query.Where("email = ?", req.Email)
	}
	if req.Phone != "" {
		query = query.Where("phone = ?", req.Phone)
	}
	if req.Username != "" {
		query = query.Where("username = ?", req.Username)
	}

	err := query.Preload("User").First(&authProvider).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &systemRes.ProviderExistResponse{Exists: false}, nil
		}
		return nil, fmt.Errorf("查询认证提供商失败: %w", err)
	}

	return &systemRes.ProviderExistResponse{
		Exists: true,
		User:   &authProvider.User,
	}, nil
}

// 辅助方法

// generateUsername 生成用户名
func (aps *AuthProviderService) generateUsername(provider, providerId string, req systemReq.UnifiedLoginRequest) string {
	if req.Username != "" {
		return req.Username
	}
	if req.Email != "" {
		return req.Email
	}
	if req.Phone != "" {
		return req.Phone
	}
	return fmt.Sprintf("%s_%s", provider, providerId)
}

// generateNickname 生成昵称
func (aps *AuthProviderService) generateNickname(req systemReq.UnifiedLoginRequest) string {
	if req.Username != "" {
		return req.Username
	}
	if req.Email != "" {
		parts := strings.Split(req.Email, "@")
		return parts[0]
	}
	if req.Phone != "" {
		// 手机号脱敏作为昵称
		if len(req.Phone) >= 7 {
			return req.Phone[:3] + "****" + req.Phone[len(req.Phone)-4:]
		}
		return req.Phone
	}
	return "新用户"
}

// maskProviderId 脱敏处理ProviderId
func (aps *AuthProviderService) maskProviderId(providerId, provider string) string {
	if provider == system.ProviderTypeEmail {
		parts := strings.Split(providerId, "@")
		if len(parts) == 2 && len(parts[0]) > 2 {
			return parts[0][:2] + "***@" + parts[1]
		}
	}
	if provider == system.ProviderTypePhone {
		if len(providerId) >= 7 {
			return providerId[:3] + "****" + providerId[len(providerId)-4:]
		}
	}
	if len(providerId) > 8 {
		return providerId[:4] + "****" + providerId[len(providerId)-4:]
	}
	return providerId
}

// validateBindRequest 验证绑定请求
func (aps *AuthProviderService) validateBindRequest(req systemReq.AuthProviderBindRequest) error {
	switch req.Provider {
	case system.ProviderTypeEmail:
		if req.Email == "" {
			return errors.New("邮箱不能为空")
		}
		if req.Code == "" {
			return errors.New("验证码不能为空")
		}
		if !aps.VerifyEmailCode(req.Email, req.Code) {
			return errors.New("邮箱验证码错误")
		}
	case system.ProviderTypePhone:
		if req.Phone == "" {
			return errors.New("手机号不能为空")
		}
		if req.Code == "" {
			return errors.New("验证码不能为空")
		}
		if !aps.VerifyPhoneCode(req.Phone, req.Code) {
			return errors.New("手机验证码错误")
		}
	}
	return nil
}

// VerifyEmailCode 验证邮箱验证码
func (aps *AuthProviderService) VerifyEmailCode(email, code string) bool {
	// TODO: 实现邮箱验证码验证逻辑
	global.GVA_LOG.Info("验证邮箱验证码", zap.String("email", email), zap.String("code", code))
	return true // 临时返回true
}

// VerifyPhoneCode 验证手机验证码
func (aps *AuthProviderService) VerifyPhoneCode(phone, code string) bool {
	// 可以使用短信验证码服务或其他验证方式
	if code == "" {
		return false
	}

	// 万能验证码，用于测试环境
	masterCodeOpen := global.GVA_CONFIG.System.MasterCodeOpen
	if masterCodeOpen {
		masterCode := global.GVA_CONFIG.System.MasterCode
		if code == masterCode {
			global.GVA_LOG.Info("使用万能验证码验证通过", zap.String("phone", phone), zap.String("code", code))
			return true
		}
	}

	// 从Redis中获取验证码进行验证
	key := "SMS_CODE:" + phone
	storedCode, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err != nil {
		return false
	}

	if storedCode != code {
		return false
	}

	// 验证成功后删除验证码
	global.GVA_REDIS.Del(context.Background(), key)
	return true
}

// migrateUserToAuthProvider 迁移用户到认证提供商系统
func (aps *AuthProviderService) migrateUserToAuthProvider(userId uint, provider, providerId, email, phone string) {
	// 检查是否已经存在
	var existing system.SysAuthProvider
	err := global.GVA_DB.Where("user_id = ? AND provider = ?", userId, provider).First(&existing).Error
	if err == nil {
		return // 已存在，跳过
	}

	// 创建认证提供商记录
	authProvider := &system.SysAuthProvider{
		UserId:     userId,
		Provider:   provider,
		ProviderId: providerId,
		Email:      email,
		Phone:      phone,
		Verified:   true,
	}

	global.GVA_DB.Create(authProvider)
}

// migrateOldUserToAuthProvider 为旧用户迁移到认证提供商系统并返回登录响应
func (aps *AuthProviderService) migrateOldUserToAuthProvider(user system.SysUser, provider, providerId string) (*systemRes.UnifiedLoginResponse, error) {
	// 检查是否已经存在认证提供商记录
	var existing system.SysAuthProvider
	err := global.GVA_DB.Where("user_id = ? AND provider = ?", user.ID, provider).First(&existing).Error
	if err == nil {
		// 已存在，更新最后使用时间并返回登录响应
		now := time.Now()
		existing.LastUsedAt = &now
		global.GVA_DB.Save(&existing)
		return aps.generateLoginResponse(user, provider, false)
	}

	// 创建认证提供商记录
	authProvider := &system.SysAuthProvider{
		UserId:     user.ID,
		Provider:   provider,
		ProviderId: providerId,
		Email:      user.Email,
		Phone:      user.Phone,
		Verified:   true,
	}
	now := time.Now()
	authProvider.LastUsedAt = &now

	if err := global.GVA_DB.Create(authProvider).Error; err != nil {
		return nil, fmt.Errorf("创建认证提供商记录失败: %w", err)
	}

	return aps.generateLoginResponse(user, provider, false)
}

// parseTempToken 解析临时令牌
func (aps *AuthProviderService) parseTempToken(tempToken string) (*TempTokenData, error) {
	// 解码base64
	jsonData, err := base64.StdEncoding.DecodeString(tempToken)
	if err != nil {
		return nil, fmt.Errorf("令牌解码失败: %w", err)
	}

	// 解析JSON
	var data TempTokenData
	if err := json.Unmarshal(jsonData, &data); err != nil {
		return nil, fmt.Errorf("令牌格式错误: %w", err)
	}

	return &data, nil
}

// TempTokenData 临时令牌数据结构
type TempTokenData struct {
	Provider   string `json:"provider"`
	ProviderId string `json:"providerId"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	OpenId     string `json:"openId"`
	UnionId    string `json:"unionId"`
	Metadata   string `json:"metadata"`
	Timestamp  int64  `json:"timestamp"`
	Expires    int64  `json:"expires"`
}

// processBind 处理绑定到已有账号
func (aps *AuthProviderService) processBind(req systemReq.AccountBindConfirmRequest, bindingData *TempTokenData) (*systemRes.AccountBindConfirmResponse, error) {
	if req.TargetUserId == 0 {
		return nil, errors.New("目标用户ID不能为空")
	}

	// 验证用户身份
	if err := aps.verifyUserIdentity(req.TargetUserId, req.Password, req.VerifyCode); err != nil {
		return nil, fmt.Errorf("身份验证失败: %w", err)
	}

	// 检查是否已经绑定
	var existing system.SysAuthProvider
	err := global.GVA_DB.Where("user_id = ? AND provider = ?", req.TargetUserId, bindingData.Provider).First(&existing).Error
	if err == nil {
		return nil, errors.New("该认证方式已经绑定到此账号")
	}

	// 检查该认证提供商是否已被其他用户使用
	err = global.GVA_DB.Where("provider = ? AND provider_id = ?", bindingData.Provider, bindingData.ProviderId).First(&existing).Error
	if err == nil {
		return nil, errors.New("该认证方式已被其他用户绑定")
	}

	// 创建认证提供商记录
	authProvider := &system.SysAuthProvider{
		UserId:     req.TargetUserId,
		Provider:   bindingData.Provider,
		ProviderId: bindingData.ProviderId,
		OpenId:     bindingData.OpenId,
		UnionId:    bindingData.UnionId,
		Email:      bindingData.Email,
		Phone:      bindingData.Phone,
		Verified:   true,
		Metadata:   bindingData.Metadata,
	}
	now := time.Now()
	authProvider.LastUsedAt = &now

	if err := global.GVA_DB.Create(authProvider).Error; err != nil {
		return nil, fmt.Errorf("绑定认证提供商失败: %w", err)
	}

	// 获取用户信息
	var user system.SysUser
	if err := global.GVA_DB.Where("id = ?", req.TargetUserId).Preload("Authorities").Preload("Authority").First(&user).Error; err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 生成登录token
	token, claims, err := utils.LoginToken(&user)
	if err != nil {
		return nil, fmt.Errorf("生成token失败: %w", err)
	}

	return &systemRes.AccountBindConfirmResponse{
		Success:   true,
		User:      user,
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix(),
		Provider:  bindingData.Provider,
		Message:   "账号绑定成功",
	}, nil
}

// processCreateNew 处理创建新账号
func (aps *AuthProviderService) processCreateNew(req systemReq.AccountBindConfirmRequest, bindingData *TempTokenData) (*systemRes.AccountBindConfirmResponse, error) {
	// 构造UnifiedLoginRequest
	unifiedReq := systemReq.UnifiedLoginRequest{
		Provider:   bindingData.Provider,
		ProviderId: bindingData.ProviderId,
		Email:      bindingData.Email,
		Phone:      bindingData.Phone,
		OpenId:     bindingData.OpenId,
		UnionId:    bindingData.UnionId,
		Metadata:   bindingData.Metadata,
	}

	// 创建新用户
	loginResp, err := aps.createNewUserWithProvider(bindingData.Provider, bindingData.ProviderId, unifiedReq)
	if err != nil {
		return nil, fmt.Errorf("创建新用户失败: %w", err)
	}

	// 生成登录token
	token, claims, err := utils.LoginToken(&loginResp.User)
	if err != nil {
		return nil, fmt.Errorf("生成token失败: %w", err)
	}

	return &systemRes.AccountBindConfirmResponse{
		Success:   true,
		User:      loginResp.User,
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix(),
		Provider:  bindingData.Provider,
		Message:   "新账号创建成功",
	}, nil
}

// verifyUserIdentity 验证用户身份
func (aps *AuthProviderService) verifyUserIdentity(userId uint, password, verifyCode string) error {
	if password == "" && verifyCode == "" {
		return errors.New("密码或验证码不能为空")
	}

	// 获取用户信息
	var user system.SysUser
	if err := global.GVA_DB.Where("id = ?", userId).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 验证密码
	if password != "" {
		if !utils.BcryptCheck(password, user.Password) {
			return errors.New("密码错误")
		}
		return nil
	}

	// 验证验证码（这里需要实现具体的验证码验证逻辑）
	if verifyCode != "" {
		// TODO: 实现验证码验证逻辑
		// 这里可以根据用户的邮箱或手机号验证码进行验证
		return nil
	}

	return errors.New("身份验证失败")
}

// MergeAccounts 合并账号
func (aps *AuthProviderService) MergeAccounts(req systemReq.AccountMergeRequest) (*systemRes.AccountMergeResponse, error) {
	if !req.ConfirmMerge {
		return nil, errors.New("请确认合并操作")
	}

	if len(req.SourceUserIds) == 0 {
		return nil, errors.New("源用户列表不能为空")
	}

	// 验证目标用户身份
	if err := aps.verifyUserIdentity(req.TargetUserId, req.Password, ""); err != nil {
		return nil, fmt.Errorf("目标用户身份验证失败: %w", err)
	}

	// 检查源用户是否包含目标用户
	for _, sourceId := range req.SourceUserIds {
		if sourceId == req.TargetUserId {
			return nil, errors.New("源用户列表不能包含目标用户")
		}
	}

	// 开始事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	mergedCount := 0

	// 合并每个源用户的认证提供商
	for _, sourceId := range req.SourceUserIds {
		var sourceProviders []system.SysAuthProvider
		if err := tx.Where("user_id = ?", sourceId).Find(&sourceProviders).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("查询源用户认证提供商失败: %w", err)
		}

		// 迁移认证提供商到目标用户
		for _, provider := range sourceProviders {
			// 检查目标用户是否已有相同类型的认证提供商
			var existing system.SysAuthProvider
			err := tx.Where("user_id = ? AND provider = ?", req.TargetUserId, provider.Provider).First(&existing).Error
			if err == nil {
				// 已存在，跳过或处理冲突
				global.GVA_LOG.Warn("目标用户已存在相同类型的认证提供商",
					zap.String("provider", provider.Provider),
					zap.Uint("targetUserId", req.TargetUserId),
					zap.Uint("sourceUserId", sourceId))
				continue
			}

			// 更新认证提供商的用户ID
			provider.UserId = req.TargetUserId
			if err := tx.Save(&provider).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("迁移认证提供商失败: %w", err)
			}
			mergedCount++
		}

		// 删除源用户（可选，也可以标记为已合并）
		if err := tx.Delete(&system.SysUser{}, sourceId).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("删除源用户失败: %w", err)
		}
	}

	tx.Commit()

	return &systemRes.AccountMergeResponse{
		Success:      true,
		TargetUserId: req.TargetUserId,
		MergedCount:  mergedCount,
		Message:      fmt.Sprintf("成功合并%d个认证提供商", mergedCount),
	}, nil
}

// ensureWechatAuthProvider 确保用户有微信认证提供商记录
func (aps *AuthProviderService) ensureWechatAuthProvider(user *system.SysUser) {
	if user == nil {
		return
	}

	// 检查是否已经存在微信认证提供商记录
	var existing system.SysAuthProvider
	err := global.GVA_DB.Where("user_id = ? AND provider = ?", user.ID, system.ProviderTypeWechat).First(&existing).Error
	if err == nil {
		// 已存在，更新最后使用时间
		now := time.Now()
		existing.LastUsedAt = &now
		global.GVA_DB.Save(&existing)
		return
	}

	// 不存在，创建新记录
	var providerId string
	if user.McpcnWechatUnionid != "" {
		providerId = user.McpcnWechatUnionid
	} else {
		providerId = user.McpcnWechatOpenid
	}

	if providerId == "" {
		return // 用户没有微信信息，跳过
	}

	authProvider := &system.SysAuthProvider{
		UserId:     user.ID,
		Provider:   system.ProviderTypeWechat,
		ProviderId: providerId,
		OpenId:     user.McpcnWechatOpenid,
		UnionId:    user.McpcnWechatUnionid,
		Verified:   true,
	}
	now := time.Now()
	authProvider.LastUsedAt = &now

	global.GVA_DB.Create(authProvider)
}

// convertWechatToUnifiedResponse 将微信授权响应转换为统一登录响应
func (aps *AuthProviderService) convertWechatToUnifiedResponse(wechatResp *WechatAuthResponse) *systemRes.UnifiedLoginResponse {
	if wechatResp == nil {
		return nil
	}
	return &systemRes.UnifiedLoginResponse{
		User:      *wechatResp.User,
		Provider:  system.ProviderTypeWechat,
		IsNewUser: false, // 微信OAuth返回的都是已存在用户
		NeedsBind: false,
	}
}

// handleWechatBindingFlow 处理微信绑定流程
func (aps *AuthProviderService) handleWechatBindingFlow(wechatResp *WechatAuthResponse, req systemReq.UnifiedLoginRequest) (*systemRes.UnifiedLoginResponse, error) {
	if wechatResp.TempData == nil {
		return nil, errors.New("微信绑定数据不完整")
	}

	tempData := wechatResp.TempData
	return &systemRes.UnifiedLoginResponse{
		User:      system.SysUser{}, // 空用户对象
		Provider:  system.ProviderTypeWechat,
		IsNewUser: false,
		NeedsBind: true,
		TempData: &systemRes.WechatTempData{
			OpenID:     tempData.OpenID,
			UnionID:    tempData.UnionID,
			Nickname:   tempData.Nickname,
			HeadImgURL: tempData.HeadImgURL,
			State:      tempData.State,
		},
	}, nil
}

// generateWechatTempToken 生成微信临时令牌
func (aps *AuthProviderService) generateWechatTempToken(tempData *WechatTempData, req systemReq.UnifiedLoginRequest) string {
	// 生成包含微信绑定信息的临时令牌
	data := map[string]interface{}{
		"provider":   system.ProviderTypeWechat,
		"providerId": tempData.OpenID,
		"openId":     tempData.OpenID,
		"unionId":    tempData.UnionID,
		"nickname":   tempData.Nickname,
		"headImgUrl": tempData.HeadImgURL,
		"state":      tempData.State,
		"timestamp":  time.Now().Unix(),
		"expires":    time.Now().Add(30 * time.Minute).Unix(), // 30分钟过期
	}

	// 使用base64编码（生产环境应使用更安全的方式）
	jsonData, _ := json.Marshal(data)
	return base64.StdEncoding.EncodeToString(jsonData)
}

func (aps *AuthProviderService) BindWechatPhone(req systemReq.WechatBindPhoneRequest, deviceID *string, ipAddress, userAgent string) (*systemRes.UnifiedLoginResponse, error) {
	// 1. 校验验证码
	if !aps.VerifyPhoneCode(req.Phone, req.Code) {
		return nil, errors.New("验证码错误或已过期")
	}

	// 2. 检查该微信是否已绑定其他账号
	var existingWechatProvider system.SysAuthProvider
	err := global.GVA_DB.Where("provider = ? AND union_id = ?", system.ProviderTypeWechat, req.UnionId).First(&existingWechatProvider).Error
	if err == nil {
		return nil, errors.New("该微信已绑定其他账号")
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询微信绑定状态失败: %w", err)
	}

	// 3. 根据手机号查找用户
	var user system.SysUser
	err = global.GVA_DB.Where("phone = ?", req.Phone).Preload("Authorities").Preload("Authority").First(&user).Error

	var isNewUser bool
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 用户不存在，创建新用户
		isNewUser = true

		// 下载微信头像并上传到OSS
		ossAvatarURL, err := aps.DownloadAndUploadWechatAvatar(req.Avatar)
		if err != nil {
			// 如果头像转存失败，记录错误但不影响用户创建，使用原始URL
			global.GVA_LOG.Warn("微信头像转存OSS失败，使用原始URL",
				zap.String("原始URL", req.Avatar),
				zap.Error(err))
			ossAvatarURL = req.Avatar
		}

		user = system.SysUser{
			UUID:        uuid.New(),
			Username:    req.Phone,
			NickName:    req.NickName,
			HeaderImg:   ossAvatarURL, // 使用OSS URL或原始URL
			Phone:       req.Phone,
			AuthorityId: 666,
			Enable:      1,
		}

		// 设置默认权限
		var authorities []system.SysAuthority
		authorities = append(authorities, system.SysAuthority{
			AuthorityId: 666,
		})
		user.Authorities = authorities

		if err := global.GVA_DB.Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建用户失败: %w", err)
		}

		// 为新用户创建手机号认证提供商记录
		phoneAuthProvider := &system.SysAuthProvider{
			UserId:     user.ID,
			Provider:   system.ProviderTypePhone,
			ProviderId: req.Phone,
			Phone:      req.Phone,
			Verified:   true,
		}
		now := time.Now()
		phoneAuthProvider.LastUsedAt = &now

		if err := global.GVA_DB.Create(phoneAuthProvider).Error; err != nil {
			return nil, fmt.Errorf("创建手机号认证提供商记录失败: %w", err)
		}
	} else if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	} else {
		// 用户存在，检查是否已绑定微信
		var existingUserWechatProvider system.SysAuthProvider
		err = global.GVA_DB.Where("user_id = ? AND provider = ?", user.ID, system.ProviderTypeWechat).First(&existingUserWechatProvider).Error
		if err == nil {
			return nil, errors.New("该账号已绑定微信，无法重复绑定")
		}
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("查询用户微信绑定状态失败: %w", err)
		}
		// 用户存在且未绑定微信，可以继续绑定
		isNewUser = false

		// 如果用户没有头像或头像为空，尝试使用微信头像
		if user.HeaderImg == "" && req.Avatar != "" {
			ossAvatarURL, err := aps.DownloadAndUploadWechatAvatar(req.Avatar)
			if err != nil {
				global.GVA_LOG.Warn("为现有用户转存微信头像失败",
					zap.Uint("userId", user.ID),
					zap.String("原始URL", req.Avatar),
					zap.Error(err))
			} else {
				// 更新用户头像
				user.HeaderImg = ossAvatarURL
				if err := global.GVA_DB.Save(&user).Error; err != nil {
					global.GVA_LOG.Warn("更新用户头像失败",
						zap.Uint("userId", user.ID),
						zap.Error(err))
				}
			}
		}
	}

	// 4. 创建微信认证提供商记录
	wechatAuthProvider := system.SysAuthProvider{
		UserId:     user.ID,
		Provider:   system.ProviderTypeWechat,
		ProviderId: req.UnionId,
		OpenId:     req.OpenId,
		UnionId:    req.UnionId,
		Verified:   true,
	}
	now := time.Now()
	wechatAuthProvider.LastUsedAt = &now

	if err := global.GVA_DB.Create(&wechatAuthProvider).Error; err != nil {
		return nil, fmt.Errorf("绑定微信失败: %w", err)
	}

	// 5. 使用JwtService创建会话和token
	jwtService := &JwtService{}
	token, err := jwtService.CreateSessionAndToken(&user, deviceID, ipAddress, userAgent)
	if err != nil {
		return nil, fmt.Errorf("生成登录token失败: %w", err)
	}

	// 获取token过期时间
	j := utils.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		return nil, fmt.Errorf("解析token失败: %w", err)
	}

	return &systemRes.UnifiedLoginResponse{
		User:      user,
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix(),
		Provider:  system.ProviderTypeWechat,
		IsNewUser: isNewUser,
		NeedsBind: false,
	}, nil
}

// downloadAndUploadWechatAvatar 下载微信头像并上传到OSS
func (aps *AuthProviderService) DownloadAndUploadWechatAvatar(avatarURL string) (string, error) {
	if avatarURL == "" {
		return "", nil // 如果没有头像URL，返回空字符串
	}

	// 1. 下载微信头像
	resp, err := http.Get(avatarURL)
	if err != nil {
		global.GVA_LOG.Error("下载微信头像失败", zap.String("url", avatarURL), zap.Error(err))
		return "", fmt.Errorf("下载微信头像失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("下载微信头像HTTP状态错误",
			zap.String("url", avatarURL),
			zap.Int("status", resp.StatusCode))
		return "", fmt.Errorf("下载微信头像失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 2. 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取微信头像数据失败", zap.Error(err))
		return "", fmt.Errorf("读取微信头像数据失败: %w", err)
	}

	// 3. 生成文件名
	// 从URL中提取文件扩展名，如果没有则默认使用.jpg
	ext := filepath.Ext(avatarURL)
	if ext == "" || !strings.Contains(ext, ".") {
		ext = ".jpg" // 微信头像通常是jpg格式
	}

	// 生成基础文件名
	baseFileName := fmt.Sprintf("wechat_avatar_%d_%s%s",
		time.Now().Unix(),
		uuid.New().String()[:8],
		ext)

	// 按照项目标准格式构造完整路径：BasePath/uploads/headerImg/日期/文件名
	// 遵循 aliyun_oss.go 中的路径格式
	var fileName string
	if global.GVA_CONFIG.AliyunOSS.BasePath == "" {
		fileName = fmt.Sprintf("uploads/headerImg/%s/%s",
			time.Now().Format("2006-01-02"),
			baseFileName)
	} else {
		fileName = fmt.Sprintf("%s/uploads/headerImg/%s/%s",
			global.GVA_CONFIG.AliyunOSS.BasePath,
			time.Now().Format("2006-01-02"),
			baseFileName)
	}

	global.GVA_LOG.Info("生成微信头像文件路径",
		zap.String("fileName", fileName),
		zap.String("basePath", global.GVA_CONFIG.AliyunOSS.BasePath))

	// 4. 上传到OSS
	oss := upload.NewOss()
	ossURL, _, err := oss.UploadBytes(imageData, fileName)
	if err != nil {
		global.GVA_LOG.Error("上传微信头像到OSS失败",
			zap.String("fileName", fileName),
			zap.String("avatarURL", avatarURL),
			zap.Error(err))
		return "", fmt.Errorf("上传微信头像到OSS失败: %w", err)
	}

	global.GVA_LOG.Info("微信头像转存OSS成功",
		zap.String("原始URL", avatarURL),
		zap.String("OSS URL", ossURL))

	return ossURL, nil
}

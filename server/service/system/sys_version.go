package system

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
)

type SysVersionService struct{}

// CreateSysVersion 创建系统版本记录
// Author [yourname](https://github.com/yourname)
func (sysVersionService *SysVersionService) CreateSysVersion(ctx context.Context, sysVersion *system.SysVersion) (err error) {
	err = global.GVA_DB.Create(sysVersion).Error
	return err
}

// DeleteSysVersion 删除系统版本记录
// Author [yourname](https://github.com/yourname)
func (sysVersionService *SysVersionService) DeleteSysVersion(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&system.SysVersion{}, "id = ?", ID).Error
	return err
}

// DeleteSysVersionByIds 批量删除系统版本记录
// Author [yourname](https://github.com/yourname)
func (sysVersionService *SysVersionService) DeleteSysVersionByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]system.SysVersion{}, "id in ?", IDs).Error
	return err
}

// UpdateSysVersion 更新系统版本记录
// Author [yourname](https://github.com/yourname)
func (sysVersionService *SysVersionService) UpdateSysVersion(ctx context.Context, sysVersion system.SysVersion) (err error) {
	err = global.GVA_DB.Model(&system.SysVersion{}).Where("id = ?", sysVersion.ID).Updates(&sysVersion).Error
	return err
}

// GetSysVersion 根据ID获取系统版本记录
// Author [yourname](https://github.com/yourname)
func (sysVersionService *SysVersionService) GetSysVersion(ctx context.Context, ID string) (sysVersion system.SysVersion, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&sysVersion).Error
	return
}

// GetSysVersionInfoList 分页获取系统版本记录
// Author [yourname](https://github.com/yourname)
func (sysVersionService *SysVersionService) GetSysVersionInfoList(ctx context.Context, info systemReq.SysVersionSearch) (list []system.SysVersion, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&system.SysVersion{})
	var sysVersions []system.SysVersion
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.OsType != nil && *info.OsType != "" {
		db = db.Where("os_type = ?", *info.OsType)
	}
	if info.Version != nil && *info.Version != "" {
		db = db.Where("version LIKE ?", "%"+*info.Version+"%")
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&sysVersions).Error
	return sysVersions, total, err
}
func (sysVersionService *SysVersionService) GetSysVersionPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

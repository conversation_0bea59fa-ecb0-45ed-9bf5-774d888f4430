package system

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

const (
	TokenPrefix = "TOKEN:" // Redis中存储token的key前缀
)

type JwtService struct{}

var JwtServiceApp = new(JwtService)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: JsonInBlacklist
//@description: 拉黑jwt
//@param: jwtList model.JwtBlacklist
//@return: err error

func (jwtService *JwtService) JsonInBlacklist(jwtList system.JwtBlacklist) (err error) {
	// 先检查缓存中是否已存在
	if _, exists := global.BlackCache.Get(jwtList.Jwt); exists {
		global.GVA_LOG.Debug("Token已在黑名单中，跳过重复插入", zap.String("token", jwtList.Jwt))
		return nil
	}

	// 尝试插入数据库，使用ON DUPLICATE KEY处理重复插入
	err = global.GVA_DB.Create(&jwtList).Error
	if err != nil {
		// 检查是否是重复键错误
		if strings.Contains(strings.ToLower(err.Error()), "duplicate") ||
			strings.Contains(strings.ToLower(err.Error()), "unique") {
			global.GVA_LOG.Debug("Token已在数据库黑名单中，跳过重复插入", zap.String("token", jwtList.Jwt))
			// 重复插入不算错误，只需要更新缓存
			global.BlackCache.SetDefault(jwtList.Jwt, struct{}{})
			return nil
		}
		return
	}
	global.BlackCache.SetDefault(jwtList.Jwt, struct{}{})
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: IsBlacklist
//@description: 判断JWT是否在黑名单内部
//@param: jwt string
//@return: bool

func (jwtService *JwtService) IsBlacklist(jwt string) bool {
	// 首先检查缓存
	_, ok := global.BlackCache.Get(jwt)
	if ok {
		return true
	}

	// 如果缓存中没有，检查数据库
	err := global.GVA_DB.Where("jwt = ?", jwt).First(&system.JwtBlacklist{}).Error
	isNotFound := errors.Is(err, gorm.ErrRecordNotFound)
	isBlacklisted := !isNotFound

	// 如果在数据库中找到，同步到缓存
	if isBlacklisted {
		global.BlackCache.SetDefault(jwt, struct{}{})
	}

	return isBlacklisted
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetRedisJWT
//@description: 从redis取jwt
//@param: userName string
//@return: redisJWT string, err error

func (jwtService *JwtService) GetRedisJWT(userName string) (redisJWT string, err error) {
	redisJWT, err = global.GVA_REDIS.Get(context.Background(), TokenPrefix+userName).Result()
	return redisJWT, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetRedisJWT
//@description: jwt存入redis并设置过期时间
//@param: jwt string, userName string
//@return: err error

func (jwtService *JwtService) SetRedisJWT(jwt string, userName string) (err error) {
	// 此处过期时间等于jwt过期时间
	dr, err := utils.ParseDuration(global.GVA_CONFIG.JWT.ExpiresTime)
	if err != nil {
		return err
	}
	timer := dr
	err = global.GVA_REDIS.Set(context.Background(), TokenPrefix+userName, jwt, timer).Err()
	return err
}

func LoadAll() {
	var data []string
	err := global.GVA_DB.Model(&system.JwtBlacklist{}).Select("jwt").Find(&data).Error
	if err != nil {
		global.GVA_LOG.Error("加载数据库jwt黑名单失败!", zap.Error(err))
		return
	}
	for i := 0; i < len(data); i++ {
		global.BlackCache.SetDefault(data[i], struct{}{})
	} // jwt黑名单 加入 BlackCache 中
}

//@author: [gin-vue-admin]
//@function: VerifyToken
//@description: 验证token是否有效
//@param: token string
//@return: claims *request.CustomClaims, valid bool, err error

func (jwtService *JwtService) VerifyToken(token string) (*utils.JWT, error) {
	// 检查token是否在黑名单中
	if jwtService.IsBlacklist(token) {
		return nil, utils.TokenInvalid
	}

	// 验证会话是否有效
	_, err := UserSessionServiceApp.ValidateSession(token)
	if err != nil {
		global.GVA_LOG.Warn("会话验证失败", zap.String("token", token), zap.Error(err))
		// 将无效token加入黑名单
		blackJWT := system.JwtBlacklist{Jwt: token}
		_ = jwtService.JsonInBlacklist(blackJWT)
		return nil, utils.TokenInvalid
	}

	// 更新会话活跃时间
	_ = UserSessionServiceApp.UpdateSessionActivity(token)

	// 解析token
	j := utils.NewJWT()
	return j, nil
}

//@author: [gin-vue-admin]
//@function: CreateSessionAndToken
//@description: 创建用户会话和JWT token
//@param: user *system.SysUser, deviceID *string, ipAddress string, userAgent string
//@return: token string, err error

func (jwtService *JwtService) CreateSessionAndToken(user *system.SysUser, deviceID *string, ipAddress, userAgent string) (string, error) {
	// 生成JWT token
	token, claims, err := utils.LoginToken(user)
	if err != nil {
		return "", err
	}

	// 构建会话信息
	sessionInfo, err := UserSessionServiceApp.GetSessionInfo(user.ID, deviceID, ipAddress, userAgent)
	if err != nil {
		return "", err
	}

	// 创建用户会话
	err = UserSessionServiceApp.CreateSession(user.ID, token, sessionInfo, time.Unix(claims.RegisteredClaims.ExpiresAt.Unix(), 0))
	if err != nil {
		return "", err
	}

	// 如果启用了多点登录控制，更新Redis中的JWT
	if global.GVA_CONFIG.System.UseMultipoint {
		err = jwtService.SetRedisJWT(token, user.Username)
		if err != nil {
			// Redis失败不影响登录，仅记录日志
			global.GVA_LOG.Error("设置Redis JWT失败", zap.Error(err))
		}
	}

	return token, nil
}

//@author: [gin-vue-admin]
//@function: RefreshTokenByDevice
//@description: 通过设备ID和token刷新token，如果token还有效则直接返回原token
//@param: deviceID string, token string
//@return: newToken string, err error

func (jwtService *JwtService) RefreshTokenByDevice(deviceID, token string) (string, error) {
	j := utils.NewJWT()

	// 首先验证设备会话是否允许刷新Token
	err := UserSessionServiceApp.ValidateDeviceSession(deviceID, token)
	if err != nil {
		return "", err
	}

	// 检查token是否还有效，如果有效且不在黑名单中，直接返回原token
	claims, err := j.ParseToken(token)
	if err == nil && claims != nil && !jwtService.IsBlacklist(token) {
		// 更新会话活跃时间
		_ = UserSessionServiceApp.UpdateSessionActivity(token)
		return token, nil
	}

	// 如果token无效或过期，继续刷新逻辑
	// 验证设备ID是否存在，并获取关联的用户
	var userID uint
	err = global.GVA_DB.Raw("SELECT user_id FROM devices WHERE device_id = ? AND status = ? AND user_id IS NOT NULL",
		deviceID, 1).Scan(&userID).Error
	if err != nil {
		return "", err
	}

	if userID == 0 {
		return "", errors.New("设备不存在或未关联用户")
	}

	// 重新解析token以获取claims（处理过期情况）
	claims, err = j.ParseToken(token)

	// 如果是过期错误，尝试使用ParseExpiredToken方法解析
	if errors.Is(err, utils.TokenExpired) {
		claims, err = j.ParseExpiredToken(token)
		if err != nil {
			return "", err
		}
	} else if err != nil {
		// 如果不是过期错误，则返回错误
		return "", err
	}

	// 验证claims是否有效
	if claims == nil {
		return "", utils.TokenInvalid
	}

	// 验证token中的用户信息与设备关联的用户信息是否匹配
	if claims.BaseClaims.ID != userID {
		return "", errors.New("token用户与设备关联用户不匹配")
	}

	// 创建新的token
	newClaims := j.CreateClaims(claims.BaseClaims)
	newToken, err := j.CreateToken(newClaims)
	if err != nil {
		return "", err
	}

	// 计算新token的过期时间
	newExpiresAt := time.Unix(newClaims.RegisteredClaims.ExpiresAt.Unix(), 0)

	// 更新会话token
	err = UserSessionServiceApp.UpdateSessionToken(token, newToken, newExpiresAt)
	if err != nil {
		return "", err
	}

	// 如果启用了多点登录控制，更新Redis中的JWT
	if global.GVA_CONFIG.System.UseMultipoint {
		err = jwtService.SetRedisJWT(newToken, claims.BaseClaims.Username)
		if err != nil {
			global.GVA_LOG.Error("更新Redis JWT失败", zap.Error(err))
		}
	}

	return newToken, nil
}

//@author: [gin-vue-admin]
//@function: RefreshTokenByWeb
//@description: 网站登录token刷新
//@param: token string
//@return: newToken string, err error

func (jwtService *JwtService) RefreshTokenByWeb(token string) (string, error) {
	j := utils.NewJWT()

	// 验证会话是否有效
	session, err := UserSessionServiceApp.ValidateSession(token)
	if err != nil {
		return "", err
	}

	// 检查是否为网站会话
	if session.SessionType != system.SessionTypeWeb {
		return "", errors.New("非网站会话无法使用此方法刷新")
	}

	// 检查token是否还有效，如果有效且不在黑名单中，直接返回原token
	claims, err := j.ParseToken(token)
	if err == nil && claims != nil && !jwtService.IsBlacklist(token) {
		// 更新会话活跃时间
		_ = UserSessionServiceApp.UpdateSessionActivity(token)
		return token, nil
	}

	// Token过期或无效，进行刷新
	// 重新解析token以获取claims（处理过期情况）
	claims, err = j.ParseToken(token)
	if errors.Is(err, utils.TokenExpired) {
		claims, err = j.ParseExpiredToken(token)
		if err != nil {
			return "", err
		}
	} else if err != nil {
		return "", err
	}

	// 验证claims是否有效
	if claims == nil {
		return "", utils.TokenInvalid
	}

	// 创建新的token
	newClaims := j.CreateClaims(claims.BaseClaims)
	newToken, err := j.CreateToken(newClaims)
	if err != nil {
		return "", err
	}

	// 计算新token的过期时间
	newExpiresAt := time.Unix(newClaims.RegisteredClaims.ExpiresAt.Unix(), 0)

	// 更新会话token
	err = UserSessionServiceApp.UpdateSessionToken(token, newToken, newExpiresAt)
	if err != nil {
		return "", err
	}

	// 如果启用了多点登录控制，更新Redis中的JWT
	if global.GVA_CONFIG.System.UseMultipoint {
		err = jwtService.SetRedisJWT(newToken, claims.BaseClaims.Username)
		if err != nil {
			global.GVA_LOG.Error("更新Redis JWT失败", zap.Error(err))
		}
	}

	return newToken, nil
}

//@author: [gin-vue-admin]
//@function: LogoutSession
//@description: 注销指定会话
//@param: token string
//@return: err error

func (jwtService *JwtService) LogoutSession(token string) error {
	// 停用会话
	err := UserSessionServiceApp.DeactivateSession(token)
	if err != nil {
		return err
	}

	return nil
}

//@author: [gin-vue-admin]
//@function: LogoutAllSessions
//@description: 注销用户的所有会话
//@param: userID uint, excludeToken string
//@return: err error

func (jwtService *JwtService) LogoutAllSessions(userID uint, excludeToken string) error {
	// 停用用户的所有设备会话（排除当前token）
	err := UserSessionServiceApp.DeactivateUserSessions(userID, system.SessionTypeDevice, excludeToken)
	if err != nil {
		return err
	}

	// 停用用户的所有网站会话（排除当前token）
	err = UserSessionServiceApp.DeactivateUserSessions(userID, system.SessionTypeWeb, excludeToken)
	if err != nil {
		return err
	}

	return nil
}

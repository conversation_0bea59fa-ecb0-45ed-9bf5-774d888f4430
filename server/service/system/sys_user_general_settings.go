package system

import (
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"gorm.io/gorm"
)

type UserGeneralSettingsService struct{}

var UserGeneralSettingsServiceApp = new(UserGeneralSettingsService)

// CreateUserGeneralSettings 创建用户通用设置记录
func (userGeneralSettingsService *UserGeneralSettingsService) CreateUserGeneralSettings(userGeneralSettings *system.UserGeneralSettings) (err error) {
	err = global.GVA_DB.Create(userGeneralSettings).Error
	return err
}

// DeleteUserGeneralSettings 删除用户通用设置记录
func (userGeneralSettingsService *UserGeneralSettingsService) DeleteUserGeneralSettings(ID string) (err error) {
	err = global.GVA_DB.Delete(&system.UserGeneralSettings{}, "id = ?", ID).Error
	return err
}

// DeleteUserGeneralSettingsByIds 批量删除用户通用设置记录
func (userGeneralSettingsService *UserGeneralSettingsService) DeleteUserGeneralSettingsByIds(IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]system.UserGeneralSettings{}, "id in ?", IDs).Error
	return err
}

// UpdateUserGeneralSettings 更新用户通用设置记录
func (userGeneralSettingsService *UserGeneralSettingsService) UpdateUserGeneralSettings(userGeneralSettings system.UserGeneralSettings) (err error) {
	err = global.GVA_DB.Model(&system.UserGeneralSettings{}).Where("id = ?", userGeneralSettings.ID).Updates(&userGeneralSettings).Error
	return err
}

// GetUserGeneralSettings 根据ID获取用户通用设置记录
func (userGeneralSettingsService *UserGeneralSettingsService) GetUserGeneralSettings(ID string) (userGeneralSettings system.UserGeneralSettings, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&userGeneralSettings).Error
	return
}

// GetUserGeneralSettingsByUserID 根据用户ID获取用户通用设置记录
func (userGeneralSettingsService *UserGeneralSettingsService) GetUserGeneralSettingsByUserID(userID uint) (userGeneralSettings system.UserGeneralSettings, err error) {
	err = global.GVA_DB.Where("user_id = ?", userID).First(&userGeneralSettings).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果记录不存在，创建默认设置
		userGeneralSettings = system.UserGeneralSettings{
			UserID:                 userID,
			AutoLaunch:             false,
			ShowMenuIcon:           true,
			LaunchShortcut:         "⌘ 点两次",
			Language:               "zh-CN",
			DisplayScreen:          "鼠标所在屏幕",
			ThemeMode:              "system",
			WindowStyle:            "full",
			AccessibilityEnabled:   false,
			ScreenRecordingEnabled: false,
			FullDiskAccessEnabled:  false,
		}
		err = userGeneralSettingsService.CreateUserGeneralSettings(&userGeneralSettings)
	}
	return
}

// UpdateUserGeneralSettingsByUserID 根据用户ID更新用户通用设置
func (userGeneralSettingsService *UserGeneralSettingsService) UpdateUserGeneralSettingsByUserID(userID uint, req systemReq.UpdateUserGeneralSettingsReq) (err error) {
	updates := make(map[string]interface{})

	if req.AutoLaunch != nil {
		updates["auto_launch"] = *req.AutoLaunch
	}
	if req.ShowMenuIcon != nil {
		updates["show_menu_icon"] = *req.ShowMenuIcon
	}
	if req.LaunchShortcut != nil {
		updates["launch_shortcut"] = *req.LaunchShortcut
	}
	if req.Language != nil {
		updates["language"] = *req.Language
	}
	if req.DisplayScreen != nil {
		updates["display_screen"] = *req.DisplayScreen
	}
	if req.ThemeMode != nil {
		updates["theme_mode"] = *req.ThemeMode
	}
	if req.WindowStyle != nil {
		updates["window_style"] = *req.WindowStyle
	}

	if len(updates) == 0 {
		return errors.New("没有需要更新的字段")
	}

	err = global.GVA_DB.Model(&system.UserGeneralSettings{}).Where("user_id = ?", userID).Updates(updates).Error
	return err
}

// UpdatePermissionsByUserID 根据用户ID更新权限状态
func (userGeneralSettingsService *UserGeneralSettingsService) UpdatePermissionsByUserID(userID uint, req systemReq.UpdatePermissionsReq) (err error) {
	updates := make(map[string]interface{})

	if req.AccessibilityEnabled != nil {
		updates["accessibility_enabled"] = *req.AccessibilityEnabled
	}
	if req.ScreenRecordingEnabled != nil {
		updates["screen_recording_enabled"] = *req.ScreenRecordingEnabled
	}
	if req.FullDiskAccessEnabled != nil {
		updates["full_disk_access_enabled"] = *req.FullDiskAccessEnabled
	}

	if len(updates) == 0 {
		return errors.New("没有需要更新的权限字段")
	}

	err = global.GVA_DB.Model(&system.UserGeneralSettings{}).Where("user_id = ?", userID).Updates(updates).Error
	return err
}

// GetUserGeneralSettingsInfoList 分页获取用户通用设置记录
func (userGeneralSettingsService *UserGeneralSettingsService) GetUserGeneralSettingsInfoList(info systemReq.UserGeneralSettingsSearch) (list []system.UserGeneralSettings, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&system.UserGeneralSettings{})
	var userGeneralSettingss []system.UserGeneralSettings
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.UserID != 0 {
		db = db.Where("user_id = ?", info.UserID)
	}
	if info.Language != "" {
		db = db.Where("language = ?", info.Language)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&userGeneralSettingss).Error
	return userGeneralSettingss, total, err
}

// GetGeneralSettingsOptions 获取通用设置选项
func (userGeneralSettingsService *UserGeneralSettingsService) GetGeneralSettingsOptions() systemRes.GeneralSettingsOptionsResponse {
	return systemRes.GeneralSettingsOptionsResponse{
		Languages: []systemRes.OptionItem{
			{Code: "zh-CN", Name: "简体中文"},
			{Code: "zh-TW", Name: "繁體中文"},
			{Code: "en-US", Name: "English"},
			{Code: "ja-JP", Name: "日本語"},
			{Code: "ko-KR", Name: "한국어"},
		},
		ThemeModes: []systemRes.OptionItem{
			{Code: "light", Name: "明亮"},
			{Code: "dark", Name: "黑暗"},
			{Code: "system", Name: "跟随系统"},
		},
		WindowStyles: []systemRes.OptionItem{
			{Code: "full", Name: "完整"},
			{Code: "simple", Name: "简约"},
			{Code: "right-sidebar", Name: "右边栏"},
			{Code: "left-sidebar", Name: "左边栏"},
		},
		DisplayScreens: []systemRes.OptionItem{
			{Code: "mouse-screen", Name: "鼠标所在屏幕"},
			{Code: "main-screen", Name: "主屏幕"},
			{Code: "custom-screen", Name: "指定屏幕"},
		},
	}
}

package system

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type UserSessionService struct{}

var UserSessionServiceApp = new(UserSessionService)

// SessionInfo 会话信息
type SessionInfo struct {
	SessionType string  `json:"sessionType"` // 会话类型
	DeviceID    *string `json:"deviceId"`    // 设备ID
	IPAddress   string  `json:"ipAddress"`   // IP地址
	UserAgent   string  `json:"userAgent"`   // 用户代理
	DeviceName  string  `json:"deviceName"`  // 设备名称
	OSInfo      string  `json:"osInfo"`      // 操作系统信息
	AppVersion  string  `json:"appVersion"`  // 应用版本
}

// CreateSession 创建用户会话
func (sessionService *UserSessionService) CreateSession(userID uint, token string, sessionInfo SessionInfo, expiresAt time.Time) error {
	now := time.Now()

	// 获取缓冲时间配置
	bufferTime, parseErr := utils.ParseDuration(global.GVA_CONFIG.JWT.BufferTime)
	if parseErr != nil {
		global.GVA_LOG.Error("解析JWT缓冲时间失败", zap.Error(parseErr))
		bufferTime = 30 * 24 * time.Hour // 默认30*24小时缓冲时间
	}

	// 计算缓冲时间截止时间：token过期时间 + 缓冲时间
	bufferAt := expiresAt.Add(bufferTime)

	// 创建新会话记录
	userSession := system.SysUserSession{
		UserID:       userID,
		DeviceID:     sessionInfo.DeviceID,
		SessionType:  sessionInfo.SessionType,
		Token:        token,
		IPAddress:    sessionInfo.IPAddress,
		UserAgent:    sessionInfo.UserAgent,
		LoginAt:      now,
		LastActiveAt: now,
		ExpiresAt:    expiresAt,
		BufferAt:     bufferAt,
		IsActive:     true,
		DeviceName:   sessionInfo.DeviceName,
		OSInfo:       sessionInfo.OSInfo,
		AppVersion:   sessionInfo.AppVersion,
	}

	// 开启事务，将登录限制检查和会话创建放在同一个事务中
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 在事务内检查登录限制并处理超出限制的情况
		if err := sessionService.checkLoginLimitInTransaction(tx, userID, sessionInfo.SessionType, sessionInfo.DeviceID); err != nil {
			return err
		}

		// 创建会话记录
		if err := tx.Create(&userSession).Error; err != nil {
			return fmt.Errorf("创建用户会话失败: %v", err)
		}

		global.GVA_LOG.Info("用户会话创建成功",
			zap.Uint("userID", userID),
			zap.String("sessionType", sessionInfo.SessionType),
			zap.Any("deviceID", sessionInfo.DeviceID))

		return nil
	})
}

// checkLoginLimitInTransaction 在事务内检查登录限制并处理超出限制的情况
func (sessionService *UserSessionService) checkLoginLimitInTransaction(tx *gorm.DB, userID uint, sessionType string, deviceID *string) error {
	var maxAllowed int
	var sessionTypeFilter string

	// 设置限制参数
	if sessionType == system.SessionTypeDevice {
		maxAllowed = system.MaxDeviceLogins
		sessionTypeFilter = system.SessionTypeDevice
	} else {
		maxAllowed = system.MaxWebLogins
		sessionTypeFilter = system.SessionTypeWeb
	}

	// 在事务内查询当前活跃会话，使用行锁防止并发问题
	var activeSessions []system.SysUserSession
	err := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("user_id = ? AND session_type = ? AND is_active = ?",
			userID, sessionTypeFilter, true).
		Order("last_active_at ASC").Find(&activeSessions).Error

	if err != nil {
		return fmt.Errorf("查询用户活跃会话失败: %v", err)
	}

	// 如果是设备登录，检查是否已经在同一设备登录
	if sessionType == system.SessionTypeDevice && deviceID != nil {
		for _, session := range activeSessions {
			if session.DeviceID != nil && *session.DeviceID == *deviceID {
				// 同一设备已经登录，需要在事务内踢出旧会话
				if err := sessionService.deactivateSessionInTransaction(tx, session.Token); err != nil {
					global.GVA_LOG.Error("踢出同设备旧会话失败", zap.Error(err))
					return fmt.Errorf("踢出同设备旧会话失败: %v", err)
				}
			}
		}
		// 重新查询活跃会话（排除刚才踢出的）
		err = tx.Set("gorm:query_option", "FOR UPDATE").
			Where("user_id = ? AND session_type = ? AND is_active = ? ",
				userID, sessionTypeFilter, true).
			Order("last_active_at ASC").Find(&activeSessions).Error
		if err != nil {
			return fmt.Errorf("重新查询用户活跃会话失败: %v", err)
		}
	}

	// 检查是否超出限制
	if len(activeSessions) >= maxAllowed {
		// 踢出最早的会话
		sessionsToRemove := len(activeSessions) - maxAllowed + 1
		for i := 0; i < sessionsToRemove; i++ {
			if err := sessionService.deactivateSessionInTransaction(tx, activeSessions[i].Token); err != nil {
				global.GVA_LOG.Error("踢出超限会话失败",
					zap.String("token", activeSessions[i].Token),
					zap.Error(err))
				return fmt.Errorf("踢出超限会话失败: %v", err)
			}
		}
	}

	return nil
}

// checkLoginLimit 检查登录限制并处理超出限制的情况（保留原方法用于其他地方调用）
func (sessionService *UserSessionService) checkLoginLimit(userID uint, sessionType string, deviceID *string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		return sessionService.checkLoginLimitInTransaction(tx, userID, sessionType, deviceID)
	})
}

// ValidateSession 验证会话是否有效
func (sessionService *UserSessionService) ValidateSession(token string) (*system.SysUserSession, error) {
	var session system.SysUserSession
	err := global.GVA_DB.Where("token = ? AND is_active = ? AND buffer_at > ?",
		token, true, time.Now()).First(&session).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("会话不存在或已过期")
		}
		return nil, fmt.Errorf("查询会话失败: %v", err)
	}

	return &session, nil
}

// UpdateSessionActivity 更新会话活跃时间
func (sessionService *UserSessionService) UpdateSessionActivity(token string) error {
	err := global.GVA_DB.Model(&system.SysUserSession{}).
		Where("token = ? AND is_active = ?", token, true).
		Update("last_active_at", time.Now()).Error

	if err != nil {
		return fmt.Errorf("更新会话活跃时间失败: %v", err)
	}

	return nil
}

// deactivateSessionInTransaction 在事务内停用会话
func (sessionService *UserSessionService) deactivateSessionInTransaction(tx *gorm.DB, token string) error {
	// 在事务内将会话标记为非活跃
	err := tx.Model(&system.SysUserSession{}).
		Where("token = ?", token).
		Updates(map[string]interface{}{
			"is_active":  false,
			"updated_at": time.Now(),
		}).Error

	if err != nil {
		return fmt.Errorf("停用会话失败: %v", err)
	}

	// 将token加入黑名单
	blackJWT := system.JwtBlacklist{Jwt: token}
	blacklistErr := JwtServiceApp.JsonInBlacklist(blackJWT)
	if blacklistErr != nil {
		global.GVA_LOG.Warn("将token加入黑名单失败",
			zap.String("token", token),
			zap.Error(blacklistErr))
		// 黑名单失败不影响会话停用
	}

	global.GVA_LOG.Info("会话已停用", zap.String("token", token))
	return nil
}

// DeactivateSession 停用会话
func (sessionService *UserSessionService) DeactivateSession(token string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		return sessionService.deactivateSessionInTransaction(tx, token)
	})
}

// DeactivateUserSessions 停用用户的指定类型会话
func (sessionService *UserSessionService) DeactivateUserSessions(userID uint, sessionType string, excludeToken string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var sessions []system.SysUserSession

		// 在事务内查询需要停用的会话
		query := tx.Where("user_id = ? AND session_type = ? AND is_active = ?", userID, sessionType, true)
		if excludeToken != "" {
			query = query.Where("token != ?", excludeToken)
		}

		err := query.Find(&sessions).Error
		if err != nil {
			return fmt.Errorf("查询用户会话失败: %v", err)
		}

		// 在事务内批量停用会话
		for _, session := range sessions {
			if err := sessionService.deactivateSessionInTransaction(tx, session.Token); err != nil {
				global.GVA_LOG.Error("停用用户会话失败",
					zap.Uint("userID", userID),
					zap.String("token", session.Token),
					zap.Error(err))
				return fmt.Errorf("停用用户会话失败: %v", err)
			}
		}

		return nil
	})
}

// ValidateDeviceSession 验证设备会话是否允许刷新Token
func (sessionService *UserSessionService) ValidateDeviceSession(deviceID string, token string) error {
	now := time.Now()

	// 判断设备id
	if deviceID == "" {
		//打印日志
		global.GVA_LOG.Error("设备ID为空，无法刷新Token")
		return errors.New("设备ID为空，无法刷新Token")
	} else {
		//打印日志
		global.GVA_LOG.Info("设备ID", zap.String("deviceID", deviceID))
	}

	// 判断token
	if token == "" {
		global.GVA_LOG.Error("Token为空，无法刷新Token")
		return errors.New("Token为空，无法刷新Token")
	} else {
		//打印日志
		global.GVA_LOG.Info("Token", zap.String("token", token))
	}

	// 首先尝试查询活跃的会话
	var session system.SysUserSession
	err := global.GVA_DB.Where("token = ? AND device_id = ? AND session_type = ? AND is_active = ? AND buffer_at > ?",
		token, deviceID, system.SessionTypeDevice, true, now).First(&session).Error

	if err == nil {
		// 找到了活跃的会话，直接通过验证
		return sessionService.validateDeviceLoginLimit(session.UserID)
	}

	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("验证设备会话失败: %v", err)
	}

	// 未找到活跃的会话
	return errors.New("设备会话不存在或已超出刷新期限，无法刷新Token")
}

// validateDeviceLoginLimit 验证设备登录数量限制（提取的公共方法）
func (sessionService *UserSessionService) validateDeviceLoginLimit(userID uint) error {
	// 检查用户的设备登录数量限制
	var activeDeviceCount int64
	err := global.GVA_DB.Model(&system.SysUserSession{}).
		Where("user_id = ? AND session_type = ? AND is_active = ? AND buffer_at > ?",
			userID, system.SessionTypeDevice, true, time.Now()).
		Count(&activeDeviceCount).Error

	if err != nil {
		return fmt.Errorf("查询用户活跃设备数量失败: %v", err)
	}

	if activeDeviceCount > system.MaxDeviceLogins {
		return errors.New("用户设备登录数量超出限制，无法刷新Token")
	}

	return nil
}

// GetUserActiveSessions 获取用户的活跃会话列表
func (sessionService *UserSessionService) GetUserActiveSessions(userID uint) ([]system.SysUserSession, error) {
	var sessions []system.SysUserSession
	err := global.GVA_DB.Where("user_id = ? AND is_active = ? AND buffer_at > ?",
		userID, true, time.Now()).
		Order("last_active_at DESC").Find(&sessions).Error

	if err != nil {
		return nil, fmt.Errorf("查询用户活跃会话失败: %v", err)
	}

	return sessions, nil
}

// CleanExpiredSessions 清理过期会话
func (sessionService *UserSessionService) CleanExpiredSessions() error {
	// 查询过期的会话
	var expiredSessions []system.SysUserSession
	err := global.GVA_DB.Where("buffer_at <= ? AND is_active = ?", time.Now(), true).
		Find(&expiredSessions).Error

	if err != nil {
		return fmt.Errorf("查询过期会话失败: %v", err)
	}

	// 批量停用过期会话
	if len(expiredSessions) > 0 {
		var tokens []string
		for _, session := range expiredSessions {
			tokens = append(tokens, session.Token)
		}

		// 批量更新为非活跃状态
		err = global.GVA_DB.Model(&system.SysUserSession{}).
			Where("token IN ?", tokens).
			Updates(map[string]interface{}{
				"is_active":  false,
				"updated_at": time.Now(),
			}).Error

		if err != nil {
			return fmt.Errorf("批量停用过期会话失败: %v", err)
		}

		// 将tokens加入黑名单
		for _, token := range tokens {
			blackJWT := system.JwtBlacklist{Jwt: token}
			_ = JwtServiceApp.JsonInBlacklist(blackJWT)
		}

		global.GVA_LOG.Info("清理过期会话完成", zap.Int("count", len(expiredSessions)))
	}

	return nil
}

// UpdateSessionToken 更新会话Token（用于Token刷新）
func (sessionService *UserSessionService) UpdateSessionToken(oldToken, newToken string, newExpiresAt time.Time) error {
	// 先检查旧token是否已在黑名单中
	if JwtServiceApp.IsBlacklist(oldToken) {
		global.GVA_LOG.Warn("尝试刷新已在黑名单中的token", zap.String("token", oldToken))
		return fmt.Errorf("token已被撤销，无法刷新")
	}

	// 检查旧token对应的会话
	var session system.SysUserSession
	err := global.GVA_DB.Where("token = ? AND is_active = ?", oldToken, true).First(&session).Error
	if err != nil {
		global.GVA_LOG.Error("查询旧会话失败", zap.String("token", oldToken), zap.Error(err))
		return fmt.Errorf("查询旧会话失败: %v", err)
	}

	// 检查会话是否在有效期内（包括缓冲时间）
	if time.Now().After(session.BufferAt) {
		global.GVA_LOG.Warn("会话已超出缓冲时间，无法刷新",
			zap.String("token", oldToken),
			zap.Time("bufferAt", session.BufferAt))
		return fmt.Errorf("会话已过期，无法刷新token")
	}

	// 获取缓冲时间配置
	bufferTime, parseErr := utils.ParseDuration(global.GVA_CONFIG.JWT.BufferTime)
	if parseErr != nil {
		global.GVA_LOG.Error("解析JWT缓冲时间失败", zap.Error(parseErr))
		bufferTime = 30 * 24 * time.Hour // 默认30天缓冲时间
	}

	// 计算新的缓冲时间截止时间：token过期时间 + 缓冲时间
	newBufferAt := newExpiresAt.Add(bufferTime)

	// 在事务中更新会话token和将旧token加入黑名单
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 更新会话token和过期时间，并增加刷新次数
		err = tx.Model(&session).Updates(map[string]interface{}{
			"token":          newToken,
			"expires_at":     newExpiresAt,
			"buffer_at":      newBufferAt,
			"last_active_at": time.Now(),
			"updated_at":     time.Now(),
			"refresh_count":  gorm.Expr("refresh_count + 1"),
		}).Error

		if err != nil {
			global.GVA_LOG.Error("更新会话Token失败", zap.String("oldToken", oldToken), zap.Error(err))
			return fmt.Errorf("更新会话Token失败: %v", err)
		}

		// 将旧token加入黑名单
		blackJWT := system.JwtBlacklist{Jwt: oldToken}
		err = JwtServiceApp.JsonInBlacklist(blackJWT)
		if err != nil {
			global.GVA_LOG.Error("将旧token加入黑名单失败", zap.String("token", oldToken), zap.Error(err))
			// 黑名单失败不影响token刷新，只记录日志
		}

		global.GVA_LOG.Info("Token刷新成功",
			zap.String("oldToken", oldToken),
			zap.String("newToken", newToken),
			zap.Uint("userID", session.UserID))

		return nil
	})
}

// GetSessionInfo 从设备信息和请求信息构建会话信息
func (sessionService *UserSessionService) GetSessionInfo(userID uint, deviceID *string, ipAddress, userAgent string) (SessionInfo, error) {
	sessionInfo := SessionInfo{
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}

	if deviceID != nil && *deviceID != "" {
		sessionInfo.SessionType = system.SessionTypeDevice
		sessionInfo.DeviceID = deviceID

		// 从设备表获取设备信息
		var deviceRecord device.Device
		err := global.GVA_DB.Where("device_id = ?", *deviceID).First(&deviceRecord).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return SessionInfo{}, errors.New("设备不存在，无法登录")
			}
			return SessionInfo{}, fmt.Errorf("查询设备信息失败: %v", err)
		}

		sessionInfo.DeviceName = deviceRecord.DeviceName
		if sessionInfo.DeviceName == "" {
			sessionInfo.DeviceName = deviceRecord.Hostname
		}
		sessionInfo.OSInfo = fmt.Sprintf("%s %s", deviceRecord.OSName, deviceRecord.OSVersion)
		sessionInfo.AppVersion = deviceRecord.AppVersion
	} else {
		sessionInfo.SessionType = system.SessionTypeWeb
		sessionInfo.DeviceName = "Web Browser"
		sessionInfo.OSInfo = sessionService.parseOSFromUserAgent(userAgent)
	}

	return sessionInfo, nil
}

// parseOSFromUserAgent 从User-Agent解析操作系统信息
func (sessionService *UserSessionService) parseOSFromUserAgent(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	if strings.Contains(userAgent, "windows") {
		return "Windows"
	} else if strings.Contains(userAgent, "macintosh") || strings.Contains(userAgent, "mac os") {
		return "macOS"
	} else if strings.Contains(userAgent, "linux") {
		return "Linux"
	} else if strings.Contains(userAgent, "android") {
		return "Android"
	} else if strings.Contains(userAgent, "iphone") || strings.Contains(userAgent, "ipad") {
		return "iOS"
	}

	return "Unknown"
}

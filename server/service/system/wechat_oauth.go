package system

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type WechatOAuthService struct{}

// WechatUserInfo 微信用户信息
type WechatUserInfo struct {
	OpenID     string `json:"openid"`
	Nickname   string `json:"nickname"`
	Sex        int    `json:"sex"`
	Province   string `json:"province"`
	City       string `json:"city"`
	Country    string `json:"country"`
	HeadImgURL string `json:"headimgurl"`
	UnionID    string `json:"unionid"`
}

// WechatAccessToken 微信访问令牌
type WechatAccessToken struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	OpenID       string `json:"openid"`
	Scope        string `json:"scope"`
	UnionID      string `json:"unionid"`
}

// WechatAuthResponse 微信授权响应
type WechatAuthResponse struct {
	Action   string                 `json:"action"`   // login: 直接登录, bind: 需要绑定账号
	Message  string                 `json:"message"`  // 提示信息
	Data     map[string]interface{} `json:"data"`     // 额外数据
	Token    string                 `json:"token"`    // 登录成功时的token
	User     *system.SysUser        `json:"user"`     // 用户信息
	TempData *WechatTempData        `json:"tempData"` // 临时数据，用于绑定流程
}

// WechatTempData 微信临时数据
type WechatTempData struct {
	OpenID     string `json:"openid"`
	UnionID    string `json:"unionid"`
	Nickname   string `json:"nickname"`
	HeadImgURL string `json:"headimgurl"`
	State      string `json:"state"`
}

// WechatErrorResponse 微信错误响应
type WechatErrorResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// generateState 生成随机state参数
func (w *WechatOAuthService) GenerateState() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// validateState 验证state参数
func (w *WechatOAuthService) validateState(state string) bool {
	ctx := context.Background()
	result := global.GVA_REDIS.Get(ctx, "wechat_state:"+state)
	if result.Err() != nil {
		global.GVA_LOG.Error("验证微信state失败", zap.Error(result.Err()))
		return false
	}

	// 删除已使用的state
	global.GVA_REDIS.Del(ctx, "wechat_state:"+state)
	return true
}

// validateStateWithoutDelete 验证state参数但不删除
func (w *WechatOAuthService) ValidateStateWithoutDelete(state string) (string, error) {
	ctx := context.Background()
	result := global.GVA_REDIS.Get(ctx, "wechat_code:"+state)
	if result.Err() != nil {
		// 如果key不存在，返回空字符串而不是错误
		if result.Err().Error() == "redis: nil" {
			return "", nil
		}
		global.GVA_LOG.Error("获取微信code失败", zap.Error(result.Err()))
		return "", result.Err()
	}

	code := result.Val()
	return code, nil
}

// CacheCodeByState 将微信授权code缓存到Redis，以state为key
func (w *WechatOAuthService) CacheCodeByState(state, code string) error {
	ctx := context.Background()

	// 验证state是否有效
	stateKey := "wechat_state:" + state
	result := global.GVA_REDIS.Get(ctx, stateKey)
	if result.Err() != nil {
		global.GVA_LOG.Error("验证微信state失败", zap.Error(result.Err()))
		return fmt.Errorf("无效的state参数")
	}

	// 将code存储到Redis，设置3分钟过期
	codeKey := "wechat_code:" + state
	err := global.GVA_REDIS.Set(ctx, codeKey, code, 3*time.Minute).Err()
	if err != nil {
		global.GVA_LOG.Error("缓存微信code失败", zap.Error(err))
		return fmt.Errorf("缓存code失败: %v", err)
	}

	global.GVA_LOG.Info("成功缓存微信授权code",
		zap.String("state", state),
		zap.String("code", code))

	return nil
}

// GetCodeByState 根据state从Redis获取微信授权code
func (w *WechatOAuthService) GetCodeByState(state string) (string, error) {
	ctx := context.Background()
	codeKey := "wechat_code:" + state

	result := global.GVA_REDIS.Get(ctx, codeKey)
	if result.Err() != nil {
		// 如果key不存在，返回空字符串而不是错误
		if result.Err().Error() == "redis: nil" {
			return "", nil
		}
		global.GVA_LOG.Error("获取微信code失败", zap.Error(result.Err()))
		return "", result.Err()
	}

	code := result.Val()

	// 获取到code后，立即删除缓存（一次性使用）
	global.GVA_REDIS.Del(ctx, codeKey)

	global.GVA_LOG.Info("成功获取微信授权code",
		zap.String("state", state),
		zap.String("code", code))

	return code, nil
}

// getAccessToken 用code换取access_token
func (w *WechatOAuthService) getAccessToken(code string) (*WechatAccessToken, error) {
	config := global.GVA_CONFIG.WechatOAuth
	if config.AppID == "" || config.AppSecret == "" {
		return nil, fmt.Errorf("微信配置不完整，请检查AppID和AppSecret")
	}

	// 构建请求URL - 根据微信官方文档
	reqURL := fmt.Sprintf(
		"https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
		config.AppID,
		config.AppSecret,
		code,
	)

	global.GVA_LOG.Info("请求微信access_token", zap.String("url", reqURL))

	// 发起HTTP GET请求
	resp, err := http.Get(reqURL)
	if err != nil {
		global.GVA_LOG.Error("请求微信API失败", zap.Error(err))
		return nil, fmt.Errorf("请求微信API失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取响应失败", zap.Error(err))
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("微信API响应", zap.String("response", string(body)))

	// 先检查是否为错误响应
	var errorResp WechatErrorResponse
	if err := json.Unmarshal(body, &errorResp); err == nil && errorResp.ErrCode != 0 {
		global.GVA_LOG.Error("微信API返回错误",
			zap.Int("errcode", errorResp.ErrCode),
			zap.String("errmsg", errorResp.ErrMsg))
		return nil, fmt.Errorf("微信API错误 %d: %s", errorResp.ErrCode, errorResp.ErrMsg)
	}

	// 解析正常响应
	var token WechatAccessToken
	if err := json.Unmarshal(body, &token); err != nil {
		global.GVA_LOG.Error("解析access_token失败", zap.Error(err))
		return nil, fmt.Errorf("解析access_token失败: %v", err)
	}

	// 验证必要字段
	if token.AccessToken == "" {
		global.GVA_LOG.Error("access_token为空")
		return nil, fmt.Errorf("获取access_token失败，响应中access_token为空")
	}

	if token.OpenID == "" {
		global.GVA_LOG.Error("openid为空")
		return nil, fmt.Errorf("获取access_token失败，响应中openid为空")
	}

	global.GVA_LOG.Info("成功获取access_token",
		zap.String("openid", token.OpenID),
		zap.String("scope", token.Scope),
		zap.Int("expires_in", token.ExpiresIn))

	return &token, nil
}

// getUserInfo 获取用户信息（内部方法，调用标准接口）
func (w *WechatOAuthService) getUserInfo(token *WechatAccessToken) (*WechatUserInfo, error) {
	// 对于网站应用授权登录，scope应该是snsapi_login，可以获取详细信息
	// 如果是其他scope（如snsapi_base），只返回基本信息
	if token.Scope == "snsapi_base" {
		return &WechatUserInfo{
			OpenID:  token.OpenID,
			UnionID: token.UnionID,
		}, nil
	}

	// 使用标准方法获取详细用户信息
	return w.GetUserInfoByAccessToken(token.AccessToken, token.OpenID)
}

// findUserByWechat 通过微信信息查找用户
func (w *WechatOAuthService) findUserByWechat(openID, unionID string) (*system.SysUser, error) {
	//var user system.SysUser

	// 优先通过unionid查找（更准确）
	// if unionID != "" {
	// 	err := global.GVA_DB.Where("mcpcn_wechat_unionid = ?", unionID).First(&user).Error
	// 	if err == nil {
	// 		return &user, nil
	// 	}
	// 	if err != gorm.ErrRecordNotFound {
	// 		return nil, err
	// 	}
	// }

	// // 通过openid查找
	// if openID != "" {
	// 	err := global.GVA_DB.Where("mcpcn_wechat_openid = ?", openID).First(&user).Error
	// 	if err == nil {
	// 		return &user, nil
	// 	}
	// 	if err != gorm.ErrRecordNotFound {
	// 		return nil, err
	// 	}
	// }
	var authProvider system.SysAuthProvider
	err := global.GVA_DB.Where("provider = ? AND provider_id = ?", system.ProviderTypeWechat, unionID).
		Preload("User").First(&authProvider).Error
	if err == nil {
		// 找到，直接登录
		now := time.Now()
		authProvider.LastUsedAt = &now
		global.GVA_DB.Save(&authProvider)
		return &authProvider.User, nil
	}
	if err != gorm.ErrRecordNotFound {
		return nil, err
	}

	return nil, gorm.ErrRecordNotFound
}

// createBindResponse 创建绑定响应
func (w *WechatOAuthService) createBindResponse(userInfo *WechatUserInfo, state string) *WechatAuthResponse {
	return &WechatAuthResponse{
		Action:  "bind",
		Message: "需要绑定账号",
		TempData: &WechatTempData{
			OpenID:     userInfo.OpenID,
			UnionID:    userInfo.UnionID,
			Nickname:   userInfo.Nickname,
			HeadImgURL: userInfo.HeadImgURL,
			State:      state,
		},
	}
}

// GetUserInfoByAccessToken 通过access_token获取用户详细信息（根据微信开放平台文档）
// 文档地址: https://developers.weixin.qq.com/doc/oplatform/developers/dev/auth/web.html
func (w *WechatOAuthService) GetUserInfoByAccessToken(accessToken, openID string) (*WechatUserInfo, error) {
	// 构建请求URL - 根据微信官方文档
	reqURL := fmt.Sprintf(
		"https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
		accessToken,
		openID,
	)

	global.GVA_LOG.Info("请求微信用户信息", zap.String("url", reqURL))

	// 发起HTTP GET请求
	resp, err := http.Get(reqURL)
	if err != nil {
		global.GVA_LOG.Error("请求微信用户信息API失败", zap.Error(err))
		return nil, fmt.Errorf("请求微信用户信息API失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取用户信息响应失败", zap.Error(err))
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("微信用户信息API响应", zap.String("response", string(body)))

	// 先检查是否为错误响应
	var errorResp WechatErrorResponse
	if err := json.Unmarshal(body, &errorResp); err == nil && errorResp.ErrCode != 0 {
		global.GVA_LOG.Error("微信用户信息API返回错误",
			zap.Int("errcode", errorResp.ErrCode),
			zap.String("errmsg", errorResp.ErrMsg))
		return nil, fmt.Errorf("微信API错误 %d: %s", errorResp.ErrCode, errorResp.ErrMsg)
	}

	// 解析正常响应
	var userInfo WechatUserInfo
	if err := json.Unmarshal(body, &userInfo); err != nil {
		global.GVA_LOG.Error("解析用户信息失败", zap.Error(err))
		return nil, fmt.Errorf("解析用户信息失败: %v", err)
	}

	// 验证必要字段
	if userInfo.OpenID == "" {
		global.GVA_LOG.Error("用户信息中openid为空")
		return nil, fmt.Errorf("获取用户信息失败，openid为空")
	}

	global.GVA_LOG.Info("成功获取微信用户信息",
		zap.String("openid", userInfo.OpenID),
		zap.String("nickname", userInfo.Nickname),
		zap.String("unionid", userInfo.UnionID))

	return &userInfo, nil
}

// LoginByWechat 微信授权登录
func (w *WechatOAuthService) LoginByWechat(req systemReq.WechatAuthRequest) (*WechatAuthResponse, error) {

	// 用code换取access_token
	accessToken, err := w.getAccessToken(req.Code)
	if err != nil {
		return nil, fmt.Errorf("获取access_token失败: %v", err)
	}

	// 获取用户信息
	userInfo, err := w.getUserInfo(accessToken)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 检查用户是否已存在
	user, err := w.findUserByWechat(userInfo.OpenID, userInfo.UnionID)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	if user != nil {
		// 设置用户默认路由
		MenuServiceApp.UserAuthorityDefaultRouter(user)
		return &WechatAuthResponse{
			Action: "login",
			User:   user,
		}, nil
	} else {
		// 用户不存在且无冲突，返回绑定信息
		return w.createBindResponse(userInfo, req.State), nil
	}
}

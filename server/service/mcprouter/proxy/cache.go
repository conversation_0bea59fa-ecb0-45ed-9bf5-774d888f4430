package proxy

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/redis/go-redis/v9"
)

const (
	proxyInfoKey = "mcp:pi_%s"
)

// 获取Redis操作接口
func getRedisClient() (redis.UniversalClient, error) {
	if global.GVA_REDIS == nil {
		return nil, errors.New("global redis not initialized")
	}
	return global.GVA_REDIS, nil
}

func getRedisContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), 5*time.Second)
}

// StoreProxyInfo stores the proxy info to redis
func StoreProxyInfo(sessionID string, proxyInfo *ProxyInfo) error {
	ctx, cancel := getRedisContext()
	defer cancel()

	redisClient, err := getRedisClient()
	if err != nil {
		return err
	}

	b, err := json.Marshal(proxyInfo)
	if err != nil {
		return err
	}

	cacheKey := fmt.Sprintf(proxyInfoKey, sessionID)
	expires := time.Hour // 1小时
	return redisClient.Set(ctx, cacheKey, b, expires).Err()
}

// GetProxyInfo gets the proxy info from redis
func GetProxyInfo(sessionID string) (*ProxyInfo, error) {
	ctx, cancel := getRedisContext()
	defer cancel()

	redisClient, err := getRedisClient()
	if err != nil {
		return nil, err
	}

	cacheKey := fmt.Sprintf(proxyInfoKey, sessionID)

	b, err := redisClient.Get(ctx, cacheKey).Bytes()
	if err != nil || b == nil {
		return nil, errors.New("cache not found")
	}

	proxyInfo := &ProxyInfo{}
	if err := json.Unmarshal(b, proxyInfo); err != nil {
		return nil, err
	}

	return proxyInfo, nil
}

// DeleteProxyInfo deletes the proxy info from redis
func DeleteProxyInfo(sessionID string) error {
	ctx, cancel := getRedisContext()
	defer cancel()

	redisClient, err := getRedisClient()
	if err != nil {
		return err
	}

	cacheKey := fmt.Sprintf(proxyInfoKey, sessionID)
	redisClient.Del(ctx, cacheKey)

	return nil
}

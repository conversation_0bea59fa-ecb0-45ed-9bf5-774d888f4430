package mcprouter

import (
	"errors"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type BindingService struct{}

// BindingRequest 绑定请求参数
type BindingRequest struct {
	ChatSessionID string `json:"chatSessionId" binding:"required"`
	ServerUUID    string `json:"serverUuid" binding:"required"`
	ToolName      string `json:"toolName" binding:"required"`
	UserID        uint   `json:"userId" binding:"required"`
}

// BindingResponse 绑定响应结果
type BindingResponse struct {
	Success        bool   `json:"success"`
	Message        string `json:"message"`
	AgentHistoryID int64  `json:"agentHistoryId,omitempty"`
	ServerLogID    int64  `json:"serverLogId,omitempty"`
	PointsTransfer int    `json:"pointsTransfer,omitempty"`
}

// BindServerLogAndAgentChatHistory 绑定ServerLog与AgentChatHistory
func (s *BindingService) BindServerLogAndAgentChatHistory(req *BindingRequest) (*BindingResponse, error) {
	// 第一步：查询AgentChatHistory
	// 条件：SessionID=ChatSessionID，UserID=userId，ToolName=ServerUUID--ToolName，ChatType=3
	userIDInt := req.UserID

	// 构造ToolName: ServerUUID--ToolName
	fullToolName := fmt.Sprintf("%s--%s", req.ServerUUID, req.ToolName)

	var agentHistory mcprouter.AgentChatHistory
	err := global.GVA_DB.Where("session_id = ? AND user_id = ? AND tool_name = ? AND chat_type = ?",
		req.ChatSessionID, userIDInt, fullToolName, 3).
		Order("created_at DESC").
		First(&agentHistory).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &BindingResponse{
				Success: false,
				Message: "未找到匹配的AgentChatHistory记录",
			}, nil
		}
		global.GVA_LOG.Error("查询AgentChatHistory失败", zap.Error(err))
		return &BindingResponse{
			Success: false,
			Message: "查询AgentChatHistory失败",
		}, nil
	}

	// 第二步：查询ServerLog
	// 条件：userId，ServerUUID，ToolName，并判断创建时间接近
	var serverLog mcprouter.ServerLog
	err = global.GVA_DB.Where("user_id = ? AND server_uuid = ? AND tool_name = ?",
		req.UserID, req.ServerUUID, req.ToolName).
		Order("request_time DESC").
		First(&serverLog).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &BindingResponse{
				Success: false,
				Message: "未找到匹配的ServerLog记录",
			}, nil
		}
		global.GVA_LOG.Error("查询ServerLog失败", zap.Error(err))
		return &BindingResponse{
			Success: false,
			Message: "查询ServerLog失败",
		}, nil
	}

	// 判断创建时间是否接近（允许5分钟的时间差）
	//timeDiff := agentHistory.CreatedAt.Sub(serverLog.RequestTime)
	//if timeDiff < 0 {
	//	timeDiff = -timeDiff
	//}
	//
	//maxAllowedDiff := 5 * time.Minute
	//if timeDiff > maxAllowedDiff {
	//	return &BindingResponse{
	//		Success: false,
	//		Message: fmt.Sprintf("记录时间差过大（%.1f分钟），可能不匹配", timeDiff.Minutes()),
	//	}, nil
	//}

	// 第三步：执行绑定操作
	// 开始事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新ServerLog的ChatSessionID
	if err := tx.Model(&serverLog).Update("chat_session_id", req.ChatSessionID).Error; err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("更新ServerLog的ChatSessionID失败", zap.Error(err))
		return &BindingResponse{
			Success: false,
			Message: "更新ServerLog失败",
		}, nil
	}

	// 更新AgentChatHistory的Points
	if err := tx.Model(&agentHistory).Update("points", serverLog.Points).Error; err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("更新AgentChatHistory的Points失败", zap.Error(err))
		return &BindingResponse{
			Success: false,
			Message: "更新AgentChatHistory失败",
		}, nil
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		global.GVA_LOG.Error("提交事务失败", zap.Error(err))
		return &BindingResponse{
			Success: false,
			Message: "提交事务失败",
		}, nil
	}

	global.GVA_LOG.Info("成功绑定ServerLog与AgentChatHistory",
		zap.Int64("serverLogId", serverLog.ID),
		zap.Int64("agentHistoryId", agentHistory.ID),
		zap.Int("pointsTransfer", serverLog.Points),
		zap.String("chatSessionId", req.ChatSessionID))

	return &BindingResponse{
		Success:        true,
		Message:        "绑定成功",
		AgentHistoryID: agentHistory.ID,
		ServerLogID:    serverLog.ID,
		PointsTransfer: serverLog.Points,
	}, nil
}

package mcpserver

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/tidwall/gjson"
)

// ServerConfig is the config for the remote mcp server
type ServerConfig struct {
	ServerUUID   string `json:"server_uuid,omitempty" mapstructure:"server_uuid,omitempty"`
	ServerName   string `json:"server_name,omitempty" mapstructure:"server_name,omitempty"`
	ServerKey    string `json:"server_key,omitempty" mapstructure:"server_key,omitempty"`
	Command      string `json:"command,omitempty" mapstructure:"command,omitempty"`
	CommandHash  string `json:"command_hash,omitempty" mapstructure:"command_hash,omitempty"`
	ShareProcess bool   `json:"share_process,omitempty" mapstructure:"share_process"`
	ServerType   string `json:"server_type,omitempty" mapstructure:"server_type,omitempty"`
	ServerURL    string `json:"server_url,omitempty" mapstructure:"server_url,omitempty"`
	ServerParams string `json:"server_params,omitempty" mapstructure:"server_params,omitempty"`
}

// GetServerConfig returns the config for the given key
func GetServerConfig(key string) *ServerConfig {
	// 先读取全局 share_process 配置，确保配置读取正常
	shareProcess := global.GVA_VP.GetBool("mcp_share_process")

	config, err := getDBServerConfig(key)
	if err != nil {
		log.Printf("get db config failed: %v\n", err)
		// 数据库查询失败时，返回一个默认配置，但保留正确的ShareProcess设置
		return &ServerConfig{
			ServerUUID:   key, // 使用key作为UUID
			ServerName:   "Unknown Server",
			ServerKey:    key,
			Command:      "",
			CommandHash:  "",
			ShareProcess: shareProcess, // 使用正确的配置值
			ServerType:   "",
			ServerURL:    "",
			ServerParams: "",
		}
	}
	return config
}

// getDBServerConfig returns the config for the given key from the database
func getDBServerConfig(key string) (*ServerConfig, error) {
	serverkey, err := mcprouter.FindServerkeyByServerKey(key)
	if err != nil {
		return nil, err
	}

	// 读取全局 share_process 配置
	shareProcess := global.GVA_VP.GetBool("mcp_share_process")
	return &ServerConfig{
		ServerUUID:   serverkey.ServerUUID,
		ServerName:   serverkey.ServerName,
		ServerKey:    serverkey.ServerKey,
		Command:      serverkey.ServerCommand,
		CommandHash:  serverkey.ServerParams,
		ShareProcess: shareProcess, // 使用全局配置
		ServerParams: serverkey.ServerParams,
	}, nil
}

// getRemoteServerConfig returns the config for the given key from the remote API
func getRemoteServerConfig(key string) (*ServerConfig, error) {
	apiUrl := global.GVA_VP.GetString("remote_apis.get_server_config")

	params := map[string]string{
		"server_key": key,
	}

	jsonData, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	log.Printf("get remote config from %s, with params: %s\n", apiUrl, jsonData)

	response, err := http.Post(apiUrl, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	data := gjson.ParseBytes(body)
	log.Printf("get remote config with key: %s, response: %s\n", key, data.String())

	if data.Get("code").Int() != 0 {
		return nil, fmt.Errorf("get remote config failed: %s", data.Get("message").String())
	}

	config := &ServerConfig{}
	if err = json.Unmarshal([]byte(data.Get("data").String()), config); err != nil {
		return nil, err
	}

	if config.Command != "" && config.CommandHash == "" {
		config.CommandHash = fmt.Sprintf("%x", md5.Sum([]byte(config.Command)))
	}

	return config, nil
}

package mcpserver

import (
	"context"

	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
)

type Server struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Config      interface{} `json:"config"`
}

func GetHostedServers(page int, limit int) ([]*Server, error) {
	servers := []*Server{}
	allowCall := true
	statusStr := "created"
	projectsSearch := mcpReq.ProjectsSearch{
		AllowCall: &allowCall,
		Status:    &statusStr,
		PageInfo:  commonReq.PageInfo{Page: page, PageSize: limit},
	}
	projects, err := service.ServiceGroupApp.McpServiceGroup.ProjectsService.GetProjectsWithFilters(
		context.Background(),
		projectsSearch,
	)
	if err != nil {
		return nil, err
	}

	for _, project := range projects {
		config := map[string]interface{}{}

		servers = append(servers, &Server{
			Name:        project.Name,
			Description: project.Description,
			Config:      config,
		})
	}

	return servers, nil
}

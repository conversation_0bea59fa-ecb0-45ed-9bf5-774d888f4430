package wecom

import (
	"bytes"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/utils/upload"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	clipboardModel "github.com/flipped-aurora/gin-vue-admin/server/model/clipboard"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/model/wecom"
	"github.com/flipped-aurora/gin-vue-admin/server/service/aliyun"
	clipboardService "github.com/flipped-aurora/gin-vue-admin/server/service/clipboard"
	"github.com/flipped-aurora/gin-vue-admin/server/service/websocket"
	"go.uber.org/zap"
)

const wecomAccessTokenKey = "wecom:access_token"

type WechatWorkService struct{}

// GetAccessToken 获取企业微信access_token，优先从redis获取，未过期则直接返回，否则请求微信接口并缓存
func (s *WechatWorkService) GetAccessToken() (string, error) {
	corpID := global.GVA_CONFIG.Wecom.CorpID
	corpSecret := global.GVA_CONFIG.Wecom.CorpSecret

	ctx := context.Background()
	// 1. 先查redis
	val, err := global.GVA_REDIS.Get(ctx, wecomAccessTokenKey+":"+corpID).Result()
	if err == nil && val != "" {
		return val, nil
	}

	// 2. 请求微信接口
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s", corpID, corpSecret)
	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var result wecom.WechatWorkAccessTokenResp
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", err
	}
	if result.ErrCode != 0 {
		return "", fmt.Errorf("get access_token failed: %s", result.ErrMsg)
	}

	// 3. 存入redis，过期时间比微信返回的expires_in略短
	expire := time.Duration(result.ExpiresIn-60) * time.Second // 提前1分钟过期
	err = global.GVA_REDIS.Set(ctx, wecomAccessTokenKey+":"+corpID, result.AccessToken, expire).Err()
	if err != nil {
		return "", err
	}
	return result.AccessToken, nil
}

// UserEnterSessionEventXML 用户进入会话事件结构体
// 参考：https://developer.work.weixin.qq.com/document/path/94670
// <xml><ToUserName><![CDATA[toUser]]></ToUserName><FromUserName><![CDATA[fromUser]]></FromUserName><CreateTime>123456789</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[user_enter_session]]></Event><ExternalUserID><![CDATA[external_userid]]></ExternalUserID></xml>
type EventXML struct {
	XMLName        xml.Name `xml:"xml"`
	ToUserName     string   `xml:"ToUserName"`
	FromUserName   string   `xml:"FromUserName"`
	CreateTime     int64    `xml:"CreateTime"`
	MsgType        string   `xml:"MsgType"`
	Event          string   `xml:"Event"`
	Token          string   `xml:"Token"`
	OpenKfId       string   `xml:"OpenKfId"`
	ExternalUserID string   `xml:"ExternalUserID"`
	Scene          string   `xml:"Scene"`
	SceneParam     string   `xml:"SceneParam"`
	WelcomeCode    string   `xml:"WelcomeCode"`
}

// HandleUserEnterSessionEvent 处理用户进入会话事件
func (s *WechatWorkService) HandleUserEnterSessionEvent(openKfid, externalUserID string) error {

	batchGetCustomerInfoResp, err := s.BatchGetCustomerInfo([]string{externalUserID}, 1)
	if err != nil {
		return err
	}
	unionID := batchGetCustomerInfoResp.CustomerList[0].UnionID
	// 查找用户
	user, err := s.GetUserByUnionid(unionID)
	if err != nil {
		return err
	}
	if user == nil {
		// 未找到用户，发送授权提示
		_, _ = s.SendKfTextMsg(externalUserID, openKfid, "欢迎使用Rapido剪贴板，使用前需要获取Rapido授权，https://mcpcn.authing.cn")
	} else {
		// 找到用户，发送欢迎消息
		_, _ = s.SendKfTextMsg(externalUserID, openKfid, "欢迎使用Rapido剪贴板，请输入你想要剪贴的内容")
	}

	return nil
}

// BatchGetCustomerInfoReq 批量获取客户信息请求体
// 参考：https://developer.work.weixin.qq.com/document/path/95159
type BatchGetCustomerInfoReq struct {
	ExternalUserIDList      []string `json:"external_userid_list"`
	NeedEnterSessionContext int      `json:"need_enter_session_context"`
}

// BatchGetCustomerInfoResp 批量获取客户信息响应体
// 只保留常用字段
// 参考：https://developer.work.weixin.qq.com/document/path/95159
type BatchGetCustomerInfoResp struct {
	ErrCode      int    `json:"errcode"`
	ErrMsg       string `json:"errmsg"`
	CustomerList []struct {
		ExternalUserID      string `json:"external_userid"`
		Nickname            string `json:"nickname"`
		Avatar              string `json:"avatar"`
		Gender              int    `json:"gender"`
		UnionID             string `json:"unionid"`
		EnterSessionContext struct {
			Scene          string `json:"scene"`
			SceneParam     string `json:"scene_param"`
			WechatChannels struct {
				Nickname string `json:"nickname"`
				Scene    int    `json:"scene"`
			} `json:"wechat_channels"`
		} `json:"enter_session_context"`
	} `json:"customer_list"`
	InvalidExternalUserID []string `json:"invalid_external_userid"`
}

// BatchGetCustomerInfo 批量获取客户信息，返回unionid等
func (s *WechatWorkService) BatchGetCustomerInfo(externalUserIDs []string, needEnterSessionContext int) (*BatchGetCustomerInfoResp, error) {
	ctx := context.Background()
	cacheKeyPrefix := "wecom:external_user_info:"
	var result BatchGetCustomerInfoResp
	var idsToQuery []string
	// 先查redis缓存
	for _, externalUserID := range externalUserIDs {
		cacheKey := cacheKeyPrefix + externalUserID
		val, err := global.GVA_REDIS.Get(ctx, cacheKey).Result()
		if err == nil && val != "" {
			var cached struct {
				ExternalUserID      string `json:"external_userid"`
				Nickname            string `json:"nickname"`
				Avatar              string `json:"avatar"`
				Gender              int    `json:"gender"`
				UnionID             string `json:"unionid"`
				EnterSessionContext struct {
					Scene          string `json:"scene"`
					SceneParam     string `json:"scene_param"`
					WechatChannels struct {
						Nickname string `json:"nickname"`
						Scene    int    `json:"scene"`
					} `json:"wechat_channels"`
				} `json:"enter_session_context"`
			}
			if err := json.Unmarshal([]byte(val), &cached); err == nil {
				result.CustomerList = append(result.CustomerList, cached)
				continue
			}
		}
		idsToQuery = append(idsToQuery, externalUserID)
	}
	// 需要请求微信接口的部分
	if len(idsToQuery) > 0 {
		token, err := s.GetAccessToken()
		if err != nil {
			return nil, err
		}
		url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/kf/customer/batchget?access_token=%s", token)
		postBody := BatchGetCustomerInfoReq{
			ExternalUserIDList:      idsToQuery,
			NeedEnterSessionContext: needEnterSessionContext,
		}
		jsonBody, err := json.Marshal(postBody)
		if err != nil {
			return nil, err
		}
		resp, err := http.Post(url, "application/json", bytes.NewReader(jsonBody))
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		var apiResp BatchGetCustomerInfoResp
		if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
			return nil, err
		}
		if apiResp.ErrCode != 0 {
			return &apiResp, fmt.Errorf("get customer info failed: %s", apiResp.ErrMsg)
		}
		// 写入redis缓存
		for _, c := range apiResp.CustomerList {
			cacheKey := cacheKeyPrefix + c.ExternalUserID
			b, _ := json.Marshal(c)
			_ = global.GVA_REDIS.Set(ctx, cacheKey, b, 2*time.Hour).Err()
			result.CustomerList = append(result.CustomerList, c)
		}
		result.InvalidExternalUserID = append(result.InvalidExternalUserID, apiResp.InvalidExternalUserID...)
	}
	return &result, nil
}

// HandleKfMsgOrEvent 处理微信客服kf_msg_or_event事件
func (s *WechatWorkService) HandleKfMsgOrEvent(xmlBody []byte) error {
	var event EventXML
	if err := xml.Unmarshal(xmlBody, &event); err != nil {
		// 记录解析失败日志
		log := wecom.WecomEventCallbackLog{
			EventType:    "parse_error",
			RawContent:   string(xmlBody),
			HandleResult: err.Error(),
			Remark:       "XML解析失败",
		}
		global.GVA_DB.Create(&log)
		return err
	}
	var handleErr error
	switch event.Event {
	case "kf_msg_or_event":
		// 先调用SyncKfMsg读取消息
		syncResp, err := s.SyncKfMsg(event.Token, "", 1000, 0, event.OpenKfId)
		if err != nil {
			handleErr = err
		} else {
			for _, msg := range syncResp.MsgList {
				var title string
				switch msg.MsgType {
				case "text":
					user, err := s.GetUserByExternalUserID(msg.ExternalUserID, msg.OpenKfid)
					if err != nil || user == nil {
						break
					}
					// 1. 先发收到消息提示
					_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "收到剪贴内容，开始处理...")
					runeContent := []rune(msg.Text.Content)
					if len(runeContent) > 20 {
						title = string(runeContent[:20]) + "..."
					} else {
						title = msg.Text.Content
					}
					s.saveToClipboard(user, msg.Text.Content, "text", "", 0, "wechat", "", title)
					// 2. 剪贴成功后发确认消息
					_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "已剪贴，剪贴标题为"+title)
				case "image":
					user, err := s.GetUserByExternalUserID(msg.ExternalUserID, msg.OpenKfid)
					if err != nil || user == nil {
						break
					}
					_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "收到剪贴内容，开始处理...")
					fileURL := msg.Image.MediaID // 实际业务可换成下载后的URL
					ossFileName := fileURL + ".png"

					fileName := fmt.Sprintf("uploads/clipboard/%s/%s",
						time.Now().Format("2006-01-02"),
						ossFileName)
					fileURL, err = s.DownloadMediaAndUploadOSS(fileURL, fileName)
					if err != nil {
						break
					}
					title = "微信图片"
					s.saveToClipboard(user, fileURL, "image", fileURL, 0, "wechat", msg.Image.MediaID, title)
					_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "已剪贴图片")
				case "voice":
					user, err := s.GetUserByExternalUserID(msg.ExternalUserID, msg.OpenKfid)
					if err != nil || user == nil {
						break
					}
					_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "收到剪贴内容，开始处理...")
					fileURL := msg.Voice.MediaID // 实际业务可换成下载后的URL
					ossFileName := fileURL + ".wav"
					fileName := fmt.Sprintf("uploads/clipboard/%s/%s",
						time.Now().Format("2006-01-02"),
						ossFileName)
					fileURL, err = s.DownloadMediaAndUploadOSS(fileURL, fileName)
					if err != nil {
						break
					}

					//将语音识别文字
					speechService := &aliyun.SpeechService{}
					resultText, err := speechService.AliyunSpeechRecognizeOnce(fileURL)
					if err != nil {
						_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "语音剪贴失败")
						break
					}

					runeContent := []rune(resultText)
					if len(runeContent) > 20 {
						title = string(runeContent[:20]) + "..."
					} else {
						title = resultText
					}
					s.saveToClipboard(user, resultText, "voice", fileURL, 0, "wechat", msg.Voice.MediaID, title)
					_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "已剪贴，剪贴标题为"+title)
				case "event":
					// 处理事件消息
					switch msg.Event.EventType {
					case "enter_session":
						//用户进入会话事件
						fmt.Printf("用户进入会话: open_kfid=%s, external_userid=%s",
							msg.Event.OpenKfid, msg.Event.ExternalUserID)

						//处理用户进入会话事件
						handleErr = s.HandleUserEnterSessionEvent(msg.Event.OpenKfid, msg.Event.ExternalUserID)
					}
				default:
					// 其他类型
					batchResp, err := s.BatchGetCustomerInfo([]string{msg.ExternalUserID}, 1)
					if err == nil && len(batchResp.CustomerList) > 0 {
						_, _ = s.SendKfTextMsg(msg.ExternalUserID, msg.OpenKfid, "暂无法剪贴该消息内容")
					}
				}
			}
		}
	default:
		handleErr = nil
	}
	// 记录事件处理日志
	log := wecom.WecomEventCallbackLog{
		EventType:  event.Event,
		RawContent: string(xmlBody),
		Remark:     "事件回调处理",
	}
	if handleErr != nil {
		log.HandleResult = handleErr.Error()
	} else {
		log.HandleResult = "success"
	}
	global.GVA_DB.Create(&log)
	return handleErr
}

// SendKfTextMsg 发送微信客服文本消息
func (s *WechatWorkService) SendKfTextMsg(touser, openKfid, content string) (*wecom.SendKfTextMsgResp, error) {
	token, err := s.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/kf/send_msg?access_token=%s", token)
	var req wecom.SendKfTextMsgReq
	req.Touser = touser
	req.OpenKfid = openKfid
	req.MsgType = "text"
	req.Text.Content = content
	jsonBody, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	resp, err := http.Post(url, "application/json", bytes.NewReader(jsonBody))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	var result wecom.SendKfTextMsgResp
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}
	if result.ErrCode != 0 {
		return &result, fmt.Errorf("send kf text msg failed: %s", result.ErrMsg)
	}
	return &result, nil
}

// SyncKfMsgReq 客服消息同步请求体
// 参考：https://developer.work.weixin.qq.com/document/path/94670
type SyncKfMsgReq struct {
	Cursor      string `json:"cursor,omitempty"`
	Token       string `json:"token"`
	Limit       int    `json:"limit,omitempty"`
	VoiceFormat int    `json:"voice_format,omitempty"`
	OpenKfid    string `json:"open_kfid"`
}

// SyncKfMsgResp 客服消息同步响应体
type SyncKfMsgResp struct {
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
	NextCursor string `json:"next_cursor"`
	HasMore    int    `json:"has_more"`
	MsgList    []struct {
		MsgID          string `json:"msgid"`
		OpenKfid       string `json:"open_kfid"`
		ExternalUserID string `json:"external_userid"`
		SendTime       int64  `json:"send_time"`
		Origin         int    `json:"origin"`
		ServicerUserID string `json:"servicer_userid"`
		MsgType        string `json:"msgtype"`
		Event          struct {
			EventType      string `json:"event_type"`
			Scene          string `json:"scene"`
			SceneParam     string `json:"scene_param"`
			WelcomeCode    string `json:"welcome_code"`
			ExternalUserID string `json:"external_userid"`
			OpenKfid       string `json:"open_kfid"`
		} `json:"event"`
		Text struct {
			Content string `json:"content"`
			MenuID  string `json:"menu_id"`
		} `json:"text"`
		Image struct {
			MediaID string `json:"media_id"`
		} `json:"image"`
		Voice struct {
			MediaID string `json:"media_id"`
		} `json:"voice"`
		Video struct {
			MediaID string `json:"media_id"`
		} `json:"video"`
		File struct {
			MediaID string `json:"media_id"`
		} `json:"file"`
	} `json:"msg_list"`
}

// SyncKfMsg 同步微信客服消息
func (s *WechatWorkService) SyncKfMsg(token, cursor string, limit, voiceFormat int, openKfid string) (*SyncKfMsgResp, error) {
	ctx := context.Background()
	cacheKey := "wecom:kfmsg:next_cursor:" + openKfid
	// 优先用缓存的cursor
	if cursor == "" {
		if val, err := global.GVA_REDIS.Get(ctx, cacheKey).Result(); err == nil && val != "" {
			cursor = val
		}
	}
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/kf/sync_msg?access_token=%s", accessToken)
	postBody := SyncKfMsgReq{
		Cursor:      cursor,
		Token:       token,
		Limit:       limit,
		VoiceFormat: voiceFormat,
		OpenKfid:    openKfid,
	}
	jsonBody, err := json.Marshal(postBody)
	if err != nil {
		return nil, err
	}
	resp, err := http.Post(url, "application/json", bytes.NewReader(jsonBody))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	var result SyncKfMsgResp
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}
	if result.ErrCode != 0 {
		return &result, fmt.Errorf("sync kf msg failed: %s", result.ErrMsg)
	}
	// 拉取成功后缓存新的next_cursor
	if result.NextCursor != "" {
		_ = global.GVA_REDIS.Set(ctx, cacheKey, result.NextCursor, 0).Err()
	}
	return &result, nil
}

// GetUserByUnionid 通过unionid查找用户
func (s *WechatWorkService) GetUserByUnionid(unionid string) (*system.SysUser, error) {
	var user system.SysUser
	err := global.GVA_DB.Where("mcpcn_wechat_unionid = ?", unionid).First(&user).Error
	if err != nil {
		return nil, err
	}
	//标记用户已连接客服
	if user.ConnectedKf {
		return &user, nil
	}
	_ = global.GVA_DB.Model(&user).Update("connected_kf", true).Error
	return &user, nil
}

// 通过external_userid查找用户,验证是否授权
func (s *WechatWorkService) GetUserByExternalUserID(externalUserID, openKfid string) (*system.SysUser, error) {
	batchResp, err := s.BatchGetCustomerInfo([]string{externalUserID}, 1)
	if err != nil || len(batchResp.CustomerList) == 0 {
		return nil, err
	}
	unionid := batchResp.CustomerList[0].UnionID
	user, err := s.GetUserByUnionid(unionid)
	if err != nil {
		return nil, err
	}
	if user == nil {
		_, _ = s.SendKfTextMsg(externalUserID, openKfid, "欢迎使用Rapido剪贴板，使用前需要获取Rapido授权，https://mcpcn.authing.cn")
	}
	return user, nil
}

// saveToClipboard 将消息内容存入剪贴板
func (s *WechatWorkService) saveToClipboard(user *system.SysUser, content, contentType, fileURL string, fileSize int64, source, mediaId, title string) {
	if user == nil {
		return
	}
	clipSvc := &clipboardService.ClipboardService{}

	req := clipboardModel.ClipboardItemCreateRequest{
		Title:       title,
		Content:     content,
		ContentType: contentType,
		FileURL:     fileURL,
		FileSize:    fileSize,
		Source:      source,
		MediaId:     mediaId,
	}
	clipboardItem, err := clipSvc.CreateClipboardItem(user.ID, req)

	// 如果用户有WebSocket连接，通过WebSocket推送新剪贴板消息
	if err == nil && websocket.GlobalWebSocketService != nil {
		if websocket.GlobalWebSocketService.IsUserConnected(user.ID) {
			wsMessage := websocket.WebSocketMessage{
				Type:      "clipboard_new",
				Content:   content,
				Title:     title,
				UserID:    user.ID,
				Timestamp: time.Now().Unix(),
				Extra: map[string]interface{}{
					"clipboardId": clipboardItem.ID,
					"contentType": contentType,
					"fileURL":     fileURL,
					"source":      source,
				},
			}

			// 发送WebSocket消息
			err := websocket.GlobalWebSocketService.SendToUser(user.ID, wsMessage)
			if err != nil {
				global.GVA_LOG.Error("发送WebSocket消息失败",
					zap.Uint("userID", user.ID),
					zap.String("error", err.Error()))
			} else {
				global.GVA_LOG.Info("通过WebSocket推送新剪贴板消息",
					zap.Uint("userID", user.ID),
					zap.String("title", title))
			}
		}
	}
}

// DownloadMediaAndUploadOSS 获取微信素材并上传到OSS，返回OSS文件URL
func (s *WechatWorkService) DownloadMediaAndUploadOSS(mediaID, ossFileName string) (string, error) {
	// 1. 获取access_token
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return "", err
	}
	// 2. 下载微信素材
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=%s&media_id=%s", accessToken, mediaID)
	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	// 3. 上传到OSS
	oss := upload.NewOss()
	ossURL, _, err := oss.UploadBytes(data, ossFileName)
	if err != nil {
		return "", err
	}
	return ossURL, nil
}

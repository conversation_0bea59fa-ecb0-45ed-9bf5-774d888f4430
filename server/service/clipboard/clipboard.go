package clipboard

import (
	"fmt"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/clipboard"
	"gorm.io/gorm"
)

type ClipboardService struct{}

// CreateClipboardItem 创建剪贴板条目
func (c *ClipboardService) CreateClipboardItem(userID uint, req clipboard.ClipboardItemCreateRequest) (*clipboard.ClipboardItem, error) {
	// 检查用户配置
	config, err := c.GetUserConfig(userID)
	if err != nil {
		// 如果没有配置，创建默认配置
		config = &clipboard.ClipboardConfig{
			UserID:         userID,
			MaxItems:       100,
			AutoCleanDays:  30,
			EnableWechat:   true,
			EnableNotify:   true,
			AllowFileTypes: "image,video,audio,document",
			MaxFileSize:    52428800, // 50MB
		}
		if err := global.GVA_DB.Create(config).Error; err != nil {
			return nil, fmt.Errorf("创建用户配置失败: %w", err)
		}
	}

	// 检查文件类型和大小限制
	if req.ContentType != "text" {
		allowTypes := strings.Split(config.AllowFileTypes, ",")
		allowed := false
		for _, t := range allowTypes {
			if strings.TrimSpace(t) == req.ContentType {
				allowed = true
				break
			}
		}
		if !allowed {
			return nil, fmt.Errorf("不支持的文件类型: %s", req.ContentType)
		}

		if req.FileSize > config.MaxFileSize {
			return nil, fmt.Errorf("文件大小超过限制: %d > %d", req.FileSize, config.MaxFileSize)
		}
	}

	// 检查条目数量限制，如果超过限制则删除最旧的
	var count int64
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ?", userID).Count(&count)
	if count >= int64(config.MaxItems) {
		// 删除最旧的条目
		var oldItem clipboard.ClipboardItem
		if err := global.GVA_DB.Where("user_id = ?", userID).
			Order("created_at ASC").First(&oldItem).Error; err == nil {
			global.GVA_DB.Delete(&oldItem)
		}
	}

	// 生成标题
	title := req.Title
	if title == "" {
		switch req.ContentType {
		case "text":
			if len(req.Content) > 50 {
				title = req.Content[:50] + "..."
			} else {
				title = req.Content
			}
		case "image":
			title = "图片"
		case "video":
			title = "视频"
		case "audio":
			title = "语音"
		case "file":
			title = "文件"
		default:
			title = "未知类型"
		}
	}

	item := &clipboard.ClipboardItem{
		UserID:      userID,
		Title:       title,
		Content:     req.Content,
		ContentType: req.ContentType,
		FileURL:     req.FileURL,
		FileSize:    req.FileSize,
		Source:      req.Source,
		MediaId:     req.MediaId,
	}

	if err := global.GVA_DB.Create(item).Error; err != nil {
		return nil, fmt.Errorf("创建剪贴板条目失败: %w", err)
	}

	return item, nil
}

// GetClipboardItemList 获取剪贴板条目列表
func (c *ClipboardService) GetClipboardItemList(userID uint, req clipboard.ClipboardItemListRequest) ([]clipboard.ClipboardItem, int64, error) {
	var items []clipboard.ClipboardItem
	var total int64

	query := global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ?", userID)

	// 添加过滤条件
	if req.ContentType != "" {
		query = query.Where("content_type = ?", req.ContentType)
	}
	if req.Source != "" {
		query = query.Where("source = ?", req.Source)
	}
	if req.IsRead != nil {
		query = query.Where("is_read = ?", *req.IsRead)
	}
	if req.IsStarred != nil {
		query = query.Where("is_starred = ?", *req.IsStarred)
	}
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("title LIKE ? OR content LIKE ?", keyword, keyword)
	}
	if req.StartTime != "" {
		query = query.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("created_at <= ?", req.EndTime)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("created_at DESC").
		Offset(offset).Limit(req.PageSize).
		Find(&items).Error; err != nil {
		return nil, 0, fmt.Errorf("查询剪贴板条目失败: %w", err)
	}

	return items, total, nil
}

// GetClipboardItem 获取单个剪贴板条目
func (c *ClipboardService) GetClipboardItem(userID uint, id uint) (*clipboard.ClipboardItem, error) {
	var item clipboard.ClipboardItem
	if err := global.GVA_DB.Where("user_id = ? AND id = ?", userID, id).First(&item).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("剪贴板条目不存在")
		}
		return nil, fmt.Errorf("查询剪贴板条目失败: %w", err)
	}
	return &item, nil
}

// UpdateClipboardItem 更新剪贴板条目
func (c *ClipboardService) UpdateClipboardItem(userID uint, req clipboard.ClipboardItemUpdateRequest) error {
	updates := make(map[string]interface{})

	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.Content != "" {
		updates["content"] = req.Content
	}
	if req.IsRead != nil {
		updates["is_read"] = *req.IsRead
	}
	if req.IsStarred != nil {
		updates["is_starred"] = *req.IsStarred
	}

	if len(updates) == 0 {
		return fmt.Errorf("没有要更新的字段")
	}

	result := global.GVA_DB.Model(&clipboard.ClipboardItem{}).
		Where("user_id = ? AND id = ?", userID, req.ID).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("更新剪贴板条目失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("剪贴板条目不存在")
	}

	return nil
}

// DeleteClipboardItem 删除剪贴板条目
func (c *ClipboardService) DeleteClipboardItem(userID uint, id uint) error {
	result := global.GVA_DB.Where("user_id = ? AND id = ?", userID, id).
		Delete(&clipboard.ClipboardItem{})

	if result.Error != nil {
		return fmt.Errorf("删除剪贴板条目失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("剪贴板条目不存在")
	}

	return nil
}

// ClearClipboard 清空剪贴板
func (c *ClipboardService) ClearClipboard(userID uint) error {
	if err := global.GVA_DB.Where("user_id = ?", userID).Delete(&clipboard.ClipboardItem{}).Error; err != nil {
		return fmt.Errorf("清空剪贴板失败: %w", err)
	}
	return nil
}

// GetUserConfig 获取用户配置
func (c *ClipboardService) GetUserConfig(userID uint) (*clipboard.ClipboardConfig, error) {
	var config clipboard.ClipboardConfig
	if err := global.GVA_DB.Where("user_id = ?", userID).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户配置不存在")
		}
		return nil, fmt.Errorf("查询用户配置失败: %w", err)
	}
	return &config, nil
}

// UpdateUserConfig 更新用户配置
func (c *ClipboardService) UpdateUserConfig(userID uint, req clipboard.ClipboardConfigUpdateRequest) error {
	config := &clipboard.ClipboardConfig{
		UserID:         userID,
		MaxItems:       req.MaxItems,
		AutoCleanDays:  req.AutoCleanDays,
		EnableWechat:   req.EnableWechat,
		EnableNotify:   req.EnableNotify,
		AllowFileTypes: req.AllowFileTypes,
		MaxFileSize:    req.MaxFileSize,
	}

	// 使用 UPSERT 操作
	if err := global.GVA_DB.Save(config).Error; err != nil {
		return fmt.Errorf("更新用户配置失败: %w", err)
	}

	return nil
}

// GetClipboardStats 获取剪贴板统计信息
func (c *ClipboardService) GetClipboardStats(userID uint) (*clipboard.ClipboardStatsResponse, error) {
	stats := &clipboard.ClipboardStatsResponse{}

	// 总条目数
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ?", userID).Count(&stats.TotalItems)

	// 按类型统计
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND content_type = ?", userID, "text").Count(&stats.TextItems)
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND content_type = ?", userID, "image").Count(&stats.ImageItems)
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND content_type = ?", userID, "video").Count(&stats.VideoItems)
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND content_type = ?", userID, "audio").Count(&stats.AudioItems)
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND content_type = ?", userID, "file").Count(&stats.FileItems)

	// 未读和收藏
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND is_read = ?", userID, false).Count(&stats.UnreadItems)
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND is_starred = ?", userID, true).Count(&stats.StarredItems)

	// 今日条目
	today := time.Now().Format("2006-01-02")
	global.GVA_DB.Model(&clipboard.ClipboardItem{}).Where("user_id = ? AND DATE(created_at) = ?", userID, today).Count(&stats.TodayItems)

	return stats, nil
}

// AutoCleanExpiredItems 自动清理过期条目
func (c *ClipboardService) AutoCleanExpiredItems() error {
	// 清理过期条目
	if err := global.GVA_DB.Where("expire_at IS NOT NULL AND expire_at < ?", time.Now()).
		Delete(&clipboard.ClipboardItem{}).Error; err != nil {
		return fmt.Errorf("清理过期条目失败: %w", err)
	}

	// 根据用户配置清理旧条目
	var configs []clipboard.ClipboardConfig
	if err := global.GVA_DB.Find(&configs).Error; err != nil {
		return fmt.Errorf("查询用户配置失败: %w", err)
	}

	for _, config := range configs {
		cutoffDate := time.Now().AddDate(0, 0, -config.AutoCleanDays)
		if err := global.GVA_DB.Where("user_id = ? AND created_at < ?", config.UserID, cutoffDate).
			Delete(&clipboard.ClipboardItem{}).Error; err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("清理用户 %d 的旧条目失败: %v", config.UserID, err))
		}
	}

	return nil
}

package mq

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// RedisMQService Redis消息队列服务
type RedisMQService struct {
	client redis.UniversalClient
}

// NewRedisMQService 创建Redis MQ服务实例
func NewRedisMQService() *RedisMQService {
	return &RedisMQService{
		client: global.GVA_REDIS,
	}
}

// PublishMessage 发布消息到指定频道
func (r *RedisMQService) PublishMessage(channel string, message interface{}) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	// 序列化消息
	messageBytes, err := json.Marshal(message)
	if err != nil {
		global.GVA_LOG.Error("序列化消息失败", zap.Error(err))
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 发布消息
	result := r.client.Publish(ctx, channel, messageBytes)
	if result.Err() != nil {
		global.GVA_LOG.Error("发布消息失败",
			zap.String("channel", channel),
			zap.Error(result.Err()))
		return result.Err()
	}

	global.GVA_LOG.Info("消息发布成功",
		zap.String("channel", channel),
		zap.Int64("subscribers", result.Val()),
		zap.String("message", string(messageBytes)))

	return nil
}

// SubscribeChannel 订阅频道消息
func (r *RedisMQService) SubscribeChannel(channel string, handler func(message string) error) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	global.GVA_LOG.Info("开始订阅频道", zap.String("channel", channel))

	// 启动消息处理循环
	go func() {
		ctx := context.Background()
		pubsub := r.client.Subscribe(ctx, channel)
		defer pubsub.Close()

		// 确认订阅成功
		_, err := pubsub.Receive(ctx)
		if err != nil {
			global.GVA_LOG.Error("订阅频道失败",
				zap.String("channel", channel),
				zap.Error(err))
			return
		}

		global.GVA_LOG.Info("成功订阅频道", zap.String("channel", channel))

		// 消息接收循环
		for {
			msg, err := pubsub.ReceiveMessage(ctx)
			if err != nil {
				global.GVA_LOG.Error("接收消息失败",
					zap.String("channel", channel),
					zap.Error(err))
				// 如果是连接错误，尝试重新订阅
				if strings.Contains(err.Error(), "closed") {
					global.GVA_LOG.Info("连接已关闭，尝试重新订阅", zap.String("channel", channel))
					time.Sleep(5 * time.Second)
					// 递归重新订阅
					go func() {
						r.SubscribeChannel(channel, handler)
					}()
					return
				}
				continue
			}

			global.GVA_LOG.Info("收到消息",
				zap.String("channel", channel),
				zap.String("message", msg.Payload))

			// 处理消息
			if err := handler(msg.Payload); err != nil {
				global.GVA_LOG.Error("处理消息失败",
					zap.String("channel", channel),
					zap.String("message", msg.Payload),
					zap.Error(err))
			}
		}
	}()

	return nil
}

// PublishProjectUpdate 发布项目更新消息
func (r *RedisMQService) PublishProjectUpdate(projectUUID, callMethod, serverName string) error {
	message := global.ProjectUpdateEvent{
		ProjectUUID: projectUUID,
		CallMethod:  callMethod,
		ServerName:  serverName,
	}

	return r.PublishMessage("project.update", message)
}

// 全局Redis MQ服务实例
var GlobalRedisMQ *RedisMQService

// InitRedisMQ 初始化Redis MQ服务
func InitRedisMQ() {
	GlobalRedisMQ = NewRedisMQService()
	global.GVA_LOG.Info("Redis MQ服务初始化完成")
}

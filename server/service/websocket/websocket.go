package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许跨域
	},
}

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type      string      `json:"type"`      // 消息类型：text, image, voice, system等
	Content   string      `json:"content"`   // 消息内容
	Title     string      `json:"title"`     // 消息标题
	UserID    uint        `json:"userId"`    // 用户ID
	Timestamp int64       `json:"timestamp"` // 时间戳
	Extra     interface{} `json:"extra"`     // 额外数据
}

// Client WebSocket客户端
type Client struct {
	ID       string          // 客户端唯一标识
	UserID   uint            // 用户ID
	Conn     *websocket.Conn // WebSocket连接
	Send     chan []byte     // 发送消息的通道
	Hub      *Hub            // 所属的Hub
	LastPing time.Time       // 最后ping时间
}

// Hub WebSocket连接中心
type Hub struct {
	clients     map[*Client]bool   // 所有连接的客户端
	broadcast   chan []byte        // 广播消息通道
	register    chan *Client       // 注册客户端通道
	unregister  chan *Client       // 注销客户端通道
	userClients map[uint][]*Client // 按用户ID分组的客户端
	mutex       sync.RWMutex       // 读写锁
}

// WebSocketService WebSocket服务
type WebSocketService struct {
	hub *Hub
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		broadcast:   make(chan []byte),
		register:    make(chan *Client),
		unregister:  make(chan *Client),
		clients:     make(map[*Client]bool),
		userClients: make(map[uint][]*Client),
	}
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService() *WebSocketService {
	hub := NewHub()
	service := &WebSocketService{hub: hub}
	go hub.run()
	return service
}

// run Hub运行循环
func (h *Hub) run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			// 添加到用户分组
			if h.userClients[client.UserID] == nil {
				h.userClients[client.UserID] = make([]*Client, 0)
			}
			h.userClients[client.UserID] = append(h.userClients[client.UserID], client)
			h.mutex.Unlock()

			global.GVA_LOG.Info("WebSocket客户端连接",
				zap.String("clientID", client.ID),
				zap.Uint("userID", client.UserID))

			// 发送连接成功消息
			welcomeMsg := WebSocketMessage{
				Type:      "system",
				Content:   "WebSocket连接成功",
				Timestamp: time.Now().Unix(),
			}
			if data, err := json.Marshal(welcomeMsg); err == nil {
				select {
				case client.Send <- data:
				default:
					close(client.Send)
					delete(h.clients, client)
				}
			}

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.Send)

				// 从用户分组中移除
				if clients, exists := h.userClients[client.UserID]; exists {
					for i, c := range clients {
						if c == client {
							h.userClients[client.UserID] = append(clients[:i], clients[i+1:]...)
							break
						}
					}
					if len(h.userClients[client.UserID]) == 0 {
						delete(h.userClients, client.UserID)
					}
				}
			}
			h.mutex.Unlock()

			global.GVA_LOG.Info("WebSocket客户端断开",
				zap.String("clientID", client.ID),
				zap.Uint("userID", client.UserID))

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.Send <- message:
				default:
					close(client.Send)
					delete(h.clients, client)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// readPump 读取消息泵
func (c *Client) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(512)
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.LastPing = time.Now()
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, _, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("error: %v", err)
			}
			break
		}
	}
}

// writePump 写入消息泵
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 添加排队的聊天消息到当前的WebSocket消息
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// HandleWebSocket 处理WebSocket连接
func (s *WebSocketService) HandleWebSocket(c *gin.Context) {
	// 从token获取用户ID
	userID := c.GetUint("userID") // 假设中间件已经解析了用户ID
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}

	DeviceID := c.GetHeader("device-id")

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		global.GVA_LOG.Error("WebSocket升级失败", zap.Error(err))
		return
	}

	client := &Client{
		ID:       DeviceID,
		UserID:   userID,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		Hub:      s.hub,
		LastPing: time.Now(),
	}

	client.Hub.register <- client

	go client.writePump()
	go client.readPump()
}

// SendToUser 向指定用户发送消息
func (s *WebSocketService) SendToUser(userID uint, message WebSocketMessage) error {
	s.hub.mutex.RLock()
	clients, exists := s.hub.userClients[userID]
	s.hub.mutex.RUnlock()

	if !exists || len(clients) == 0 {
		global.GVA_LOG.Warn("用户没有WebSocket连接", zap.Uint("userID", userID))
		return nil
	}

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	s.hub.mutex.RLock()
	for _, client := range clients {
		select {
		case client.Send <- data:
		default:
			close(client.Send)
			delete(s.hub.clients, client)
		}
	}
	s.hub.mutex.RUnlock()

	global.GVA_LOG.Info("向用户发送WebSocket消息",
		zap.Uint("userID", userID),
		zap.String("type", message.Type))

	return nil
}

// Broadcast 广播消息给所有连接的客户端
func (s *WebSocketService) Broadcast(message WebSocketMessage) error {
	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	select {
	case s.hub.broadcast <- data:
	default:
		return err
	}

	return nil
}

// GetConnectedUsers 获取当前连接的用户列表
func (s *WebSocketService) GetConnectedUsers() []uint {
	s.hub.mutex.RLock()
	defer s.hub.mutex.RUnlock()

	users := make([]uint, 0, len(s.hub.userClients))
	for userID := range s.hub.userClients {
		users = append(users, userID)
	}
	return users
}

// IsUserConnected 检查用户是否有WebSocket连接
func (s *WebSocketService) IsUserConnected(userID uint) bool {
	s.hub.mutex.RLock()
	defer s.hub.mutex.RUnlock()

	clients, exists := s.hub.userClients[userID]
	return exists && len(clients) > 0
}

// IsDeviceConnected 检查指定设备是否有WebSocket连接
func (s *WebSocketService) IsDeviceConnected(deviceID string) bool {
	s.hub.mutex.RLock()
	defer s.hub.mutex.RUnlock()

	// 遍历所有客户端，查找匹配的设备ID
	for client := range s.hub.clients {
		if client.ID == deviceID {
			return true
		}
	}
	return false
}

// IsUserDeviceConnected 检查指定用户的指定设备是否有WebSocket连接
func (s *WebSocketService) IsUserDeviceConnected(userID uint, deviceID string) bool {
	s.hub.mutex.RLock()
	defer s.hub.mutex.RUnlock()

	clients, exists := s.hub.userClients[userID]
	if !exists {
		return false
	}

	// 在用户的客户端列表中查找指定设备
	for _, client := range clients {
		if client.ID == deviceID {
			return true
		}
	}
	return false
}

// GetUserConnectedDevices 获取用户所有已连接的设备ID列表
func (s *WebSocketService) GetUserConnectedDevices(userID uint) []string {
	s.hub.mutex.RLock()
	defer s.hub.mutex.RUnlock()

	clients, exists := s.hub.userClients[userID]
	if !exists {
		return []string{}
	}

	deviceIDs := make([]string, 0, len(clients))
	for _, client := range clients {
		if client.ID != "" { // 确保设备ID不为空
			deviceIDs = append(deviceIDs, client.ID)
		}
	}
	return deviceIDs
}

// 全局WebSocket服务实例
var GlobalWebSocketService *WebSocketService

// InitWebSocketService 初始化WebSocket服务
func InitWebSocketService() {
	GlobalWebSocketService = NewWebSocketService()
	global.GVA_LOG.Info("WebSocket服务初始化完成")
}

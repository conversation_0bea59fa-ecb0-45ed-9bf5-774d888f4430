package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/aliyun"
	"github.com/flipped-aurora/gin-vue-admin/server/service/authing"
	"github.com/flipped-aurora/gin-vue-admin/server/service/clipboard"
	"github.com/flipped-aurora/gin-vue-admin/server/service/device"
	"github.com/flipped-aurora/gin-vue-admin/server/service/example"
	"github.com/flipped-aurora/gin-vue-admin/server/service/integral"
	"github.com/flipped-aurora/gin-vue-admin/server/service/llm"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/service/payment"
	"github.com/flipped-aurora/gin-vue-admin/server/service/product"
	"github.com/flipped-aurora/gin-vue-admin/server/service/sms"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service/translate"
	"github.com/flipped-aurora/gin-vue-admin/server/service/websocket"
	"github.com/flipped-aurora/gin-vue-admin/server/service/wecom"
	"github.com/flipped-aurora/gin-vue-admin/server/service/yidun"
)

type ServiceGroup struct {
	SystemServiceGroup    system.ServiceGroup
	ExampleServiceGroup   example.ServiceGroup
	IntegralServiceGroup  integral.ServiceGroup
	McpServiceGroup       mcp.ServiceGroup
	AuthingServiceGroup   authing.ServiceGroup
	TranslateServiceGroup translate.ServiceGroup
	LLMServiceGroup       llm.ServiceGroup
	PaymentServiceGroup   payment.ServiceGroup
	ProductServiceGroup   product.ServiceGroup
	DeviceServiceGroup    device.ServiceGroup
	ClipboardServiceGroup clipboard.ServiceGroup
	WeComServiceGroup     wecom.ServiceGroup
	WebSocketServiceGroup websocket.ServiceGroup
	AliyunServiceGroup    aliyun.ServiceGroup
	YidunServiceGroup     yidun.ServiceGroup
	SmsServiceGroup       sms.ServiceGroup
}

var ServiceGroupApp = &ServiceGroup{McpServiceGroup: mcp.NewServiceGroup()}

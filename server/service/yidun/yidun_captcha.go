package yidun

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	captchaSdk "github.com/yidun/yidun-golang-sdk/yidun/service/captcha"
	"go.uber.org/zap"
)

type YidunCaptchaService struct{}

type CaptchaVerifyRequest = captchaSdk.CaptchaVerifyRequest

type CaptchaVerifyResponse = captchaSdk.CaptchaVerifyResponse

// VerifyCaptcha 验证验证码 - 使用官方SDK
func (ycs *YidunCaptchaService) VerifyCaptcha(req CaptchaVerifyRequest) (*CaptchaVerifyResponse, error) {

	// 验证配置
	if err := ycs.ValidateConfig(); err != nil {
		return nil, err
	}

	// 创建SDK客户端
	client := captchaSdk.NewCaptchaVerifyClientWithAccessKey(
		global.GVA_CONFIG.Yidun.SecretId,
		global.GVA_CONFIG.Yidun.SecretKey,
	)

	// 创建SDK请求
	sdkRequest := captchaSdk.NewCaptchaVerifyRequest()
	sdkRequest.SetCaptchaId(global.GVA_CONFIG.Yidun.CaptchaId).
		SetValidate(*req.Validate).
		SetUser("")

	// 调用SDK验证，直接返回SDK响应
	response, err := client.Verify(sdkRequest)
	if err != nil {
		global.GVA_LOG.Error("易盾验证码SDK验证失败", zap.Error(err))
		return nil, fmt.Errorf("验证码验证失败: %v", err)
	}

	// 记录验证结果
	if response.Error != nil && *response.Error == 0 {
		if response.Result != nil && *response.Result {
			global.GVA_LOG.Info("易盾验证码验证成功")
		} else {
			global.GVA_LOG.Warn("易盾验证码验证失败",
				zap.String("Validate", *req.Validate))
		}
	} else {
		errorCode := 0
		errorMsg := "unknown error"
		if response.Error != nil {
			errorCode = *response.Error
		}
		if response.Msg != nil {
			errorMsg = *response.Msg
		}
		global.GVA_LOG.Error("易盾验证码验证接口错误",
			zap.Int("error", errorCode),
			zap.String("msg", errorMsg))
	}

	return response, nil
}

// ValidateConfig 验证易盾配置
func (ycs *YidunCaptchaService) ValidateConfig() error {
	config := global.GVA_CONFIG.Yidun

	if config.SecretId == "" {
		return fmt.Errorf("易盾SecretId未配置")
	}
	if config.SecretKey == "" {
		return fmt.Errorf("易盾SecretKey未配置")
	}
	if config.CaptchaId == "" {
		return fmt.Errorf("易盾CaptchaId未配置")
	}

	return nil
}

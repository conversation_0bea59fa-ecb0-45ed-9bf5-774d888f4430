package sms

import (
	"encoding/json"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	aliSmsService "github.com/flipped-aurora/gin-vue-admin/server/plugin/sms/aliyun_sms/service"
	tencentSmsService "github.com/flipped-aurora/gin-vue-admin/server/plugin/sms/tencent_sms/service"
	"go.uber.org/zap"
)

type SmsService struct{}

// SendSMSCodeWithType 根据短信类型发送短信验证码
func (s *SmsService) SendSMSCodeWithType(phoneNumber, code string, smsType int) error {
	// 可以通过配置文件选择使用哪个短信服务商
	// 这里默认使用腾讯云短信服务
	//return s.SendTencentSMSWithType(phoneNumber, code, smsType)
	return s.SendAliSMSWithType(phoneNumber, code, smsType)
}

// SendTencentSMSWithType 根据短信类型发送腾讯云短信
func (s *SmsService) SendTencentSMSWithType(phoneNumber, code string, smsType int) error {
	tencentService := &tencentSmsService.TencentSmsService{}

	// 构建手机号数组，需要加上国家代码
	phoneNumbers := []string{"+86" + phoneNumber}

	// 构建模板参数
	templateParams := []string{code}

	// 根据短信类型选择不同的模板ID
	var templateId string
	switch smsType {
	case 1: // 注册验证码
		templateId = global.GVA_CONFIG.TencentSMS.RegisterTemplateID
	case 2: // 登录验证码
		templateId = global.GVA_CONFIG.TencentSMS.LoginTemplateID
	case 3: // 找回密码验证码
		templateId = global.GVA_CONFIG.TencentSMS.LoginTemplateID // 复用登录验证码模板
	case 4: // 绑定
		templateId = global.GVA_CONFIG.TencentSMS.TemplateID // 复用登录验证码模板
	default:
		return fmt.Errorf("不支持的短信类型: %d", smsType)
	}

	if templateId == "" {
		return fmt.Errorf("腾讯云短信模板ID未配置，短信类型: %d", smsType)
	}

	err := tencentService.SendSms(templateId, phoneNumbers, templateParams)
	if err != nil {
		global.GVA_LOG.Error("腾讯云短信发送失败", zap.Error(err))
		return fmt.Errorf("腾讯云短信发送失败: %v", err)
	}

	global.GVA_LOG.Info("腾讯云短信发送成功", zap.String("phone", phoneNumber), zap.Int("smsType", smsType))
	return nil
}

// SendAliSMSWithType 根据短信类型发送阿里云短信
func (s *SmsService) SendAliSMSWithType(phoneNumber, code string, smsType int) error {
	aliService := &aliSmsService.AliSmsService{}

	// 构建手机号数组
	phoneNumbers := []string{phoneNumber}

	// 构建模板参数 - 阿里云使用JSON格式
	templateParam := map[string]string{
		"code": code,
	}
	templateParamJson, err := json.Marshal(templateParam)
	if err != nil {
		return fmt.Errorf("构建模板参数失败: %v", err)
	}

	// 根据短信类型选择不同的模板Code
	var templateCode string
	switch smsType {
	case 1: // 注册验证码
		templateCode = global.GVA_CONFIG.AliSms.RegisterTemplateCode
	case 2: // 登录验证码
		templateCode = global.GVA_CONFIG.AliSms.LoginTemplateCode
	case 3: // 找回密码验证码
		templateCode = global.GVA_CONFIG.AliSms.TemplateCode // 复用登录验证码模板
	case 4: // 绑定手机号
		templateCode = global.GVA_CONFIG.AliSms.TemplateCode // 复用登录验证码模板
	default:
		return fmt.Errorf("不支持的短信类型: %d", smsType)
	}

	if templateCode == "" {
		return fmt.Errorf("阿里云短信模板Code未配置，短信类型: %d", smsType)
	}

	err = aliService.SendAliSms(phoneNumbers, templateCode, string(templateParamJson))
	if err != nil {
		global.GVA_LOG.Error("阿里云短信发送失败", zap.Error(err))
		return fmt.Errorf("阿里云短信发送失败: %v", err)
	}

	global.GVA_LOG.Info("阿里云短信发送成功", zap.String("phone", phoneNumber), zap.Int("smsType", smsType))
	return nil
}

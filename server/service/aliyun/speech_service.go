package aliyun

import (
	"context"
	"encoding/json"
	"errors"
	"log"
	"os"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	nls "github.com/aliyun/alibabacloud-nls-go-sdk"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type SpeechService struct{}

// AliyunSpeechRecognizeOnce 进行一句话语音识别，返回识别文本
func (s *SpeechService) AliyunSpeechRecognizeOnce(audioFile string) (string, error) {
	// 获取Token
	token, err := s.GetAliyunToken()
	if err != nil {
		return "", err
	}
	// 创建连接配置
	config := nls.NewConnectionConfigWithToken(nls.DEFAULT_URL, global.GVA_CONFIG.Aliyun.Speech.AppKey, token)
	// 日志
	logger := nls.NewNlsLogger(os.Stderr, "aliyun-speech", log.LstdFlags)
	logger.SetLogSil(false)
	logger.SetDebug(false)
	// 识别结果
	var resultText string
	onCompleted := func(text string, param interface{}) {
		resultText = text
	}
	sr, err := nls.NewSpeechRecognition(config, logger,
		nil, nil, nil,
		onCompleted, nil, nil)
	if err != nil {
		return "", err
	}
	param := nls.DefaultSpeechRecognitionParam()
	ready, err := sr.Start(param, nil)
	if err != nil {
		return "", err
	}
	<-ready
	// 发送音频数据
	file, err := os.Open(audioFile)
	if err != nil {
		return "", err
	}
	defer file.Close()
	buffers := nls.LoadPcmInChunk(file, 320)
	for _, data := range buffers.Data {
		if data != nil {
			sr.SendAudioData(data.Data)
		}
	}
	ready, err = sr.Stop()
	if err != nil {
		return "", err
	}
	<-ready
	sr.Shutdown()
	if resultText == "" {
		return "", errors.New("未识别到有效文本")
	}
	return resultText, nil
}

const aliyunTokenKey = "aliyun:speech:token"

type TokenResult struct {
	ErrMsg string `json:"ErrMsg"`
	Token  struct {
		UserId     string `json:"UserId"`
		Id         string `json:"Id"`
		ExpireTime int64  `json:"ExpireTime"`
	} `json:"Token"`
}

// GetAliyunToken 优先从 Redis 获取 Token，未命中则请求阿里云接口并写入 Redis
func (s *SpeechService) GetAliyunToken() (string, error) {
	ak := global.GVA_CONFIG.Aliyun.Speech.AccessKeyId
	sk := global.GVA_CONFIG.Aliyun.Speech.AccessKeySecret
	ctx := context.Background()
	// 1. 先查redis
	val, err := global.GVA_REDIS.Get(ctx, aliyunTokenKey+":"+ak).Result()
	if err == nil && val != "" {
		return val, nil
	}

	// 2. 使用阿里云官方 SDK 请求 Token
	client, err := sdk.NewClientWithAccessKey("cn-shanghai", ak, sk)
	if err != nil {
		return "", err
	}

	request := requests.NewCommonRequest()
	request.Method = "POST"
	request.Domain = "nls-meta.cn-shanghai.aliyuncs.com"
	request.ApiName = "CreateToken"
	request.Version = "2019-02-28"

	response, err := client.ProcessCommonRequest(request)
	if err != nil {
		return "", err
	}

	if response.GetHttpStatus() != 200 {
		return "", errors.New("获取Token失败: HTTP状态码 " + string(rune(response.GetHttpStatus())))
	}

	var tokenResult TokenResult
	err = json.Unmarshal([]byte(response.GetHttpContentString()), &tokenResult)
	if err != nil {
		return "", err
	}

	if tokenResult.Token.Id == "" || tokenResult.Token.ExpireTime == 0 {
		return "", errors.New("Token响应异常: " + response.GetHttpContentString())
	}

	if tokenResult.ErrMsg != "" {
		return "", errors.New("获取Token失败: " + tokenResult.ErrMsg)
	}

	// 3. 存入redis，过期时间比阿里云返回的expires_in略短
	expire := time.Duration(tokenResult.Token.ExpireTime-60) * time.Second // 提前1分钟过期
	err = global.GVA_REDIS.Set(ctx, aliyunTokenKey+":"+ak, tokenResult.Token.Id, expire).Err()
	if err != nil {
		return "", err
	}
	return tokenResult.Token.Id, nil
}

package product

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/product"
	productReq "github.com/flipped-aurora/gin-vue-admin/server/model/product/request"
	productRes "github.com/flipped-aurora/gin-vue-admin/server/model/product/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	integralService "github.com/flipped-aurora/gin-vue-admin/server/service/integral"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type MembershipService struct{}

var MembershipServiceApp = new(MembershipService)

// GetUserMembershipStatus 获取用户会员状态
func (m *MembershipService) GetUserMembershipStatus(userID uint) (*productRes.UserMembershipStatusResponse, error) {
	// 查询用户当前激活的会员
	var activeMembership product.UserMembership
	err := global.GVA_DB.Where("user_id = ? AND status = ? AND start_time <= ? AND end_time > ?",
		userID, product.MembershipStatusActive, time.Now(), time.Now()).
		Order("end_time desc").First(&activeMembership).Error

	response := &productRes.UserMembershipStatusResponse{
		HasActiveMembership: false,
		VipLevel:            0,
	}

	if err == nil {
		// 有激活的会员
		response.HasActiveMembership = true
		response.VipLevel = activeMembership.GetVipLevel()

		membershipResp := m.convertMembershipToResponse(activeMembership)
		response.CurrentMembership = &membershipResp
		response.MonthlyPoints = activeMembership.MonthlyPoints
		endTime := response.CurrentMembership.EndTime
		//转string，yyyy-MM-dd
		response.CurrentMembership.VipExpireAt = endTime.Format("2006-01-02")
		// 计算下次积分发放时间
		if activeMembership.LastPointTime != nil {
			nextPointTime := activeMembership.LastPointTime.AddDate(0, 0, 30)
			response.NextPointTime = &nextPointTime
		} else {
			// 如果没有发放过积分，则从开始时间算起
			nextPointTime := activeMembership.StartTime.AddDate(0, 0, 30)
			response.NextPointTime = &nextPointTime
		}
	}

	// 查询用户总积分
	var user system.SysUser
	if err := global.GVA_DB.First(&user, userID).Error; err == nil {
		response.TotalPoints = user.Points
	}

	return response, nil
}

// GetUserMembershipList 获取用户会员列表
func (m *MembershipService) GetUserMembershipList(req productReq.UserMembershipSearch) (*productRes.UserMembershipListResponse, error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	db := global.GVA_DB.Model(&product.UserMembership{})

	// 添加查询条件
	if req.UserID != nil {
		db = db.Where("user_id = ?", *req.UserID)
	}
	if req.MembershipType != nil {
		db = db.Where("membership_type = ?", *req.MembershipType)
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}

	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取会员总数失败: %v", err)
	}

	var memberships []product.UserMembership
	if err := db.Limit(limit).Offset(offset).Order("id desc").Find(&memberships).Error; err != nil {
		return nil, fmt.Errorf("获取会员列表失败: %v", err)
	}

	// 转换为响应格式
	var membershipResponses []productRes.UserMembershipResponse
	for _, membership := range memberships {
		membershipResponses = append(membershipResponses, m.convertMembershipToResponse(membership))
	}

	return &productRes.UserMembershipListResponse{
		List:  membershipResponses,
		Total: total,
	}, nil
}

// convertMembershipToResponse 转换会员为响应格式
func (m *MembershipService) convertMembershipToResponse(membership product.UserMembership) productRes.UserMembershipResponse {
	response := productRes.UserMembershipResponse{
		UserMembership: membership,
		IsActive:       membership.IsActive(),
		RemainingDays:  membership.RemainingDays(),
		VipLevel:       membership.GetVipLevel(),
	}

	// 解析特权JSON
	if membership.Features != "" {
		var features []string
		json.Unmarshal([]byte(membership.Features), &features)
		response.FeaturesArray = features
	}

	return response
}

// GrantMonthlyPoints 发放月度积分（定时任务调用）
func (m *MembershipService) GrantMonthlyPoints() error {
	// 查询需要发放积分的会员
	now := time.Now()

	// 查询条件：激活状态 + 未过期 + 月积分 > 0 + 满足30天周期条件
	var memberships []product.UserMembership

	// 构建复杂查询 - 基于购买日期每30天发放积分
	db := global.GVA_DB.Where("status = ? AND end_time > ? AND monthly_points > 0",
		product.MembershipStatusActive, now)

	// 添加时间条件：
	// 1. 没有发放过积分且距离开始时间满30天
	// 2. 已发放过积分且距离上次发放时间满30天
	thirtyDaysAgo := now.AddDate(0, 0, -30)
	db = db.Where("(last_point_time IS NULL AND start_time <= ?) OR (last_point_time IS NOT NULL AND last_point_time <= ?)",
		thirtyDaysAgo, thirtyDaysAgo)

	if err := db.Find(&memberships).Error; err != nil {
		global.GVA_LOG.Error("查询需要发放积分的会员失败", zap.Error(err))
		return fmt.Errorf("查询需要发放积分的会员失败: %v", err)
	}

	// 逐个发放积分
	successCount := 0
	for _, membership := range memberships {
		// 验证是否真的需要发放积分（精确到天数计算）
		if m.shouldGrantPoints(membership, now) {
			if err := m.grantPointsToUser(membership); err != nil {
				global.GVA_LOG.Error("发放积分失败",
					zap.Uint("userID", membership.UserID),
					zap.String("membershipType", string(membership.MembershipType)),
					zap.Error(err))
				continue
			}
			successCount++
		}
	}

	global.GVA_LOG.Info("积分发放检查完成",
		zap.Int("totalChecked", len(memberships)),
		zap.Int("successCount", successCount))
	return nil
}

// shouldGrantPoints 检查是否应该发放积分
func (m *MembershipService) shouldGrantPoints(membership product.UserMembership, now time.Time) bool {
	// 如果从未发放过积分，检查距离开始时间是否满30天
	if membership.LastPointTime == nil {
		daysSinceStart := int(now.Sub(membership.StartTime).Hours() / 24)
		return daysSinceStart >= 30
	}

	// 如果已经发放过积分，检查距离上次发放是否满30天
	daysSinceLastGrant := int(now.Sub(*membership.LastPointTime).Hours() / 24)
	return daysSinceLastGrant >= 30
}

// grantPointsToUser 给单个用户发放积分
func (m *MembershipService) grantPointsToUser(membership product.UserMembership) error {
	// 获取会员月度积分任务ID
	taskID := uint(config.TASK_MEMBERSHIP_MONTHLY)

	// 使用任务服务发放积分并记录任务完成
	taskService := integralService.TaskServiceApp
	err := taskService.AddPointsWithTaskRecord(
		membership.UserID,
		membership.MonthlyPoints,
		fmt.Sprintf("会员[%s]月度积分", membership.MembershipType),
		"membership",
		taskID,
	)
	if err != nil {
		return fmt.Errorf("发放会员月度积分失败: %v", err)
	}

	// 更新会员的最后积分发放时间
	now := time.Now()
	if err := global.GVA_DB.Model(&membership).Updates(map[string]interface{}{
		"last_point_time": now,
		"total_points":    gorm.Expr("total_points + ?", membership.MonthlyPoints),
	}).Error; err != nil {
		return fmt.Errorf("更新会员积分发放时间失败: %v", err)
	}

	return nil
}

// UpdateExpiredMemberships 更新过期会员状态（定时任务调用）
func (m *MembershipService) UpdateExpiredMemberships() error {
	now := time.Now()

	// 更新过期的会员状态
	result := global.GVA_DB.Model(&product.UserMembership{}).
		Where("status = ? AND end_time <= ?", product.MembershipStatusActive, now).
		Update("status", product.MembershipStatusExpired)

	if result.Error != nil {
		global.GVA_LOG.Error("更新过期会员状态失败", zap.Error(result.Error))
		return fmt.Errorf("更新过期会员状态失败: %v", result.Error)
	}

	// 同时需要更新用户的VIP等级
	var expiredUsers []uint
	global.GVA_DB.Model(&product.UserMembership{}).
		Where("status = ? AND end_time <= ?", product.MembershipStatusExpired, now).
		Distinct("user_id").Pluck("user_id", &expiredUsers)

	for _, userID := range expiredUsers {
		if err := m.updateUserVipLevel(userID); err != nil {
			global.GVA_LOG.Error("更新用户VIP等级失败",
				zap.Uint("userID", userID), zap.Error(err))
		}
	}

	global.GVA_LOG.Info("过期会员状态更新完成", zap.Int64("count", result.RowsAffected))
	return nil
}

// updateUserVipLevel 更新用户VIP等级
func (m *MembershipService) updateUserVipLevel(userID uint) error {
	// 查询用户当前最高有效会员等级
	var membership product.UserMembership
	err := global.GVA_DB.Where("user_id = ? AND status = ? AND start_time <= ? AND end_time > ?",
		userID, product.MembershipStatusActive, time.Now(), time.Now()).
		Order("membership_type desc").First(&membership).Error

	var vipLevel int
	if err == gorm.ErrRecordNotFound {
		// 没有有效会员
		vipLevel = 0
	} else if err != nil {
		return fmt.Errorf("查询用户会员失败: %v", err)
	} else {
		vipLevel = membership.GetVipLevel()
	}

	// 更新用户VIP等级
	if err := global.GVA_DB.Model(&system.SysUser{}).Where("id = ?", userID).
		Update("vip_level", vipLevel).Error; err != nil {
		return fmt.Errorf("更新用户VIP等级失败: %v", err)
	}

	return nil
}

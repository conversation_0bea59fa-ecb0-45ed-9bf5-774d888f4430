package product

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/model/product"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/product/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/product/response"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/gorm"
)

type UserAddressService struct{}

// CreateUserAddress 创建用户地址
func (u *UserAddressService) CreateUserAddress(userID uint, req systemReq.CreateUserAddressRequest) (*systemRes.UserAddressResponse, error) {
	return u.createOrUpdateAddress(userID, req, 0)
}

// UpdateUserAddress 更新用户地址
func (u *UserAddressService) UpdateUserAddress(userID uint, req systemReq.UpdateUserAddressRequest) (*systemRes.UserAddressResponse, error) {
	// 检查地址是否属于当前用户
	var existingAddress product.UserAddress
	if err := global.GVA_DB.Where("id = ? AND user_id = ?", req.ID, userID).First(&existingAddress).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("地址不存在或无权限访问")
		}
		return nil, fmt.Errorf("查询地址失败: %v", err)
	}

	return u.createOrUpdateAddress(userID, req.CreateUserAddressRequest, req.ID)
}

// createOrUpdateAddress 创建或更新地址的通用方法
func (u *UserAddressService) createOrUpdateAddress(userID uint, req systemReq.CreateUserAddressRequest, addressID uint) (*systemRes.UserAddressResponse, error) {
	return u.executeInTransaction(func(tx *gorm.DB) (*systemRes.UserAddressResponse, error) {
		// 如果设置为默认地址，先取消其他默认地址
		if req.IsDefault {
			if err := tx.Model(&product.UserAddress{}).
				Where("user_id = ? AND is_default = true", userID).
				Update("is_default", false).Error; err != nil {
				return nil, fmt.Errorf("取消默认地址失败: %v", err)
			}
		}

		var address product.UserAddress
		if addressID > 0 {
			// 更新操作
			if err := tx.Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
				return nil, fmt.Errorf("地址不存在: %v", err)
			}

			// 更新字段
			address.Name = req.Name
			address.Phone = req.Phone
			address.Province = req.Province
			address.City = req.City
			address.District = req.District
			address.Address = req.Address
			address.Zipcode = req.Zipcode
			address.Label = req.Label
			address.IsDefault = req.IsDefault

			if err := tx.Save(&address).Error; err != nil {
				return nil, fmt.Errorf("更新地址失败: %v", err)
			}
		} else {
			// 创建操作
			address = product.UserAddress{
				UserID:    userID,
				Name:      req.Name,
				Phone:     req.Phone,
				Province:  req.Province,
				City:      req.City,
				District:  req.District,
				Address:   req.Address,
				Zipcode:   req.Zipcode,
				Label:     req.Label,
				IsDefault: req.IsDefault,
				Status:    int(product.AddressStatusNormal),
			}

			if err := tx.Create(&address).Error; err != nil {
				return nil, fmt.Errorf("创建地址失败: %v", err)
			}
		}

		response := systemRes.ConvertToUserAddressResponse(address)
		return &response, nil
	})
}

// GetUserAddressList 获取用户地址列表
func (u *UserAddressService) GetUserAddressList(userID uint, req systemReq.UserAddressSearch) (*systemRes.UserAddressListResponse, error) {
	var addresses []product.UserAddress
	var total int64

	db := global.GVA_DB.Model(&product.UserAddress{}).Where("user_id = ?", userID)

	// 添加查询条件
	if req.Label != nil && *req.Label != "" {
		db = db.Where("label LIKE ?", "%"+*req.Label+"%")
	}
	if req.Province != nil && *req.Province != "" {
		db = db.Where("province = ?", *req.Province)
	}
	if req.City != nil && *req.City != "" {
		db = db.Where("city = ?", *req.City)
	}
	if req.IsDefault != nil {
		db = db.Where("is_default = ?", *req.IsDefault)
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取地址总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := db.Order("is_default DESC, updated_at DESC").
		Offset(offset).Limit(req.PageSize).Find(&addresses).Error; err != nil {
		return nil, fmt.Errorf("获取地址列表失败: %v", err)
	}

	return &systemRes.UserAddressListResponse{
		List:     systemRes.ConvertToUserAddressResponseList(addresses),
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetUserAddress 获取单个地址
func (u *UserAddressService) GetUserAddress(userID, addressID uint) (*systemRes.UserAddressResponse, error) {
	var address product.UserAddress
	if err := global.GVA_DB.Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("地址不存在")
		}
		return nil, fmt.Errorf("查询地址失败: %v", err)
	}

	response := systemRes.ConvertToUserAddressResponse(address)
	return &response, nil
}

// DeleteUserAddress 删除地址
func (u *UserAddressService) DeleteUserAddress(userID, addressID uint) error {
	// 检查地址是否存在且属于当前用户
	var address product.UserAddress
	if err := global.GVA_DB.Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("地址不存在")
		}
		return fmt.Errorf("查询地址失败: %v", err)
	}

	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 删除地址
		if err := tx.Delete(&address).Error; err != nil {
			return fmt.Errorf("删除地址失败: %v", err)
		}

		// 如果删除的是默认地址，自动设置第一个地址为默认地址
		if address.IsDefault {
			var firstAddress product.UserAddress
			if err := tx.Where("user_id = ?", userID).First(&firstAddress).Error; err == nil {
				tx.Model(&firstAddress).Update("is_default", true)
			}
		}

		return nil
	})
}

// SetDefaultAddress 设置默认地址
func (u *UserAddressService) SetDefaultAddress(userID uint, req systemReq.SetDefaultAddressRequest) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 检查地址是否存在且属于当前用户
		var address product.UserAddress
		if err := tx.Where("id = ? AND user_id = ?", req.ID, userID).First(&address).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("地址不存在")
			}
			return fmt.Errorf("查询地址失败: %v", err)
		}

		// 取消其他默认地址
		if err := tx.Model(&product.UserAddress{}).
			Where("user_id = ? AND is_default = true", userID).
			Update("is_default", false).Error; err != nil {
			return fmt.Errorf("取消默认地址失败: %v", err)
		}

		// 设置当前地址为默认
		if err := tx.Model(&address).Update("is_default", true).Error; err != nil {
			return fmt.Errorf("设置默认地址失败: %v", err)
		}

		return nil
	})
}

// GetDefaultAddress 获取默认地址
func (u *UserAddressService) GetDefaultAddress(userID uint) (*systemRes.UserAddressResponse, error) {
	var address product.UserAddress
	if err := global.GVA_DB.Where("user_id = ? AND is_default = true", userID).First(&address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("未设置默认地址")
		}
		return nil, fmt.Errorf("查询默认地址失败: %v", err)
	}

	response := systemRes.ConvertToUserAddressResponse(address)
	return &response, nil
}

// executeInTransaction 执行事务的通用方法
func (u *UserAddressService) executeInTransaction(fn func(tx *gorm.DB) (*systemRes.UserAddressResponse, error)) (*systemRes.UserAddressResponse, error) {
	var result *systemRes.UserAddressResponse
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var txErr error
		result, txErr = fn(tx)
		return txErr
	})
	return result, err
}

package product

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	"github.com/flipped-aurora/gin-vue-admin/server/model/payment"
	"github.com/flipped-aurora/gin-vue-admin/server/model/product"
	productReq "github.com/flipped-aurora/gin-vue-admin/server/model/product/request"
	productRes "github.com/flipped-aurora/gin-vue-admin/server/model/product/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	integralService "github.com/flipped-aurora/gin-vue-admin/server/service/integral"
	paymentService "github.com/flipped-aurora/gin-vue-admin/server/service/payment"
	paymentUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/payment"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PurchaseService struct{}

var PurchaseServiceApp = new(PurchaseService)

// BuyProduct 购买商品
func (p *PurchaseService) BuyProduct(userID uint, req productReq.BuyProductRequest) (*productRes.BuyProductResponse, error) {
	// 获取商品信息
	var productModel product.Product
	if err := global.GVA_DB.First(&productModel, req.ProductID).Error; err != nil {
		return nil, fmt.Errorf("商品不存在")
	}

	// 检查商品状态
	if productModel.Status != product.ProductStatusOnline {
		return nil, fmt.Errorf("商品已下架")
	}

	// 检查库存
	if productModel.Stock != -1 && productModel.Stock < req.Quantity {
		return nil, fmt.Errorf("库存不足")
	}

	// 计算总价
	totalPrice := productModel.Price * int64(req.Quantity)

	// 处理收货地址
	var finalShippingAddress *productReq.ShippingAddressRequest
	var shippingAddressJSON string

	// 实物商品需要处理地址
	if productModel.Type == product.ProductTypePhysical {
		// 获取收货地址
		address, err := p.getShippingAddress(userID, req)
		if err != nil {
			return nil, err
		}
		finalShippingAddress = address

		// 计算运费
		totalPrice += productModel.ShippingFee

		// 序列化地址
		if finalShippingAddress != nil {
			addressBytes, _ := json.Marshal(finalShippingAddress)
			shippingAddressJSON = string(addressBytes)
		}
	}

	// 开始事务
	var orderNo string
	var paymentURL string

	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 创建支付订单
		paymentReq := paymentUtils.CreatePaymentRequest{
			UserID:        userID,
			Amount:        totalPrice,
			Subject:       productModel.Name,
			Body:          productModel.Description,
			DeviceType:    paymentUtils.DeviceType(req.DeviceType),
			PaymentMethod: payment.PaymentMethod(req.PaymentMethod),
			ExpireMinutes: req.ExpireMinutes,
			ClientIP:      req.ClientIP,
		}

		// 添加商品相关的额外信息
		if paymentReq.Extra == nil {
			paymentReq.Extra = make(map[string]interface{})
		}
		// 微信JSAPI支付需要OpenID
		if req.PaymentMethod == string(payment.PaymentMethodWechat) &&
			req.DeviceType == string(paymentUtils.DeviceTypeWechat) {
			if req.OpenID == "" {
				return fmt.Errorf("微信JSAPI支付需要提供用户OpenID。如果不在微信环境中，请将deviceType改为'mobile'或'pc'")
			}
			paymentReq.OpenID = req.OpenID
		}

		global.GVA_LOG.Info("创建支付订单",
			zap.String("productName", productModel.Name),
			zap.Int64("amount", totalPrice),
			zap.String("paymentMethod", req.PaymentMethod),
			zap.Uint("userId", userID))

		paymentResp, err := paymentService.PaymentServiceApp.CreatePayment(paymentReq)
		if err != nil {
			global.GVA_LOG.Error("创建支付订单失败",
				zap.Error(err),
				zap.String("productName", productModel.Name),
				zap.Uint("userId", userID))
			return fmt.Errorf("创建支付订单失败: %v", err)
		}

		orderNo = paymentResp.OrderNo
		paymentURL = paymentResp.PaymentURL

		global.GVA_LOG.Info("支付订单创建成功",
			zap.String("orderNo", orderNo),
			zap.String("productName", productModel.Name))

		// 创建订单项
		orderItem := &product.OrderItem{
			OrderNo:     orderNo,
			ProductID:   req.ProductID,
			ProductName: productModel.Name,
			ProductType: productModel.Type,
			UnitPrice:   productModel.Price,
			Quantity:    req.Quantity,
			TotalPrice:  totalPrice,

			// 会员商品相关
			MembershipType: productModel.MembershipType,
			Duration:       productModel.Duration,
			DurationUnit:   productModel.DurationUnit,
			MonthlyPoints:  productModel.MonthlyPoints,

			// 实物商品相关
			ShippingFee:     productModel.ShippingFee,
			ShippingAddress: shippingAddressJSON,
			ShippingStatus:  product.ShippingStatusPending,

			Status: product.OrderItemStatusPending,
		}

		if err := tx.Create(orderItem).Error; err != nil {
			global.GVA_LOG.Error("创建订单项失败", zap.Error(err), zap.String("orderNo", orderNo))
			return fmt.Errorf("创建订单项失败: %v", err)
		}

		// 减少库存（仅实物商品）
		if productModel.Type == product.ProductTypePhysical && productModel.Stock != -1 {
			result := tx.Model(&productModel).UpdateColumn("stock", gorm.Expr("stock - ?", req.Quantity))
			if result.Error != nil {
				global.GVA_LOG.Error("更新库存失败", zap.Error(result.Error), zap.String("orderNo", orderNo))
				return fmt.Errorf("更新库存失败: %v", result.Error)
			}
			if result.RowsAffected == 0 {
				return fmt.Errorf("库存不足或商品已被删除")
			}
		}

		global.GVA_LOG.Info("商品购买订单创建完成",
			zap.String("orderNo", orderNo),
			zap.String("productName", productModel.Name),
			zap.Int("quantity", req.Quantity))

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &productRes.BuyProductResponse{
		OrderNo:    orderNo,
		PaymentURL: paymentURL,
		Amount:     totalPrice,
	}, nil
}

// getShippingAddress 获取收货地址
func (p *PurchaseService) getShippingAddress(userID uint, req productReq.BuyProductRequest) (*productReq.ShippingAddressRequest, error) {
	// 优先使用地址ID
	if req.AddressID != nil {
		return p.getAddressByID(userID, *req.AddressID)
	}

	// 其次使用直接传入的地址
	if req.ShippingAddress != nil {
		return req.ShippingAddress, nil
	}

	// 如果都没有，尝试获取默认地址
	defaultAddress, err := p.getDefaultAddress(userID)
	if err != nil {
		return nil, fmt.Errorf("实物商品需要提供收货地址")
	}

	return defaultAddress, nil
}

// getAddressByID 通过地址ID获取地址信息
func (p *PurchaseService) getAddressByID(userID, addressID uint) (*productReq.ShippingAddressRequest, error) {
	var address product.UserAddress
	if err := global.GVA_DB.Where("id = ? AND user_id = ? AND status = ?",
		addressID, userID, product.AddressStatusNormal).First(&address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("地址不存在或已禁用")
		}
		return nil, fmt.Errorf("查询地址失败: %v", err)
	}

	return &productReq.ShippingAddressRequest{
		Name:     address.Name,
		Phone:    address.Phone,
		Province: address.Province,
		City:     address.City,
		District: address.District,
		Address:  address.Address,
		Zipcode:  address.Zipcode,
	}, nil
}

// getDefaultAddress 获取用户默认地址
func (p *PurchaseService) getDefaultAddress(userID uint) (*productReq.ShippingAddressRequest, error) {
	var address product.UserAddress
	if err := global.GVA_DB.Where("user_id = ? AND is_default = true AND status = ?",
		userID, product.AddressStatusNormal).First(&address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("未设置默认地址")
		}
		return nil, fmt.Errorf("查询默认地址失败: %v", err)
	}

	return &productReq.ShippingAddressRequest{
		Name:     address.Name,
		Phone:    address.Phone,
		Province: address.Province,
		City:     address.City,
		District: address.District,
		Address:  address.Address,
		Zipcode:  address.Zipcode,
	}, nil
}

// ProcessPaymentSuccess 处理支付成功后的逻辑
func (p *PurchaseService) ProcessPaymentSuccess(orderNo string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 获取订单项
		var orderItems []product.OrderItem
		if err := tx.Where("order_no = ?", orderNo).Find(&orderItems).Error; err != nil {
			return fmt.Errorf("获取订单项失败: %v", err)
		}

		// 防重复处理：检查是否已经处理过
		alreadyProcessed := true
		for _, item := range orderItems {
			if item.Status != product.OrderItemStatusProcessed {
				alreadyProcessed = false
				break
			}
		}

		if alreadyProcessed {
			global.GVA_LOG.Info("订单已处理过，跳过重复处理", zap.String("orderNo", orderNo))
			return nil
		}

		for _, item := range orderItems {
			// 再次检查单个订单项状态（在事务中确保数据一致性）
			var currentItem product.OrderItem
			if err := tx.Where("id = ? AND order_no = ?", item.ID, orderNo).First(&currentItem).Error; err != nil {
				return fmt.Errorf("获取订单项详情失败: %v", err)
			}

			// 如果该订单项已处理，跳过
			if currentItem.Status == product.OrderItemStatusProcessed {
				global.GVA_LOG.Info("订单项已处理，跳过",
					zap.String("orderNo", orderNo),
					zap.Uint("itemId", item.ID))
				continue
			}

			// 处理会员商品
			if item.ProductType == product.ProductTypeMembership {
				if err := p.activateMembership(tx, orderNo, item); err != nil {
					return fmt.Errorf("激活会员失败: %v", err)
				}
			}

			// 更新订单项状态为已处理（使用乐观锁防止重复更新）
			result := tx.Model(&product.OrderItem{}).
				Where("id = ? AND status = ?", item.ID, product.OrderItemStatusPending).
				Update("status", product.OrderItemStatusProcessed)

			if result.Error != nil {
				return fmt.Errorf("更新订单项状态失败: %v", result.Error)
			}

			// 如果受影响行数为0，说明状态已被其他进程更新，跳过后续处理
			if result.RowsAffected == 0 {
				global.GVA_LOG.Warn("订单项状态已被更新，可能重复处理",
					zap.String("orderNo", orderNo),
					zap.Uint("itemId", item.ID))
				continue
			}

			// 更新商品销量（使用原子操作）
			if err := tx.Model(&product.Product{}).Where("id = ?", item.ProductID).
				UpdateColumn("sales_count", gorm.Expr("sales_count + ?", item.Quantity)).Error; err != nil {
				global.GVA_LOG.Warn("更新商品销量失败", zap.Error(err))
			}
		}

		global.GVA_LOG.Info("支付成功处理完成", zap.String("orderNo", orderNo))
		return nil
	})
}

// activateMembership 激活会员
func (p *PurchaseService) activateMembership(tx *gorm.DB, orderNo string, item product.OrderItem) error {
	// 获取支付订单信息
	var paymentOrder payment.PaymentOrder
	if err := tx.Where("order_no = ?", orderNo).First(&paymentOrder).Error; err != nil {
		return fmt.Errorf("获取支付订单失败: %v", err)
	}

	userID := paymentOrder.UserID

	// 防重复激活：检查是否已经为此订单激活过会员
	var existingActivation product.UserMembership
	err := tx.Where("user_id = ? AND order_no = ? AND product_id = ?",
		userID, orderNo, item.ProductID).First(&existingActivation).Error

	if err == nil {
		// 已经为此订单激活过会员，跳过
		global.GVA_LOG.Info("订单已激活过会员，跳过重复激活",
			zap.String("orderNo", orderNo),
			zap.Uint("userId", userID),
			zap.Uint("productId", item.ProductID))
		return nil
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查会员激活记录失败: %v", err)
	}

	// 计算会员时间
	startTime := time.Now()
	endTime := startTime.AddDate(0, 0, item.Duration)

	// 检查是否已有同类型的活跃会员（非当前订单激活的）
	var existingMembership product.UserMembership
	err = tx.Where("user_id = ? AND membership_type = ? AND status = ? AND order_no != ?",
		userID, item.MembershipType, product.MembershipStatusActive, orderNo).First(&existingMembership).Error

	if err == nil {
		// 已有同类型会员，延长时间
		endTime = existingMembership.EndTime.AddDate(0, 0, item.Duration)

		if err := tx.Model(&existingMembership).Updates(map[string]interface{}{
			"end_time":       endTime,
			"duration":       existingMembership.Duration + item.Duration,
			"monthly_points": item.MonthlyPoints, // 更新月积分
		}).Error; err != nil {
			return fmt.Errorf("延长会员时间失败: %v", err)
		}

		global.GVA_LOG.Info("延长现有会员时间",
			zap.String("orderNo", orderNo),
			zap.Uint("userId", userID),
			zap.String("membershipType", string(item.MembershipType)),
			zap.Int("addDays", item.Duration))

	} else if err == gorm.ErrRecordNotFound {
		// 创建新会员记录
		membership := &product.UserMembership{
			UserID:         userID,
			MembershipType: item.MembershipType,
			ProductID:      item.ProductID,
			OrderNo:        orderNo,
			StartTime:      startTime,
			EndTime:        endTime,
			Duration:       item.Duration,
			MonthlyPoints:  item.MonthlyPoints,
			Status:         product.MembershipStatusActive,
			AutoRenew:      false,
		}

		if err := tx.Create(membership).Error; err != nil {
			return fmt.Errorf("创建会员记录失败: %v", err)
		}

		global.GVA_LOG.Info("创建新会员记录",
			zap.String("orderNo", orderNo),
			zap.Uint("userId", userID),
			zap.String("membershipType", string(item.MembershipType)))

	} else {
		return fmt.Errorf("查询会员记录失败: %v", err)
	}

	// 更新用户VIP等级
	var newVipLevel int
	switch item.MembershipType {
	case product.MembershipTypePro:
		newVipLevel = 1
	case product.MembershipTypeUltra:
		newVipLevel = 2
	}

	// 只有当新的VIP等级更高时才更新
	var user system.SysUser
	if err := tx.First(&user, userID).Error; err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	if newVipLevel > user.VipLevel {
		if err := tx.Model(&user).Update("vip_level", newVipLevel).Error; err != nil {
			return fmt.Errorf("更新用户VIP等级失败: %v", err)
		}
	}

	// 发放积分（防重复发放）
	if item.MonthlyPoints > 0 {
		// 检查是否已经为此订单发放过积分
		var pointsRecord integral.SysUserPoints
		err := tx.Where("user_id = ? AND order_id = ? AND type = 'order'",
			userID, paymentOrder.ID).First(&pointsRecord).Error

		if err == gorm.ErrRecordNotFound {
			// 未发放过积分，进行发放
			months := item.Duration / 30
			if months == 0 {
				months = 1 // 至少发放一个月的积分
			}

			totalPoints := item.MonthlyPoints * months

			// 使用任务服务发放积分并记录任务完成
			// 获取会员购买任务ID
			taskID := uint(config.TASK_MEMBERSHIP_MONTHLY)

			taskService := integralService.TaskServiceApp
			err := taskService.AddPointsWithTaskRecord(
				userID,
				totalPoints,
				fmt.Sprintf("购买会员[%s]赠送积分", item.MembershipType),
				"order",
				taskID,
			)
			if err != nil {
				return fmt.Errorf("发放会员积分失败: %v", err)
			}

			// 更新积分流水记录的OrderID
			if err := tx.Table("sys_user_points").
				Where("user_id = ? AND type = 'order' AND task_id = ? AND order_id = 0",
					userID, taskID).
				Update("order_id", paymentOrder.ID).Error; err != nil {
				global.GVA_LOG.Warn("更新积分流水OrderID失败", zap.Error(err))
			}

			global.GVA_LOG.Info("发放会员积分",
				zap.String("orderNo", orderNo),
				zap.Uint("userId", userID),
				zap.Int("points", totalPoints))

		} else if err != nil {
			return fmt.Errorf("检查积分发放记录失败: %v", err)
		} else {
			// 已经发放过积分，跳过
			global.GVA_LOG.Info("积分已发放过，跳过",
				zap.String("orderNo", orderNo),
				zap.Uint("userId", userID))
		}
	}

	return nil
}

// CancelOrder 取消订单
func (p *PurchaseService) CancelOrder(userID uint, orderNo string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查询订单项
		var orderItems []product.OrderItem
		if err := tx.Where("order_no = ?", orderNo).Find(&orderItems).Error; err != nil {
			return fmt.Errorf("查询订单失败: %v", err)
		}

		if len(orderItems) == 0 {
			return fmt.Errorf("订单不存在")
		}

		// 检查是否可以取消（只有pending状态才能取消）
		for _, item := range orderItems {
			if item.Status != product.OrderItemStatusPending {
				return fmt.Errorf("订单状态不允许取消")
			}
		}

		// 查询支付订单
		var paymentOrder payment.PaymentOrder
		if err := tx.Where("order_no = ? AND user_id = ?", orderNo, userID).First(&paymentOrder).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("支付订单不存在或无权限")
			}
			return fmt.Errorf("查询支付订单失败: %v", err)
		}

		// 检查支付状态
		if paymentOrder.Status == payment.PaymentStatusPaid {
			return fmt.Errorf("订单已支付，无法取消")
		}

		// 更新订单项状态
		if err := tx.Model(&product.OrderItem{}).Where("order_no = ?", orderNo).
			Update("status", product.OrderItemStatusCancelled).Error; err != nil {
			return fmt.Errorf("更新订单项状态失败: %v", err)
		}

		// 更新支付订单状态
		if err := tx.Model(&paymentOrder).Update("status", payment.PaymentStatusCancelled).Error; err != nil {
			return fmt.Errorf("更新支付订单状态失败: %v", err)
		}

		// 恢复库存（仅实物商品）
		for _, item := range orderItems {
			if item.ProductType == product.ProductTypePhysical {
				if err := tx.Model(&product.Product{}).Where("id = ?", item.ProductID).
					UpdateColumn("stock", gorm.Expr("stock + ?", item.Quantity)).Error; err != nil {
					global.GVA_LOG.Warn("恢复库存失败", zap.Error(err), zap.Uint("productId", item.ProductID))
				}
			}
		}

		global.GVA_LOG.Info("订单取消成功",
			zap.String("orderNo", orderNo),
			zap.Uint("userId", userID))

		return nil
	})
}

// InitEventListeners 初始化事件监听器
func InitEventListeners() {
	// 监听支付成功事件
	global.GVA_EVENT_MANAGER.Subscribe(global.EventTypePaymentSuccess, func(data interface{}) error {
		event, ok := data.(global.PaymentSuccessEvent)
		if !ok {
			return fmt.Errorf("事件数据类型错误")
		}

		global.GVA_LOG.Info("收到支付成功事件", zap.String("orderNo", event.OrderNo))

		// 调用 ProcessPaymentSuccess 方法
		return PurchaseServiceApp.ProcessPaymentSuccess(event.OrderNo)
	})

	global.GVA_LOG.Info("产品服务事件监听器初始化完成")
}

package product

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/product"
	productReq "github.com/flipped-aurora/gin-vue-admin/server/model/product/request"
	productRes "github.com/flipped-aurora/gin-vue-admin/server/model/product/response"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ProductService struct{}

var ProductServiceApp = new(ProductService)

// CreateProduct 创建商品
func (p *ProductService) CreateProduct(req productReq.CreateProductRequest) (*product.Product, error) {
	// 序列化图片数组
	imagesJSON, _ := json.Marshal(req.Images)
	tagsJSON, _ := json.Marshal(req.Tags)
	featuresJSON, _ := json.Marshal(req.Features)

	// 计算实际天数
	duration := p.calculateDuration(req.Duration, req.DurationUnit)

	productModel := &product.Product{
		Name:          req.Name,
		Description:   req.Description,
		Type:          req.Type,
		Category:      req.Category,
		Price:         req.Price,
		OriginalPrice: req.OriginalPrice,
		Stock:         req.Stock,
		Status:        product.ProductStatusOnline,
		CoverImage:    req.CoverImage,
		Images:        string(imagesJSON),
		Tags:          string(tagsJSON),

		// 会员商品相关
		MembershipType: req.MembershipType,
		Duration:       duration,
		DurationUnit:   req.DurationUnit,
		MonthlyPoints:  req.MonthlyPoints,
		Features:       string(featuresJSON),

		// 实物商品相关
		Weight:      req.Weight,
		Dimensions:  req.Dimensions,
		ShippingFee: req.ShippingFee,
	}

	// 设置上架时间
	now := time.Now()
	productModel.LaunchTime = &now

	if err := global.GVA_DB.Create(productModel).Error; err != nil {
		global.GVA_LOG.Error("创建商品失败", zap.Error(err))
		return nil, fmt.Errorf("创建商品失败: %v", err)
	}

	return productModel, nil
}

// UpdateProduct 更新商品
func (p *ProductService) UpdateProduct(req productReq.UpdateProductRequest) (*product.Product, error) {
	var productModel product.Product
	if err := global.GVA_DB.First(&productModel, req.ID).Error; err != nil {
		return nil, fmt.Errorf("商品不存在")
	}

	// 序列化数组字段
	imagesJSON, _ := json.Marshal(req.Images)
	tagsJSON, _ := json.Marshal(req.Tags)
	featuresJSON, _ := json.Marshal(req.Features)

	// 计算实际天数
	duration := p.calculateDuration(req.Duration, req.DurationUnit)

	// 更新字段
	updates := map[string]interface{}{
		"name":           req.Name,
		"description":    req.Description,
		"type":           req.Type,
		"category":       req.Category,
		"price":          req.Price,
		"original_price": req.OriginalPrice,
		"stock":          req.Stock,
		"cover_image":    req.CoverImage,
		"images":         string(imagesJSON),
		"tags":           string(tagsJSON),

		// 会员商品相关
		"membership_type": req.MembershipType,
		"duration":        duration,
		"duration_unit":   req.DurationUnit,
		"monthly_points":  req.MonthlyPoints,
		"features":        string(featuresJSON),

		// 实物商品相关
		"weight":       req.Weight,
		"dimensions":   req.Dimensions,
		"shipping_fee": req.ShippingFee,
	}

	if err := global.GVA_DB.Model(&productModel).Updates(updates).Error; err != nil {
		global.GVA_LOG.Error("更新商品失败", zap.Error(err))
		return nil, fmt.Errorf("更新商品失败: %v", err)
	}

	return &productModel, nil
}

// GetProductList 获取商品列表
func (p *ProductService) GetProductList(req productReq.ProductSearch) (*productRes.ProductListResponse, error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	db := global.GVA_DB.Model(&product.Product{})

	// 添加查询条件
	if req.Name != nil && *req.Name != "" {
		db = db.Where("name LIKE ?", "%"+*req.Name+"%")
	}
	if req.Type != nil {
		db = db.Where("type = ?", *req.Type)
	}
	if req.Category != nil && *req.Category != "" {
		db = db.Where("category = ?", *req.Category)
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.MembershipType != nil {
		db = db.Where("membership_type = ?", *req.MembershipType)
	}
	if req.MinPrice != nil {
		db = db.Where("price >= ?", *req.MinPrice)
	}
	if req.MaxPrice != nil {
		db = db.Where("price <= ?", *req.MaxPrice)
	}
	if req.Tags != nil && *req.Tags != "" {
		db = db.Where("tags LIKE ?", "%"+*req.Tags+"%")
	}

	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取商品总数失败: %v", err)
	}

	var products []product.Product
	if err := db.Limit(limit).Offset(offset).Order("sort_order desc, id desc").Find(&products).Error; err != nil {
		return nil, fmt.Errorf("获取商品列表失败: %v", err)
	}

	// 转换为响应格式
	var productResponses []productRes.ProductResponse
	for _, prod := range products {
		productResponses = append(productResponses, p.convertToResponse(prod))
	}

	return &productRes.ProductListResponse{
		List:  productResponses,
		Total: total,
	}, nil
}

// GetProduct 获取单个商品
func (p *ProductService) GetProduct(id uint) (*productRes.ProductResponse, error) {
	var productModel product.Product
	if err := global.GVA_DB.First(&productModel, id).Error; err != nil {
		return nil, fmt.Errorf("商品不存在")
	}

	// 增加浏览量
	global.GVA_DB.Model(&productModel).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1))

	response := p.convertToResponse(productModel)
	return &response, nil
}

// DeleteProduct 删除商品
func (p *ProductService) DeleteProduct(id uint) error {
	var productModel product.Product
	if err := global.GVA_DB.First(&productModel, id).Error; err != nil {
		return fmt.Errorf("商品不存在")
	}

	// 软删除
	if err := global.GVA_DB.Model(&productModel).Update("status", product.ProductStatusDeleted).Error; err != nil {
		return fmt.Errorf("删除商品失败: %v", err)
	}

	return nil
}

// convertToResponse 转换为响应格式
func (p *ProductService) convertToResponse(prod product.Product) productRes.ProductResponse {
	response := productRes.ProductResponse{
		Product: prod,
	}

	// 解析JSON字段
	var images []string
	if prod.Images != "" {
		json.Unmarshal([]byte(prod.Images), &images)
	}
	response.ImagesArray = images

	var tags []string
	if prod.Tags != "" {
		json.Unmarshal([]byte(prod.Tags), &tags)
	}
	response.TagsArray = tags

	var features []string
	if prod.Features != "" {
		json.Unmarshal([]byte(prod.Features), &features)
	}
	response.FeaturesArray = features

	// 计算库存状态
	response.IsInStock = prod.Stock > 0 || prod.Stock == -1

	// 计算折扣率
	if prod.OriginalPrice > 0 && prod.Price < prod.OriginalPrice {
		response.DiscountRate = float64(prod.OriginalPrice-prod.Price) / float64(prod.OriginalPrice) * 100
	}

	return response
}

// calculateDuration 计算实际天数
func (p *ProductService) calculateDuration(duration int, unit product.DurationUnit) int {
	switch unit {
	case product.DurationUnitMonth:
		return duration * 30
	case product.DurationUnitQuarter:
		return duration * 90
	case product.DurationUnitYear:
		return duration * 365
	default:
		return duration
	}
}

// GetMembershipProducts 获取会员商品列表
func (p *ProductService) GetMembershipProducts() ([]productRes.ProductResponse, error) {
	var products []product.Product
	if err := global.GVA_DB.Where("type = ? AND status = ?",
		product.ProductTypeMembership, product.ProductStatusOnline).
		Order("sort_order desc, price asc").Find(&products).Error; err != nil {
		return nil, fmt.Errorf("获取会员商品失败: %v", err)
	}

	var responses []productRes.ProductResponse
	for _, prod := range products {
		responses = append(responses, p.convertToResponse(prod))
	}

	return responses, nil
}

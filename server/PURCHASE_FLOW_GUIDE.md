# 优化后的购买流程设计指南

## 概述

本文档说明了优化后的商品购买系统设计，明确了 `/payment/create` 和 `/product/buy` 的职责分工，以及完整的购买流程。

## 核心设计原则

### 1. 职责分离
- **`/payment/create`**: 纯支付服务，仅处理支付订单创建和第三方支付接口对接
- **`/product/buy`**: 完整业务逻辑，处理商品验证、库存管理、订单创建等

### 2. 安全策略
- `/payment/create` 标记为内部接口，不建议外部直接调用
- 外部调用会收到提示，建议使用业务接口
- 内部调用通过Header、IP或User-Agent进行验证

## API接口设计

### 1. 商品购买接口（推荐使用）

```http
POST /api/product/buy
Authorization: Bearer {token}
Content-Type: application/json

{
  "productId": 1,
  "quantity": 2,
  "paymentMethod": "wechat",
  "deviceType": "mobile",
  "openId": "wx_openid_for_jsapi", // 微信JSAPI支付时必需
  "addressId": 1, // 实物商品需要地址
  "expireMinutes": 30
}
```

**功能特性**：
- ✅ 商品验证（存在性、状态、库存）
- ✅ 价格计算（含运费）
- ✅ 地址处理（实物商品）
- ✅ 库存管理
- ✅ 事务保障
- ✅ 完整日志记录

### 2. 支付创建接口（内部接口）

```http
POST /api/payment/create
Authorization: Bearer {token}
X-Internal-Call: gva-internal-token
Content-Type: application/json

{
  "userId": 1,
  "amount": 10000,
  "subject": "商品购买",
  "body": "商品描述",
  "paymentMethod": "wechat",
  "deviceType": "mobile",
  "expireMinutes": 30
}
```

**功能特性**：
- ⚠️ 仅支付订单创建
- ⚠️ 不包含业务验证
- ⚠️ 内部接口，有访问限制

### 3. 订单取消接口

```http
POST /api/product/cancel/{orderNo}
Authorization: Bearer {token}
```

## 完整购买流程

### 第一步：前端发起购买

```javascript
// 前端购买请求
const buyProduct = async (productId, paymentMethod, deviceType) => {
  const response = await fetch('/api/product/buy', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      productId,
      paymentMethod,
      deviceType,
      quantity: 1,
      expireMinutes: 30
    })
  });
  
  const result = await response.json();
  if (result.code === 0) {
    // 使用返回的支付URL进行支付
    window.location.href = result.data.paymentUrl;
  }
};
```

### 第二步：后端业务处理

```go
// 商品购买服务流程
func (p *PurchaseService) BuyProduct(userID uint, req BuyProductRequest) {
    // 1. 验证商品（状态、库存）
    // 2. 计算总价（商品价格 + 运费）
    // 3. 处理收货地址（实物商品）
    // 4. 在事务中：
    //    a. 调用支付服务创建支付订单
    //    b. 创建业务订单项
    //    c. 扣减库存
    // 5. 返回支付URL
}
```

### 第三步：支付成功处理

```go
// 支付成功后的业务处理
func (p *PurchaseService) ProcessPaymentSuccess(orderNo string) {
    // 1. 激活会员（会员商品）
    // 2. 更新订单状态
    // 3. 增加销量统计
    // 4. 发送通知
}
```

## 错误处理策略

### 1. 业务验证错误
```json
{
  "code": -1,
  "msg": "商品已下架",
  "data": null
}
```

### 2. 支付创建错误
```json
{
  "code": -1,
  "msg": "创建支付订单失败: 微信JSAPI支付需要提供用户openid",
  "data": null
}
```

### 3. 库存不足错误
```json
{
  "code": -1,
  "msg": "库存不足",
  "data": null
}
```

## 事务管理

### 购买事务
- 支付订单创建
- 业务订单创建  
- 库存扣减
- 异常时自动回滚

### 支付成功事务
- 订单状态更新
- 会员激活
- 积分发放
- 防重复处理

## 扩展场景

### 1. 充值场景
可以直接使用 `/payment/create`：
```go
// 充值不涉及商品业务逻辑
paymentReq := CreatePaymentRequest{
    UserID: userID,
    Amount: rechargeAmount,
    Subject: "账户充值",
    PaymentMethod: "alipay",
}
```

### 2. 其他业务场景
建议创建专门的业务接口，内部调用支付服务：
- `/subscription/buy` - 订阅购买
- `/vip/upgrade` - VIP升级
- `/course/buy` - 课程购买

## 监控和日志

### 关键日志点
1. 商品购买开始
2. 支付订单创建成功/失败
3. 业务订单创建成功/失败
4. 库存更新成功/失败
5. 支付成功处理完成

### 性能监控
- 购买接口响应时间
- 支付成功率
- 订单处理成功率
- 库存准确性

## 最佳实践

### 1. API使用
- ✅ 使用 `/product/buy` 进行商品购买
- ❌ 避免直接调用 `/payment/create`
- ✅ 在业务接口中设置ClientIP
- ✅ 微信JSAPI支付时提供OpenID

### 2. 错误处理
- 前端展示友好的错误信息
- 后端记录详细的错误日志
- 支付失败时指导用户重试

### 3. 安全考虑
- 验证用户权限
- 防止重复提交
- 验证支付金额
- 记录操作日志

## 总结

通过这种设计，我们实现了：
1. **清晰的职责分离**：业务逻辑与支付逻辑分离
2. **更好的安全性**：内部接口访问控制
3. **完整的事务保障**：确保数据一致性
4. **良好的扩展性**：支持多种业务场景
5. **全面的错误处理**：提供友好的用户体验 
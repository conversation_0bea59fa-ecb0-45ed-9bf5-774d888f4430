syntax = "proto3";

package device;

import "common/common.proto";

option go_package = "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/device";

// 设备信息
message Device {
  uint32 id = 1;
  string device_id = 2;          // 设备唯一标识码
  string device_name = 3;        // 设备自定义名称
  string hardware_hash = 4;      // 硬件特征哈希值
  
  // 硬件信息
  string cpu_info = 5;           // CPU信息
  string memory_info = 6;        // 内存信息
  string disk_info = 7;          // 磁盘信息
  string network_info = 8;       // 网络信息
  string gpu_info = 9;           // 显卡信息
  
  // 系统环境信息
  string os_name = 10;           // 操作系统名称
  string os_version = 11;        // 操作系统版本
  string os_arch = 12;           // 系统架构
  string hostname = 13;          // 主机名
  string username = 14;          // 当前用户名
  string user_home_dir = 15;     // 用户主目录
  string work_dir = 16;          // 工作目录
  
  // 应用信息
  string app_version = 17;       // 应用版本
  string app_build_no = 18;      // 应用构建号
  
  // 网络信息
  string ip_address = 19;        // IP地址
  string mac_address = 20;       // MAC地址
  
  // 用户关联
  uint32 user_id = 21;           // 关联用户ID
  
  // 时间信息
  common.Timestamp first_seen_at = 22;   // 首次见到时间
  common.Timestamp last_seen_at = 23;    // 最后见到时间
  common.Timestamp last_report_at = 24;  // 最后上报时间
  common.Timestamp created_at = 25;      // 创建时间
  common.Timestamp updated_at = 26;      // 更新时间
  
  // 状态信息
  int32 status = 27;             // 状态 1:正常 2:禁用
  bool is_active = 28;           // 是否活跃
  int64 report_count = 29;       // 上报次数
  string remark = 30;            // 备注
  bool is_default = 31;          // 是否默认
  string mcp_access_address = 32; // MCP 访问地址
  string agent_id = 33;           // 智能体ID
}

// 设备在线状态信息
message DeviceOnlineStatus {
  bool is_online = 1;            // 是否在线
  common.Timestamp last_active_at = 2;  // 最后活跃时间
  string session_type = 3;       // 会话类型
  string ip_address = 4;         // 登录IP地址
  string user_agent = 5;         // 用户代理
  string device_name = 6;        // 设备名称
  string os_info = 7;            // 操作系统信息
  string app_version = 8;        // 应用版本
}

// 根据设备ID获取设备信息请求
message GetDeviceByIDRequest {
  string device_id = 1;
}

// 获取设备信息和在线状态响应
message GetDeviceInfoResponse {
  common.BaseResponse base = 1;
  Device device = 2;
  DeviceOnlineStatus online_status = 3;
}

// 获取我的设备列表请求
message GetMyDevicesRequest {
  uint32 user_id = 1;      // 用户ID
}

// 获取我的设备列表响应
message GetMyDevicesResponse {
  common.BaseResponse base = 1;
  repeated Device devices = 2;  // 设备列表
  int64 total = 3;             // 总数量
}

// 设备服务定义
service DeviceService {
  // 根据设备ID获取设备信息和在线状态（不需要认证）
  rpc GetDeviceInfo(GetDeviceByIDRequest) returns (GetDeviceInfoResponse);
  
  // 获取用户的设备列表
  rpc GetMyDevices(GetMyDevicesRequest) returns (GetMyDevicesResponse);
} 
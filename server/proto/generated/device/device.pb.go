// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: device/device.proto

package device

import (
	common "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 设备信息
type Device struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Id           uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeviceId     string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`             // 设备唯一标识码
	DeviceName   string                 `protobuf:"bytes,3,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`       // 设备自定义名称
	HardwareHash string                 `protobuf:"bytes,4,opt,name=hardware_hash,json=hardwareHash,proto3" json:"hardware_hash,omitempty"` // 硬件特征哈希值
	// 硬件信息
	CpuInfo     string `protobuf:"bytes,5,opt,name=cpu_info,json=cpuInfo,proto3" json:"cpu_info,omitempty"`             // CPU信息
	MemoryInfo  string `protobuf:"bytes,6,opt,name=memory_info,json=memoryInfo,proto3" json:"memory_info,omitempty"`    // 内存信息
	DiskInfo    string `protobuf:"bytes,7,opt,name=disk_info,json=diskInfo,proto3" json:"disk_info,omitempty"`          // 磁盘信息
	NetworkInfo string `protobuf:"bytes,8,opt,name=network_info,json=networkInfo,proto3" json:"network_info,omitempty"` // 网络信息
	GpuInfo     string `protobuf:"bytes,9,opt,name=gpu_info,json=gpuInfo,proto3" json:"gpu_info,omitempty"`             // 显卡信息
	// 系统环境信息
	OsName      string `protobuf:"bytes,10,opt,name=os_name,json=osName,proto3" json:"os_name,omitempty"`                  // 操作系统名称
	OsVersion   string `protobuf:"bytes,11,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`         // 操作系统版本
	OsArch      string `protobuf:"bytes,12,opt,name=os_arch,json=osArch,proto3" json:"os_arch,omitempty"`                  // 系统架构
	Hostname    string `protobuf:"bytes,13,opt,name=hostname,proto3" json:"hostname,omitempty"`                            // 主机名
	Username    string `protobuf:"bytes,14,opt,name=username,proto3" json:"username,omitempty"`                            // 当前用户名
	UserHomeDir string `protobuf:"bytes,15,opt,name=user_home_dir,json=userHomeDir,proto3" json:"user_home_dir,omitempty"` // 用户主目录
	WorkDir     string `protobuf:"bytes,16,opt,name=work_dir,json=workDir,proto3" json:"work_dir,omitempty"`               // 工作目录
	// 应用信息
	AppVersion string `protobuf:"bytes,17,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`   // 应用版本
	AppBuildNo string `protobuf:"bytes,18,opt,name=app_build_no,json=appBuildNo,proto3" json:"app_build_no,omitempty"` // 应用构建号
	// 网络信息
	IpAddress  string `protobuf:"bytes,19,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`    // IP地址
	MacAddress string `protobuf:"bytes,20,opt,name=mac_address,json=macAddress,proto3" json:"mac_address,omitempty"` // MAC地址
	// 用户关联
	UserId uint32 `protobuf:"varint,21,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 关联用户ID
	// 时间信息
	FirstSeenAt  *common.Timestamp `protobuf:"bytes,22,opt,name=first_seen_at,json=firstSeenAt,proto3" json:"first_seen_at,omitempty"`    // 首次见到时间
	LastSeenAt   *common.Timestamp `protobuf:"bytes,23,opt,name=last_seen_at,json=lastSeenAt,proto3" json:"last_seen_at,omitempty"`       // 最后见到时间
	LastReportAt *common.Timestamp `protobuf:"bytes,24,opt,name=last_report_at,json=lastReportAt,proto3" json:"last_report_at,omitempty"` // 最后上报时间
	CreatedAt    *common.Timestamp `protobuf:"bytes,25,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`            // 创建时间
	UpdatedAt    *common.Timestamp `protobuf:"bytes,26,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`            // 更新时间
	// 状态信息
	Status           int32  `protobuf:"varint,27,opt,name=status,proto3" json:"status,omitempty"`                                              // 状态 1:正常 2:禁用
	IsActive         bool   `protobuf:"varint,28,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`                          // 是否活跃
	ReportCount      int64  `protobuf:"varint,29,opt,name=report_count,json=reportCount,proto3" json:"report_count,omitempty"`                 // 上报次数
	Remark           string `protobuf:"bytes,30,opt,name=remark,proto3" json:"remark,omitempty"`                                               // 备注
	IsDefault        bool   `protobuf:"varint,31,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`                       // 是否默认
	McpAccessAddress string `protobuf:"bytes,32,opt,name=mcp_access_address,json=mcpAccessAddress,proto3" json:"mcp_access_address,omitempty"` // MCP 访问地址
	AgentId          string `protobuf:"bytes,33,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`                              // 智能体ID
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Device) Reset() {
	*x = Device{}
	mi := &file_device_device_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_device_device_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_device_device_proto_rawDescGZIP(), []int{0}
}

func (x *Device) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Device) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *Device) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *Device) GetHardwareHash() string {
	if x != nil {
		return x.HardwareHash
	}
	return ""
}

func (x *Device) GetCpuInfo() string {
	if x != nil {
		return x.CpuInfo
	}
	return ""
}

func (x *Device) GetMemoryInfo() string {
	if x != nil {
		return x.MemoryInfo
	}
	return ""
}

func (x *Device) GetDiskInfo() string {
	if x != nil {
		return x.DiskInfo
	}
	return ""
}

func (x *Device) GetNetworkInfo() string {
	if x != nil {
		return x.NetworkInfo
	}
	return ""
}

func (x *Device) GetGpuInfo() string {
	if x != nil {
		return x.GpuInfo
	}
	return ""
}

func (x *Device) GetOsName() string {
	if x != nil {
		return x.OsName
	}
	return ""
}

func (x *Device) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *Device) GetOsArch() string {
	if x != nil {
		return x.OsArch
	}
	return ""
}

func (x *Device) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *Device) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Device) GetUserHomeDir() string {
	if x != nil {
		return x.UserHomeDir
	}
	return ""
}

func (x *Device) GetWorkDir() string {
	if x != nil {
		return x.WorkDir
	}
	return ""
}

func (x *Device) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *Device) GetAppBuildNo() string {
	if x != nil {
		return x.AppBuildNo
	}
	return ""
}

func (x *Device) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *Device) GetMacAddress() string {
	if x != nil {
		return x.MacAddress
	}
	return ""
}

func (x *Device) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Device) GetFirstSeenAt() *common.Timestamp {
	if x != nil {
		return x.FirstSeenAt
	}
	return nil
}

func (x *Device) GetLastSeenAt() *common.Timestamp {
	if x != nil {
		return x.LastSeenAt
	}
	return nil
}

func (x *Device) GetLastReportAt() *common.Timestamp {
	if x != nil {
		return x.LastReportAt
	}
	return nil
}

func (x *Device) GetCreatedAt() *common.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Device) GetUpdatedAt() *common.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Device) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Device) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Device) GetReportCount() int64 {
	if x != nil {
		return x.ReportCount
	}
	return 0
}

func (x *Device) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *Device) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *Device) GetMcpAccessAddress() string {
	if x != nil {
		return x.McpAccessAddress
	}
	return ""
}

func (x *Device) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

// 设备在线状态信息
type DeviceOnlineStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsOnline      bool                   `protobuf:"varint,1,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`              // 是否在线
	LastActiveAt  *common.Timestamp      `protobuf:"bytes,2,opt,name=last_active_at,json=lastActiveAt,proto3" json:"last_active_at,omitempty"` // 最后活跃时间
	SessionType   string                 `protobuf:"bytes,3,opt,name=session_type,json=sessionType,proto3" json:"session_type,omitempty"`      // 会话类型
	IpAddress     string                 `protobuf:"bytes,4,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`            // 登录IP地址
	UserAgent     string                 `protobuf:"bytes,5,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`            // 用户代理
	DeviceName    string                 `protobuf:"bytes,6,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`         // 设备名称
	OsInfo        string                 `protobuf:"bytes,7,opt,name=os_info,json=osInfo,proto3" json:"os_info,omitempty"`                     // 操作系统信息
	AppVersion    string                 `protobuf:"bytes,8,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`         // 应用版本
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceOnlineStatus) Reset() {
	*x = DeviceOnlineStatus{}
	mi := &file_device_device_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceOnlineStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceOnlineStatus) ProtoMessage() {}

func (x *DeviceOnlineStatus) ProtoReflect() protoreflect.Message {
	mi := &file_device_device_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceOnlineStatus.ProtoReflect.Descriptor instead.
func (*DeviceOnlineStatus) Descriptor() ([]byte, []int) {
	return file_device_device_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceOnlineStatus) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *DeviceOnlineStatus) GetLastActiveAt() *common.Timestamp {
	if x != nil {
		return x.LastActiveAt
	}
	return nil
}

func (x *DeviceOnlineStatus) GetSessionType() string {
	if x != nil {
		return x.SessionType
	}
	return ""
}

func (x *DeviceOnlineStatus) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *DeviceOnlineStatus) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *DeviceOnlineStatus) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *DeviceOnlineStatus) GetOsInfo() string {
	if x != nil {
		return x.OsInfo
	}
	return ""
}

func (x *DeviceOnlineStatus) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

// 根据设备ID获取设备信息请求
type GetDeviceByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceByIDRequest) Reset() {
	*x = GetDeviceByIDRequest{}
	mi := &file_device_device_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceByIDRequest) ProtoMessage() {}

func (x *GetDeviceByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_device_device_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceByIDRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceByIDRequest) Descriptor() ([]byte, []int) {
	return file_device_device_proto_rawDescGZIP(), []int{2}
}

func (x *GetDeviceByIDRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// 获取设备信息和在线状态响应
type GetDeviceInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.BaseResponse   `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Device        *Device                `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`
	OnlineStatus  *DeviceOnlineStatus    `protobuf:"bytes,3,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceInfoResponse) Reset() {
	*x = GetDeviceInfoResponse{}
	mi := &file_device_device_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceInfoResponse) ProtoMessage() {}

func (x *GetDeviceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_device_device_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetDeviceInfoResponse) Descriptor() ([]byte, []int) {
	return file_device_device_proto_rawDescGZIP(), []int{3}
}

func (x *GetDeviceInfoResponse) GetBase() *common.BaseResponse {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetDeviceInfoResponse) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *GetDeviceInfoResponse) GetOnlineStatus() *DeviceOnlineStatus {
	if x != nil {
		return x.OnlineStatus
	}
	return nil
}

// 获取我的设备列表请求
type GetMyDevicesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint32                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyDevicesRequest) Reset() {
	*x = GetMyDevicesRequest{}
	mi := &file_device_device_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyDevicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyDevicesRequest) ProtoMessage() {}

func (x *GetMyDevicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_device_device_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyDevicesRequest.ProtoReflect.Descriptor instead.
func (*GetMyDevicesRequest) Descriptor() ([]byte, []int) {
	return file_device_device_proto_rawDescGZIP(), []int{4}
}

func (x *GetMyDevicesRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 获取我的设备列表响应
type GetMyDevicesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.BaseResponse   `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Devices       []*Device              `protobuf:"bytes,2,rep,name=devices,proto3" json:"devices,omitempty"` // 设备列表
	Total         int64                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`    // 总数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyDevicesResponse) Reset() {
	*x = GetMyDevicesResponse{}
	mi := &file_device_device_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyDevicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyDevicesResponse) ProtoMessage() {}

func (x *GetMyDevicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_device_device_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyDevicesResponse.ProtoReflect.Descriptor instead.
func (*GetMyDevicesResponse) Descriptor() ([]byte, []int) {
	return file_device_device_proto_rawDescGZIP(), []int{5}
}

func (x *GetMyDevicesResponse) GetBase() *common.BaseResponse {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetMyDevicesResponse) GetDevices() []*Device {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *GetMyDevicesResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_device_device_proto protoreflect.FileDescriptor

const file_device_device_proto_rawDesc = "" +
	"\n" +
	"\x13device/device.proto\x12\x06device\x1a\x13common/common.proto\"\xd7\b\n" +
	"\x06Device\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x1f\n" +
	"\vdevice_name\x18\x03 \x01(\tR\n" +
	"deviceName\x12#\n" +
	"\rhardware_hash\x18\x04 \x01(\tR\fhardwareHash\x12\x19\n" +
	"\bcpu_info\x18\x05 \x01(\tR\acpuInfo\x12\x1f\n" +
	"\vmemory_info\x18\x06 \x01(\tR\n" +
	"memoryInfo\x12\x1b\n" +
	"\tdisk_info\x18\a \x01(\tR\bdiskInfo\x12!\n" +
	"\fnetwork_info\x18\b \x01(\tR\vnetworkInfo\x12\x19\n" +
	"\bgpu_info\x18\t \x01(\tR\agpuInfo\x12\x17\n" +
	"\aos_name\x18\n" +
	" \x01(\tR\x06osName\x12\x1d\n" +
	"\n" +
	"os_version\x18\v \x01(\tR\tosVersion\x12\x17\n" +
	"\aos_arch\x18\f \x01(\tR\x06osArch\x12\x1a\n" +
	"\bhostname\x18\r \x01(\tR\bhostname\x12\x1a\n" +
	"\busername\x18\x0e \x01(\tR\busername\x12\"\n" +
	"\ruser_home_dir\x18\x0f \x01(\tR\vuserHomeDir\x12\x19\n" +
	"\bwork_dir\x18\x10 \x01(\tR\aworkDir\x12\x1f\n" +
	"\vapp_version\x18\x11 \x01(\tR\n" +
	"appVersion\x12 \n" +
	"\fapp_build_no\x18\x12 \x01(\tR\n" +
	"appBuildNo\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x13 \x01(\tR\tipAddress\x12\x1f\n" +
	"\vmac_address\x18\x14 \x01(\tR\n" +
	"macAddress\x12\x17\n" +
	"\auser_id\x18\x15 \x01(\rR\x06userId\x125\n" +
	"\rfirst_seen_at\x18\x16 \x01(\v2\x11.common.TimestampR\vfirstSeenAt\x123\n" +
	"\flast_seen_at\x18\x17 \x01(\v2\x11.common.TimestampR\n" +
	"lastSeenAt\x127\n" +
	"\x0elast_report_at\x18\x18 \x01(\v2\x11.common.TimestampR\flastReportAt\x120\n" +
	"\n" +
	"created_at\x18\x19 \x01(\v2\x11.common.TimestampR\tcreatedAt\x120\n" +
	"\n" +
	"updated_at\x18\x1a \x01(\v2\x11.common.TimestampR\tupdatedAt\x12\x16\n" +
	"\x06status\x18\x1b \x01(\x05R\x06status\x12\x1b\n" +
	"\tis_active\x18\x1c \x01(\bR\bisActive\x12!\n" +
	"\freport_count\x18\x1d \x01(\x03R\vreportCount\x12\x16\n" +
	"\x06remark\x18\x1e \x01(\tR\x06remark\x12\x1d\n" +
	"\n" +
	"is_default\x18\x1f \x01(\bR\tisDefault\x12,\n" +
	"\x12mcp_access_address\x18  \x01(\tR\x10mcpAccessAddress\x12\x19\n" +
	"\bagent_id\x18! \x01(\tR\aagentId\"\xa6\x02\n" +
	"\x12DeviceOnlineStatus\x12\x1b\n" +
	"\tis_online\x18\x01 \x01(\bR\bisOnline\x127\n" +
	"\x0elast_active_at\x18\x02 \x01(\v2\x11.common.TimestampR\flastActiveAt\x12!\n" +
	"\fsession_type\x18\x03 \x01(\tR\vsessionType\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x04 \x01(\tR\tipAddress\x12\x1d\n" +
	"\n" +
	"user_agent\x18\x05 \x01(\tR\tuserAgent\x12\x1f\n" +
	"\vdevice_name\x18\x06 \x01(\tR\n" +
	"deviceName\x12\x17\n" +
	"\aos_info\x18\a \x01(\tR\x06osInfo\x12\x1f\n" +
	"\vapp_version\x18\b \x01(\tR\n" +
	"appVersion\"3\n" +
	"\x14GetDeviceByIDRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\"\xaa\x01\n" +
	"\x15GetDeviceInfoResponse\x12(\n" +
	"\x04base\x18\x01 \x01(\v2\x14.common.BaseResponseR\x04base\x12&\n" +
	"\x06device\x18\x02 \x01(\v2\x0e.device.DeviceR\x06device\x12?\n" +
	"\ronline_status\x18\x03 \x01(\v2\x1a.device.DeviceOnlineStatusR\fonlineStatus\".\n" +
	"\x13GetMyDevicesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\rR\x06userId\"\x80\x01\n" +
	"\x14GetMyDevicesResponse\x12(\n" +
	"\x04base\x18\x01 \x01(\v2\x14.common.BaseResponseR\x04base\x12(\n" +
	"\adevices\x18\x02 \x03(\v2\x0e.device.DeviceR\adevices\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x03R\x05total2\xa8\x01\n" +
	"\rDeviceService\x12L\n" +
	"\rGetDeviceInfo\x12\x1c.device.GetDeviceByIDRequest\x1a\x1d.device.GetDeviceInfoResponse\x12I\n" +
	"\fGetMyDevices\x12\x1b.device.GetMyDevicesRequest\x1a\x1c.device.GetMyDevicesResponseBGZEgithub.com/flipped-aurora/gin-vue-admin/server/proto/generated/deviceb\x06proto3"

var (
	file_device_device_proto_rawDescOnce sync.Once
	file_device_device_proto_rawDescData []byte
)

func file_device_device_proto_rawDescGZIP() []byte {
	file_device_device_proto_rawDescOnce.Do(func() {
		file_device_device_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_device_device_proto_rawDesc), len(file_device_device_proto_rawDesc)))
	})
	return file_device_device_proto_rawDescData
}

var file_device_device_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_device_device_proto_goTypes = []any{
	(*Device)(nil),                // 0: device.Device
	(*DeviceOnlineStatus)(nil),    // 1: device.DeviceOnlineStatus
	(*GetDeviceByIDRequest)(nil),  // 2: device.GetDeviceByIDRequest
	(*GetDeviceInfoResponse)(nil), // 3: device.GetDeviceInfoResponse
	(*GetMyDevicesRequest)(nil),   // 4: device.GetMyDevicesRequest
	(*GetMyDevicesResponse)(nil),  // 5: device.GetMyDevicesResponse
	(*common.Timestamp)(nil),      // 6: common.Timestamp
	(*common.BaseResponse)(nil),   // 7: common.BaseResponse
}
var file_device_device_proto_depIdxs = []int32{
	6,  // 0: device.Device.first_seen_at:type_name -> common.Timestamp
	6,  // 1: device.Device.last_seen_at:type_name -> common.Timestamp
	6,  // 2: device.Device.last_report_at:type_name -> common.Timestamp
	6,  // 3: device.Device.created_at:type_name -> common.Timestamp
	6,  // 4: device.Device.updated_at:type_name -> common.Timestamp
	6,  // 5: device.DeviceOnlineStatus.last_active_at:type_name -> common.Timestamp
	7,  // 6: device.GetDeviceInfoResponse.base:type_name -> common.BaseResponse
	0,  // 7: device.GetDeviceInfoResponse.device:type_name -> device.Device
	1,  // 8: device.GetDeviceInfoResponse.online_status:type_name -> device.DeviceOnlineStatus
	7,  // 9: device.GetMyDevicesResponse.base:type_name -> common.BaseResponse
	0,  // 10: device.GetMyDevicesResponse.devices:type_name -> device.Device
	2,  // 11: device.DeviceService.GetDeviceInfo:input_type -> device.GetDeviceByIDRequest
	4,  // 12: device.DeviceService.GetMyDevices:input_type -> device.GetMyDevicesRequest
	3,  // 13: device.DeviceService.GetDeviceInfo:output_type -> device.GetDeviceInfoResponse
	5,  // 14: device.DeviceService.GetMyDevices:output_type -> device.GetMyDevicesResponse
	13, // [13:15] is the sub-list for method output_type
	11, // [11:13] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_device_device_proto_init() }
func file_device_device_proto_init() {
	if File_device_device_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_device_device_proto_rawDesc), len(file_device_device_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_device_device_proto_goTypes,
		DependencyIndexes: file_device_device_proto_depIdxs,
		MessageInfos:      file_device_device_proto_msgTypes,
	}.Build()
	File_device_device_proto = out.File
	file_device_device_proto_goTypes = nil
	file_device_device_proto_depIdxs = nil
}

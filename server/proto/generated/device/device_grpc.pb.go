// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.3
// source: device/device.proto

package device

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DeviceService_GetDeviceInfo_FullMethodName = "/device.DeviceService/GetDeviceInfo"
	DeviceService_GetMyDevices_FullMethodName  = "/device.DeviceService/GetMyDevices"
)

// DeviceServiceClient is the client API for DeviceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 设备服务定义
type DeviceServiceClient interface {
	// 根据设备ID获取设备信息和在线状态（不需要认证）
	GetDeviceInfo(ctx context.Context, in *GetDeviceByIDRequest, opts ...grpc.CallOption) (*GetDeviceInfoResponse, error)
	// 获取用户的设备列表
	GetMyDevices(ctx context.Context, in *GetMyDevicesRequest, opts ...grpc.CallOption) (*GetMyDevicesResponse, error)
}

type deviceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeviceServiceClient(cc grpc.ClientConnInterface) DeviceServiceClient {
	return &deviceServiceClient{cc}
}

func (c *deviceServiceClient) GetDeviceInfo(ctx context.Context, in *GetDeviceByIDRequest, opts ...grpc.CallOption) (*GetDeviceInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDeviceInfoResponse)
	err := c.cc.Invoke(ctx, DeviceService_GetDeviceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceServiceClient) GetMyDevices(ctx context.Context, in *GetMyDevicesRequest, opts ...grpc.CallOption) (*GetMyDevicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMyDevicesResponse)
	err := c.cc.Invoke(ctx, DeviceService_GetMyDevices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeviceServiceServer is the server API for DeviceService service.
// All implementations must embed UnimplementedDeviceServiceServer
// for forward compatibility.
//
// 设备服务定义
type DeviceServiceServer interface {
	// 根据设备ID获取设备信息和在线状态（不需要认证）
	GetDeviceInfo(context.Context, *GetDeviceByIDRequest) (*GetDeviceInfoResponse, error)
	// 获取用户的设备列表
	GetMyDevices(context.Context, *GetMyDevicesRequest) (*GetMyDevicesResponse, error)
	mustEmbedUnimplementedDeviceServiceServer()
}

// UnimplementedDeviceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDeviceServiceServer struct{}

func (UnimplementedDeviceServiceServer) GetDeviceInfo(context.Context, *GetDeviceByIDRequest) (*GetDeviceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceInfo not implemented")
}
func (UnimplementedDeviceServiceServer) GetMyDevices(context.Context, *GetMyDevicesRequest) (*GetMyDevicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMyDevices not implemented")
}
func (UnimplementedDeviceServiceServer) mustEmbedUnimplementedDeviceServiceServer() {}
func (UnimplementedDeviceServiceServer) testEmbeddedByValue()                       {}

// UnsafeDeviceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeviceServiceServer will
// result in compilation errors.
type UnsafeDeviceServiceServer interface {
	mustEmbedUnimplementedDeviceServiceServer()
}

func RegisterDeviceServiceServer(s grpc.ServiceRegistrar, srv DeviceServiceServer) {
	// If the following call pancis, it indicates UnimplementedDeviceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DeviceService_ServiceDesc, srv)
}

func _DeviceService_GetDeviceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).GetDeviceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_GetDeviceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).GetDeviceInfo(ctx, req.(*GetDeviceByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceService_GetMyDevices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyDevicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).GetMyDevices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_GetMyDevices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).GetMyDevices(ctx, req.(*GetMyDevicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DeviceService_ServiceDesc is the grpc.ServiceDesc for DeviceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeviceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "device.DeviceService",
	HandlerType: (*DeviceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDeviceInfo",
			Handler:    _DeviceService_GetDeviceInfo_Handler,
		},
		{
			MethodName: "GetMyDevices",
			Handler:    _DeviceService_GetMyDevices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "device/device.proto",
}

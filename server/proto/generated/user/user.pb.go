// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: user/user.proto

package user

import (
	common "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户信息
type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	NickName      string                 `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                 `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone,omitempty"`
	Avatar        string                 `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`
	AuthorityId   uint32                 `protobuf:"varint,7,opt,name=authority_id,json=authorityId,proto3" json:"authority_id,omitempty"`
	CreatedAt     *common.Timestamp      `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *common.Timestamp      `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_user_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_user_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_user_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *User) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *User) GetAuthorityId() uint32 {
	if x != nil {
		return x.AuthorityId
	}
	return 0
}

func (x *User) GetCreatedAt() *common.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetUpdatedAt() *common.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 获取用户请求
type GetUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint32                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_user_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_user_user_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 获取用户响应
type GetUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.BaseResponse   `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	User          *User                  `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	mi := &file_user_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_user_user_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserResponse) GetBase() *common.BaseResponse {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetUserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

// 获取用户积分请求
type GetUserPointsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint32                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPointsRequest) Reset() {
	*x = GetUserPointsRequest{}
	mi := &file_user_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPointsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPointsRequest) ProtoMessage() {}

func (x *GetUserPointsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPointsRequest.ProtoReflect.Descriptor instead.
func (*GetUserPointsRequest) Descriptor() ([]byte, []int) {
	return file_user_user_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserPointsRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 获取用户积分响应
type GetUserPointsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.BaseResponse   `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Points        int32                  `protobuf:"varint,2,opt,name=points,proto3" json:"points,omitempty"`                              // 付费积分
	FreePoints    int32                  `protobuf:"varint,3,opt,name=free_points,json=freePoints,proto3" json:"free_points,omitempty"`    // 免费积分
	TotalPoints   int32                  `protobuf:"varint,4,opt,name=total_points,json=totalPoints,proto3" json:"total_points,omitempty"` // 总积分
	VipLevel      int32                  `protobuf:"varint,5,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`          // VIP等级
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPointsResponse) Reset() {
	*x = GetUserPointsResponse{}
	mi := &file_user_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPointsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPointsResponse) ProtoMessage() {}

func (x *GetUserPointsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPointsResponse.ProtoReflect.Descriptor instead.
func (*GetUserPointsResponse) Descriptor() ([]byte, []int) {
	return file_user_user_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserPointsResponse) GetBase() *common.BaseResponse {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetUserPointsResponse) GetPoints() int32 {
	if x != nil {
		return x.Points
	}
	return 0
}

func (x *GetUserPointsResponse) GetFreePoints() int32 {
	if x != nil {
		return x.FreePoints
	}
	return 0
}

func (x *GetUserPointsResponse) GetTotalPoints() int32 {
	if x != nil {
		return x.TotalPoints
	}
	return 0
}

func (x *GetUserPointsResponse) GetVipLevel() int32 {
	if x != nil {
		return x.VipLevel
	}
	return 0
}

var File_user_user_proto protoreflect.FileDescriptor

const file_user_user_proto_rawDesc = "" +
	"\n" +
	"\x0fuser/user.proto\x12\x04user\x1a\x13common/common.proto\"\x9a\x02\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1b\n" +
	"\tnick_name\x18\x03 \x01(\tR\bnickName\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x14\n" +
	"\x05phone\x18\x05 \x01(\tR\x05phone\x12\x16\n" +
	"\x06avatar\x18\x06 \x01(\tR\x06avatar\x12!\n" +
	"\fauthority_id\x18\a \x01(\rR\vauthorityId\x120\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x11.common.TimestampR\tcreatedAt\x120\n" +
	"\n" +
	"updated_at\x18\t \x01(\v2\x11.common.TimestampR\tupdatedAt\")\n" +
	"\x0eGetUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\rR\x06userId\"[\n" +
	"\x0fGetUserResponse\x12(\n" +
	"\x04base\x18\x01 \x01(\v2\x14.common.BaseResponseR\x04base\x12\x1e\n" +
	"\x04user\x18\x02 \x01(\v2\n" +
	".user.UserR\x04user\"/\n" +
	"\x14GetUserPointsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\rR\x06userId\"\xba\x01\n" +
	"\x15GetUserPointsResponse\x12(\n" +
	"\x04base\x18\x01 \x01(\v2\x14.common.BaseResponseR\x04base\x12\x16\n" +
	"\x06points\x18\x02 \x01(\x05R\x06points\x12\x1f\n" +
	"\vfree_points\x18\x03 \x01(\x05R\n" +
	"freePoints\x12!\n" +
	"\ftotal_points\x18\x04 \x01(\x05R\vtotalPoints\x12\x1b\n" +
	"\tvip_level\x18\x05 \x01(\x05R\bvipLevel2\x8f\x01\n" +
	"\vUserService\x126\n" +
	"\aGetUser\x12\x14.user.GetUserRequest\x1a\x15.user.GetUserResponse\x12H\n" +
	"\rGetUserPoints\x12\x1a.user.GetUserPointsRequest\x1a\x1b.user.GetUserPointsResponseBEZCgithub.com/flipped-aurora/gin-vue-admin/server/proto/generated/userb\x06proto3"

var (
	file_user_user_proto_rawDescOnce sync.Once
	file_user_user_proto_rawDescData []byte
)

func file_user_user_proto_rawDescGZIP() []byte {
	file_user_user_proto_rawDescOnce.Do(func() {
		file_user_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_user_proto_rawDesc), len(file_user_user_proto_rawDesc)))
	})
	return file_user_user_proto_rawDescData
}

var file_user_user_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_user_user_proto_goTypes = []any{
	(*User)(nil),                  // 0: user.User
	(*GetUserRequest)(nil),        // 1: user.GetUserRequest
	(*GetUserResponse)(nil),       // 2: user.GetUserResponse
	(*GetUserPointsRequest)(nil),  // 3: user.GetUserPointsRequest
	(*GetUserPointsResponse)(nil), // 4: user.GetUserPointsResponse
	(*common.Timestamp)(nil),      // 5: common.Timestamp
	(*common.BaseResponse)(nil),   // 6: common.BaseResponse
}
var file_user_user_proto_depIdxs = []int32{
	5, // 0: user.User.created_at:type_name -> common.Timestamp
	5, // 1: user.User.updated_at:type_name -> common.Timestamp
	6, // 2: user.GetUserResponse.base:type_name -> common.BaseResponse
	0, // 3: user.GetUserResponse.user:type_name -> user.User
	6, // 4: user.GetUserPointsResponse.base:type_name -> common.BaseResponse
	1, // 5: user.UserService.GetUser:input_type -> user.GetUserRequest
	3, // 6: user.UserService.GetUserPoints:input_type -> user.GetUserPointsRequest
	2, // 7: user.UserService.GetUser:output_type -> user.GetUserResponse
	4, // 8: user.UserService.GetUserPoints:output_type -> user.GetUserPointsResponse
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_user_user_proto_init() }
func file_user_user_proto_init() {
	if File_user_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_user_proto_rawDesc), len(file_user_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_user_proto_goTypes,
		DependencyIndexes: file_user_user_proto_depIdxs,
		MessageInfos:      file_user_user_proto_msgTypes,
	}.Build()
	File_user_user_proto = out.File
	file_user_user_proto_goTypes = nil
	file_user_user_proto_depIdxs = nil
}

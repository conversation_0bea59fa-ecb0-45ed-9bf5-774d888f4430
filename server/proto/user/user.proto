syntax = "proto3";

package user;

import "common/common.proto";

option go_package = "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/user";

// 用户信息
message User {
  uint32 id = 1;
  string username = 2;
  string nick_name = 3;
  string email = 4;
  string phone = 5;
  string avatar = 6;
  uint32 authority_id = 7;
  common.Timestamp created_at = 8;
  common.Timestamp updated_at = 9;
}

// 获取用户请求
message GetUserRequest {
  uint32 user_id = 1;
}

// 获取用户响应
message GetUserResponse {
  common.BaseResponse base = 1;
  User user = 2;
}

// 获取用户积分请求
message GetUserPointsRequest {
  uint32 user_id = 1;
}

// 获取用户积分响应
message GetUserPointsResponse {
  common.BaseResponse base = 1;
  int32 points = 2;       // 付费积分
  int32 free_points = 3;  // 免费积分
  int32 total_points = 4; // 总积分
  int32 vip_level = 5;    // VIP等级
}

// 用户服务定义
service UserService {
  // 获取用户信息
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  
  // 获取用户积分
  rpc GetUserPoints(GetUserPointsRequest) returns (GetUserPointsResponse);
} 
#!/bin/bash

# 确保生成目录存在
mkdir -p generated/common generated/user generated/device

# 生成通用proto文件
protoc --go_out=generated --go_opt=paths=source_relative \
       --go-grpc_out=generated --go-grpc_opt=paths=source_relative \
       common/common.proto

# 生成用户服务proto文件
protoc --go_out=generated --go_opt=paths=source_relative \
       --go-grpc_out=generated --go-grpc_opt=paths=source_relative \
       --proto_path=. \
       user/user.proto

# 生成设备服务proto文件
protoc --go_out=generated --go_opt=paths=source_relative \
       --go-grpc_out=generated --go-grpc_opt=paths=source_relative \
       --proto_path=. \
       device/device.proto

echo "Proto files generated successfully!" 
package task

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
)

// RefreshDailyFreePoints 每日刷新免费积分定时任务
func RefreshDailyFreePoints() error {
	global.GVA_LOG.Info("开始执行每日免费积分刷新任务")

	err := system.UserServiceApp.RefreshDailyFreePoints()
	if err != nil {
		global.GVA_LOG.Error("每日免费积分刷新任务执行失败: " + err.Error())
		return fmt.Errorf("每日免费积分刷新失败: %v", err)
	}

	global.GVA_LOG.Info("每日免费积分刷新任务执行完成")
	return nil
}

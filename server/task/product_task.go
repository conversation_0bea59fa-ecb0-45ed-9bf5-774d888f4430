package task

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service/product"
)

// GrantMonthlyPoints 发放会员积分定时任务（每30天周期）
func GrantMonthlyPoints() error {
	global.GVA_LOG.Info("开始执行会员积分发放检查任务")

	err := product.MembershipServiceApp.GrantMonthlyPoints()
	if err != nil {
		global.GVA_LOG.Error("会员积分发放检查任务执行失败: " + err.Error())
		return fmt.Errorf("会员积分发放失败: %v", err)
	}

	global.GVA_LOG.Info("会员积分发放检查任务执行完成")
	return nil
}

// UpdateExpiredMemberships 更新过期会员状态定时任务
func UpdateExpiredMemberships() error {
	global.GVA_LOG.Info("开始执行过期会员状态更新任务")

	err := product.MembershipServiceApp.UpdateExpiredMemberships()
	if err != nil {
		global.GVA_LOG.Error("过期会员状态更新任务执行失败: " + err.Error())
		return fmt.Errorf("过期会员状态更新失败: %v", err)
	}

	global.GVA_LOG.Info("过期会员状态更新任务执行完成")
	return nil
}

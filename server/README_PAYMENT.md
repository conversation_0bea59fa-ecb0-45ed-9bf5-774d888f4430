# 支付系统集成方案

## 项目概述

本项目为 gin-vue-admin 集成了完整的支付系统，支持微信支付和支付宝支付。该系统采用清晰的分层架构，具有良好的扩展性和可维护性。

## 目录结构

```
server/
├── model/payment/                 # 数据模型
│   ├── payment_order.go          # 支付订单模型
│   ├── payment_config.go         # 支付配置模型
│   └── payment_refund.go         # 退款记录模型
├── utils/payment/                 # 支付工具类
│   ├── interface.go              # 支付接口定义
│   ├── wechat.go                 # 微信支付实现
│   └── alipay.go                 # 支付宝支付实现
├── service/payment/               # 业务逻辑层
│   ├── enter.go                  # 服务组定义
│   └── payment_service.go        # 支付服务实现
├── api/v1/payment/               # API控制器
│   ├── enter.go                  # API组定义
│   └── payment_api.go            # 支付API实现
├── router/payment/               # 路由配置
│   ├── enter.go                  # 路由组定义
│   └── payment_router.go         # 支付路由定义
└── docs/                         # 文档
    ├── payment_guide.md          # 使用指南
    └── payment_init.sql          # 初始化SQL
```

## 已实现功能

### ✅ 核心功能
- [x] 支付订单创建
- [x] 支付状态查询  
- [x] 异步通知处理
- [x] 订单退款
- [x] 支付配置管理
- [x] 订单列表查询

### ✅ 技术特性
- [x] 统一的支付接口设计
- [x] 支持微信支付和支付宝支付
- [x] 完整的数据库模型设计
- [x] RESTful API接口
- [x] 分层架构设计
- [x] 金额精度处理（分为单位）
- [x] 订单状态管理
- [x] 异步通知验证
- [x] 退款流程管理

### ✅ 数据模型
- [x] PaymentOrder：支付订单表
- [x] PaymentConfig：支付配置表  
- [x] PaymentRefund：退款记录表

### ✅ API接口
- [x] POST /payment/create - 创建支付订单
- [x] GET /payment/query/{orderNo} - 查询支付状态
- [x] POST /payment/refund - 申请退款
- [x] GET /payment/config - 获取支付配置
- [x] GET /payment/list - 获取支付订单列表
- [x] POST /payment/notify/wechat - 微信支付通知
- [x] POST /payment/notify/alipay - 支付宝支付通知

## 架构设计

### 分层架构
```
┌─────────────────┐
│   API Layer     │  ← 控制器层，处理HTTP请求
├─────────────────┤
│ Service Layer   │  ← 业务逻辑层，处理支付业务
├─────────────────┤
│  Utils Layer    │  ← 工具层，封装第三方支付SDK
├─────────────────┤
│  Model Layer    │  ← 数据模型层，定义数据结构
└─────────────────┘
```

### 支付流程
```
1. 创建订单 → 2. 调用第三方API → 3. 返回支付URL
                    ↓
6. 更新订单状态 ← 5. 验证签名 ← 4. 接收异步通知
```

### 退款流程
```
1. 验证订单 → 2. 创建退款记录 → 3. 调用退款API → 4. 更新状态
```

## 支付状态管理

### 订单状态
| 状态 | 说明 | 可转换状态 |
|------|------|-----------|
| 1-待支付 | 订单已创建 | 2,3,4 |
| 2-已支付 | 支付成功 | 5,6 |
| 3-支付失败 | 支付失败 | 1,4 |
| 4-已取消 | 订单取消 | - |
| 5-已退款 | 全额退款 | - |
| 6-部分退款 | 部分退款 | 5 |

### 退款状态
| 状态 | 说明 |
|------|------|
| 1-退款中 | 退款申请已提交 |
| 2-退款成功 | 退款完成 |
| 3-退款失败 | 退款失败 |
| 4-已取消 | 退款已取消 |

## 安全设计

### 1. 金额安全
- 使用分为单位存储，避免浮点数精度问题
- 严格的金额校验和转换

### 2. 签名验证
- 异步通知必须验证签名
- 防止伪造通知攻击

### 3. 幂等性处理
- 订单号唯一性保证
- 重复通知处理机制

### 4. 配置安全
- 敏感配置加密存储
- API响应脱敏处理

## 数据库设计

### 核心表关系
```
payment_orders (订单表)
    ├── payment_refunds (退款表) [1:N]
    └── payment_configs (配置表) [N:1]
```

### 索引设计
- order_no: 唯一索引
- user_id: 普通索引  
- status: 普通索引
- created_at: 时间索引

## 扩展性设计

### 1. 支付方式扩展
通过实现 `PaymentInterface` 接口可轻松添加新的支付方式：

```go
type PaymentInterface interface {
    CreatePayment(order *payment.PaymentOrder) (string, error)
    QueryPayment(orderNo string) (*PaymentQueryResult, error)
    RefundPayment(refund *payment.PaymentRefund) (*RefundResult, error)
    VerifyNotify(notifyData []byte) (*NotifyResult, error)
}
```

### 2. 业务逻辑扩展
支持在关键节点添加钩子函数：
- 支付成功后处理
- 退款成功后处理
- 订单状态变更监听

### 3. 配置管理扩展
- 动态配置加载
- 多环境配置支持
- 配置版本管理

## 部署说明

### 1. 数据库初始化
```bash
# 执行初始化SQL
mysql -u root -p your_database < docs/payment_init.sql
```

### 2. 配置更新
在 `payment_configs` 表中更新实际的支付配置信息。

### 3. 证书文件
将微信支付证书文件放置到服务器，并更新配置中的路径。

### 4. 域名配置
确保异步通知URL可以被支付平台访问。

## 监控和日志

### 关键指标
- 订单创建成功率
- 支付成功率
- 异步通知处理成功率
- 退款成功率
- 平均支付时长

### 日志记录
- 订单状态变更日志
- 第三方API调用日志
- 异步通知处理日志
- 错误和异常日志

## 测试建议

### 1. 单元测试
- 支付工具类测试
- 业务逻辑测试
- 数据模型测试

### 2. 集成测试
- 支付流程端到端测试
- 异步通知处理测试
- 退款流程测试

### 3. 压力测试
- 高并发订单创建
- 大量异步通知处理
- 数据库性能测试

## 运维建议

### 1. 监控告警
- 支付失败率异常告警
- 数据库连接异常告警
- 第三方API调用异常告警

### 2. 数据备份
- 定期备份支付相关数据
- 重要配置信息备份

### 3. 容灾处理
- 第三方服务不可用的降级方案
- 数据库故障的恢复方案

## 下一步开发计划

### 近期计划
- [ ] 完善微信支付SDK集成
- [ ] 完善支付宝SDK集成  
- [ ] 添加更多支付方式（银联等）
- [ ] 增加支付数据统计功能

### 中期计划
- [ ] 支付风控系统
- [ ] 多商户支持
- [ ] 分账功能
- [ ] 营销工具集成

### 长期计划
- [ ] 国际化支付支持
- [ ] 区块链支付探索
- [ ] AI风控系统

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 技术支持

如有问题，请创建 Issue 或联系开发团队。

---

**注意**: 本系统微信H5支付和支付宝支付已完整实现，微信Native扫码支付使用官方SDK。生产环境使用前请进行充分测试并配置正确的支付参数。

## 防重复处理机制

支付回调会重复发送，系统实现了多层防重复处理：

### 1. 支付订单级别
- 检查支付订单状态，已支付则跳过处理

### 2. 业务订单级别  
- 检查订单项处理状态，已处理则跳过

### 3. 乐观锁机制
- 使用条件更新防止并发冲突

### 4. 业务逻辑级别
- 会员激活：检查激活记录防重复
- 积分发放：检查发放记录防重复

详细说明请参考：[防重复处理机制文档](./docs/payment_idempotent.md)

## 设备类型支持

支付系统支持根据不同设备类型选择最佳支付方式：

### 支持的设备类型
- **PC端** (`pc`)：电脑网站支付
- **手机端** (`mobile`)：手机网站支付
- **应用端** (`app`)：移动应用支付

### 自动适配机制
- **支付宝PC**：使用 `TradePagePay` (电脑网站支付)
- **支付宝手机**：使用 `TradeWapPay` (手机网站支付)
- **微信PC**：使用 `Native API` (扫码支付)
- **微信手机**：使用 `H5 API` (手机网站支付)

详细说明请参考：[设备类型支持文档](./docs/payment_device_support.md)

## 快速开始 

## 核心特性

- **多支付方式支持**：微信支付、支付宝支付
- **设备类型适配**：自动根据PC/手机/微信内部选择最佳支付方式
- [x] 完整的订单流程：创建、支付、查询、退款
- [x] 异步通知处理：支持支付成功/失败回调
- [x] 防重复处理：多层次防重复机制，确保回调安全
- [x] 会员系统集成：支持会员商品购买和自动激活
- [x] 积分系统：购买会员自动发放积分
- [x] 配置化管理：支付参数通过配置文件管理
- [x] 全场景支付支持：
  - 微信JSAPI支付（微信内部）
  - 微信H5支付（手机浏览器）
  - 微信Native支付（PC扫码）
  - 支付宝网站支付（PC和手机） 
package config

type Clipboard struct {
	MaxFileSize        int64  `mapstructure:"max-file-size" json:"max-file-size" yaml:"max-file-size"`
	AllowedFileTypes   string `mapstructure:"allowed-file-types" json:"allowed-file-types" yaml:"allowed-file-types"`
	AutoCleanDays      int    `mapstructure:"auto-clean-days" json:"auto-clean-days" yaml:"auto-clean-days"`
	EnableNotification bool   `mapstructure:"enable-notification" json:"enable-notification" yaml:"enable-notification"`
}

package config

// Payment 支付配置
type Payment struct {
	Wechat WechatPayConfig `mapstructure:"wechat" json:"wechat" yaml:"wechat"`
	<PERSON><PERSON>y AlipayConfig    `mapstructure:"alipay" json:"alipay" yaml:"alipay"`
}

// WechatPayConfig 微信支付配置
// 根据微信支付官方Go SDK文档 https://pay.weixin.qq.com/doc/v3/merchant/4012076515
type WechatPayConfig struct {
	Enabled                    bool   `mapstructure:"enabled" json:"enabled" yaml:"enabled"`                                                                   // 是否启用微信支付
	MpAppID                    string `mapstructure:"mp-app-id" json:"mp-app-id" yaml:"mp-app-id"`                                                             // 微信服务号应用ID
	OpenAppID                  string `mapstructure:"open-app-id" json:"open-app-id" yaml:"open-app-id"`                                                       // 微信网站应用ID
	MchID                      string `mapstructure:"mch-id" json:"mch-id" yaml:"mch-id"`                                                                      // 微信支付商户号
	MchCertificateSerialNumber string `mapstructure:"mch-certificate-serial-number" json:"mch-certificate-serial-number" yaml:"mch-certificate-serial-number"` // 商户API证书序列号
	APIv3Key                   string `mapstructure:"apiv3-key" json:"apiv3-key" yaml:"apiv3-key"`                                                             // 微信支付APIv3密钥（32位字符串）
	PrivateKey                 string `mapstructure:"private-key" json:"private-key" yaml:"private-key"`                                                       // 商户API私钥内容（PEM格式字符串，可替代key-path）
	NotifyUrl                  string `mapstructure:"notify-url" json:"notify-url" yaml:"notify-url"`                                                          // 支付结果异步通知地址
	// 环境配置
	IsSandbox bool `mapstructure:"is-sandbox" json:"is-sandbox" yaml:"is-sandbox"` // 是否沙箱环境（建议用于测试）
	// H5支付专用配置
	H5Domain    string `mapstructure:"h5-domain" json:"h5-domain" yaml:"h5-domain"`             // H5支付授权域名（需在微信商户平台配置）
	H5ReturnUrl string `mapstructure:"h5-return-url" json:"h5-return-url" yaml:"h5-return-url"` // H5支付完成后返回地址
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	Enabled      bool   `mapstructure:"enabled" json:"enabled" yaml:"enabled"`                   // 是否启用
	AppID        string `mapstructure:"app-id" json:"app-id" yaml:"app-id"`                      // 应用ID
	PrivateKey   string `mapstructure:"private-key" json:"private-key" yaml:"private-key"`       // 应用私钥
	PublicKey    string `mapstructure:"public-key" json:"public-key" yaml:"public-key"`          // 支付宝公钥
	IsProduction bool   `mapstructure:"is-production" json:"is-production" yaml:"is-production"` // 是否生产环境
	NotifyUrl    string `mapstructure:"notify-url" json:"notify-url" yaml:"notify-url"`          // 异步通知地址
	ReturnUrl    string `mapstructure:"return-url" json:"return-url" yaml:"return-url"`          // 同步返回地址
	SignType     string `mapstructure:"sign-type" json:"sign-type" yaml:"sign-type"`             // 签名类型，默认RSA2
}

package config

type TencentSMS struct {
	SecretID           string `mapstructure:"secret-id" json:"secret-id" yaml:"secret-id"`
	SecretKey          string `mapstructure:"secret-key" json:"secret-key" yaml:"secret-key"`
	AppID              string `mapstructure:"app-id" json:"app-id" yaml:"app-id"`
	SignName           string `mapstructure:"sign-name" json:"sign-name" yaml:"sign-name"`
	TemplateID         string `mapstructure:"template-id" json:"template-id" yaml:"template-id"`                            // 通用模板ID（向后兼容）
	RegisterTemplateID string `mapstructure:"register-template-id" json:"register-template-id" yaml:"register-template-id"` // 注册验证码模板ID
	LoginTemplateID    string `mapstructure:"login-template-id" json:"login-template-id" yaml:"login-template-id"`          // 登录验证码模板ID
}

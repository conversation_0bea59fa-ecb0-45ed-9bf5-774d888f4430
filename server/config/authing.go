package config

type Authing struct {
	AppId          string `mapstructure:"app-id" json:"app-id" yaml:"app-id"`                               // Authing 应用 ID
	Secret         string `mapstructure:"secret" json:"secret" yaml:"secret"`                               // Authing 应用密钥
	UserPoolId     string `mapstructure:"user-pool-id" json:"user-pool-id" yaml:"user-pool-id"`             // Authing 用户池 ID
	UserPoolSecret string `mapstructure:"user-pool-secret" json:"user-pool-secret" yaml:"user-pool-secret"` // Authing 用户池秘钥
	Host           string `mapstructure:"host" json:"host" yaml:"host"`                                     // Authing 服务地址（可选，用于私有化部署）
	RedirectUri    string `mapstructure:"redirect-uri" json:"redirect-uri" yaml:"redirect-uri"`             // Authing 重定向 URI
	TokenEndpoint  string `mapstructure:"token-endpoint" json:"token-endpoint" yaml:"token-endpoint"`       // Authing token端点
}

package pansou

import (
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"time"
)

// Config pansou应用配置结构
type Config struct {
	DefaultChannels    []string
	DefaultConcurrency int
	Port               string
	ProxyURL           string
	UseProxy           bool
	// 缓存相关配置
	CacheEnabled    bool
	CachePath       string
	CacheMaxSizeMB  int
	CacheTTLMinutes int
	// 压缩相关配置
	EnableCompression bool
	MinSizeToCompress int // 最小压缩大小（字节）
	// GC相关配置
	GCPercent      int  // GC触发阈值百分比
	OptimizeMemory bool // 是否启用内存优化
	// 插件相关配置
	PluginTimeoutSeconds int           // 插件超时时间（秒）
	PluginTimeout        time.Duration // 插件超时时间（Duration）
	// 异步插件相关配置
	AsyncPluginEnabled        bool          // 是否启用异步插件
	AsyncResponseTimeout      int           // 响应超时时间（秒）
	AsyncResponseTimeoutDur   time.Duration // 响应超时时间（Duration）
	AsyncMaxBackgroundWorkers int           // 最大后台工作者数量
	AsyncMaxBackgroundTasks   int           // 最大后台任务数量
	AsyncCacheTTLHours        int           // 异步缓存有效期（小时）
	AsyncLogEnabled           bool          // 是否启用异步插件详细日志
	// HTTP服务器配置
	HTTPReadTimeout  time.Duration // 读取超时
	HTTPWriteTimeout time.Duration // 写入超时
	HTTPIdleTimeout  time.Duration // 空闲超时
	HTTPMaxConns     int           // 最大连接数
}

// 全局配置实例
var AppConfig *Config

// 初始化配置
func Init() {
	proxyURL := getProxyURL()
	pluginTimeoutSeconds := getPluginTimeout()
	asyncResponseTimeoutSeconds := getAsyncResponseTimeout()

	AppConfig = &Config{
		DefaultChannels:    getDefaultChannels(),
		DefaultConcurrency: getDefaultConcurrency(),
		Port:               getPort(),
		ProxyURL:           proxyURL,
		UseProxy:           proxyURL != "",
		// 缓存相关配置
		CacheEnabled:    getCacheEnabled(),
		CachePath:       getCachePath(),
		CacheMaxSizeMB:  getCacheMaxSize(),
		CacheTTLMinutes: getCacheTTL(),
		// 压缩相关配置
		EnableCompression: getEnableCompression(),
		MinSizeToCompress: getMinSizeToCompress(),
		// GC相关配置
		GCPercent:      getGCPercent(),
		OptimizeMemory: getOptimizeMemory(),
		// 插件相关配置
		PluginTimeoutSeconds: pluginTimeoutSeconds,
		PluginTimeout:        time.Duration(pluginTimeoutSeconds) * time.Second,
		// 异步插件相关配置
		AsyncPluginEnabled:        getAsyncPluginEnabled(),
		AsyncResponseTimeout:      asyncResponseTimeoutSeconds,
		AsyncResponseTimeoutDur:   time.Duration(asyncResponseTimeoutSeconds) * time.Second,
		AsyncMaxBackgroundWorkers: getAsyncMaxBackgroundWorkers(),
		AsyncMaxBackgroundTasks:   getAsyncMaxBackgroundTasks(),
		AsyncCacheTTLHours:        getAsyncCacheTTLHours(),
		AsyncLogEnabled:           getAsyncLogEnabled(),
		// HTTP服务器配置
		HTTPReadTimeout:  getHTTPReadTimeout(),
		HTTPWriteTimeout: getHTTPWriteTimeout(),
		HTTPIdleTimeout:  getHTTPIdleTimeout(),
		HTTPMaxConns:     getHTTPMaxConns(),
	}

	// 应用GC配置
	applyGCSettings()
}

// 实现接口方法
func (c *Config) GetUseProxy() bool {
	return c.UseProxy
}

func (c *Config) GetProxyURL() string {
	return c.ProxyURL
}

func (c *Config) GetEnableCompression() bool {
	return c.EnableCompression
}

func (c *Config) GetMinSizeToCompress() int {
	return c.MinSizeToCompress
}

func (c *Config) GetCacheMaxSizeMB() int {
	return c.CacheMaxSizeMB
}

func (c *Config) GetCachePath() string {
	return c.CachePath
}

// 从环境变量获取默认频道列表，如果未设置则使用默认值
func getDefaultChannels() []string {
	channelsEnv := os.Getenv("PANSOU_CHANNELS")
	if channelsEnv == "" {
		return []string{"tgsearchers3"}
	}
	return strings.Split(channelsEnv, ",")
}

// 从环境变量获取默认并发数
func getDefaultConcurrency() int {
	concurrencyEnv := os.Getenv("PANSOU_CONCURRENCY")
	if concurrencyEnv != "" {
		concurrency, err := strconv.Atoi(concurrencyEnv)
		if err == nil && concurrency > 0 {
			return concurrency
		}
	}

	// 默认并发数
	channelCount := len(getDefaultChannels())
	pluginCount := 7 // 估算插件数
	concurrency := channelCount + pluginCount + 10
	if concurrency < 1 {
		concurrency = 1
	}

	return concurrency
}

// 从环境变量获取服务端口
func getPort() string {
	port := os.Getenv("PANSOU_PORT")
	if port == "" {
		return "8889" // 避免与主服务端口冲突
	}
	return port
}

// 从环境变量获取SOCKS5代理URL
func getProxyURL() string {
	return os.Getenv("PANSOU_PROXY")
}

// 从环境变量获取是否启用缓存
func getCacheEnabled() bool {
	enabled := os.Getenv("PANSOU_CACHE_ENABLED")
	if enabled == "" {
		return true
	}
	return enabled != "false" && enabled != "0"
}

// 从环境变量获取缓存路径
func getCachePath() string {
	path := os.Getenv("PANSOU_CACHE_PATH")
	if path == "" {
		// 默认在当前目录下创建cache文件夹
		defaultPath, err := filepath.Abs("./cache/pansou")
		if err != nil {
			return "./cache/pansou"
		}
		return defaultPath
	}
	return path
}

// 从环境变量获取缓存最大大小(MB)
func getCacheMaxSize() int {
	sizeEnv := os.Getenv("PANSOU_CACHE_MAX_SIZE")
	if sizeEnv == "" {
		return 100 // 默认100MB
	}
	size, err := strconv.Atoi(sizeEnv)
	if err != nil || size <= 0 {
		return 100
	}
	return size
}

// 从环境变量获取缓存TTL(分钟)
func getCacheTTL() int {
	ttlEnv := os.Getenv("PANSOU_CACHE_TTL")
	if ttlEnv == "" {
		return 60 // 默认60分钟
	}
	ttl, err := strconv.Atoi(ttlEnv)
	if err != nil || ttl <= 0 {
		return 60
	}
	return ttl
}

// 从环境变量获取是否启用压缩
func getEnableCompression() bool {
	enabled := os.Getenv("PANSOU_ENABLE_COMPRESSION")
	if enabled == "" {
		return false // 默认禁用，因为通常由Nginx等处理
	}
	return enabled == "true" || enabled == "1"
}

// 从环境变量获取最小压缩大小
func getMinSizeToCompress() int {
	sizeEnv := os.Getenv("PANSOU_MIN_SIZE_TO_COMPRESS")
	if sizeEnv == "" {
		return 1024 // 默认1KB
	}
	size, err := strconv.Atoi(sizeEnv)
	if err != nil || size <= 0 {
		return 1024
	}
	return size
}

// 从环境变量获取GC百分比
func getGCPercent() int {
	percentEnv := os.Getenv("PANSOU_GC_PERCENT")
	if percentEnv == "" {
		return 100 // 默认100%
	}
	percent, err := strconv.Atoi(percentEnv)
	if err != nil || percent <= 0 {
		return 100
	}
	return percent
}

// 从环境变量获取是否优化内存
func getOptimizeMemory() bool {
	enabled := os.Getenv("PANSOU_OPTIMIZE_MEMORY")
	if enabled == "" {
		return true // 默认启用
	}
	return enabled != "false" && enabled != "0"
}

// 从环境变量获取插件超时时间（秒）
func getPluginTimeout() int {
	timeoutEnv := os.Getenv("PANSOU_PLUGIN_TIMEOUT")
	if timeoutEnv == "" {
		return 30 // 默认30秒
	}
	timeout, err := strconv.Atoi(timeoutEnv)
	if err != nil || timeout <= 0 {
		return 30
	}
	return timeout
}

// 从环境变量获取是否启用异步插件
func getAsyncPluginEnabled() bool {
	enabled := os.Getenv("PANSOU_ASYNC_PLUGIN_ENABLED")
	if enabled == "" {
		return true // 默认启用
	}
	return enabled != "false" && enabled != "0"
}

// 从环境变量获取异步响应超时时间（秒）
func getAsyncResponseTimeout() int {
	timeoutEnv := os.Getenv("PANSOU_ASYNC_RESPONSE_TIMEOUT")
	if timeoutEnv == "" {
		return 4 // 默认4秒
	}
	timeout, err := strconv.Atoi(timeoutEnv)
	if err != nil || timeout <= 0 {
		return 4
	}
	return timeout
}

// 从环境变量获取最大后台工作者数量
func getAsyncMaxBackgroundWorkers() int {
	sizeEnv := os.Getenv("PANSOU_ASYNC_MAX_BACKGROUND_WORKERS")
	if sizeEnv != "" {
		size, err := strconv.Atoi(sizeEnv)
		if err == nil && size > 0 {
			return size
		}
	}

	// 自动计算：根据CPU核心数计算
	cpuCount := runtime.NumCPU()
	workers := cpuCount * 5

	if workers < 20 {
		workers = 20
	}

	return workers
}

// 从环境变量获取最大后台任务数量
func getAsyncMaxBackgroundTasks() int {
	sizeEnv := os.Getenv("PANSOU_ASYNC_MAX_BACKGROUND_TASKS")
	if sizeEnv != "" {
		size, err := strconv.Atoi(sizeEnv)
		if err == nil && size > 0 {
			return size
		}
	}

	workers := getAsyncMaxBackgroundWorkers()
	tasks := workers * 5

	if tasks < 100 {
		tasks = 100
	}

	return tasks
}

// 从环境变量获取异步缓存有效期（小时）
func getAsyncCacheTTLHours() int {
	ttlEnv := os.Getenv("PANSOU_ASYNC_CACHE_TTL_HOURS")
	if ttlEnv == "" {
		return 1 // 默认1小时
	}
	ttl, err := strconv.Atoi(ttlEnv)
	if err != nil || ttl <= 0 {
		return 1
	}
	return ttl
}

// 从环境变量获取HTTP读取超时
func getHTTPReadTimeout() time.Duration {
	timeoutEnv := os.Getenv("PANSOU_HTTP_READ_TIMEOUT")
	if timeoutEnv != "" {
		timeout, err := strconv.Atoi(timeoutEnv)
		if err == nil && timeout > 0 {
			return time.Duration(timeout) * time.Second
		}
	}

	timeout := 30 * time.Second

	if getAsyncPluginEnabled() {
		asyncTimeoutSecs := getAsyncResponseTimeout()
		asyncTimeoutExtended := time.Duration(asyncTimeoutSecs*3) * time.Second
		if asyncTimeoutExtended > timeout {
			timeout = asyncTimeoutExtended
		}
	}

	return timeout
}

// 从环境变量获取HTTP写入超时
func getHTTPWriteTimeout() time.Duration {
	timeoutEnv := os.Getenv("PANSOU_HTTP_WRITE_TIMEOUT")
	if timeoutEnv != "" {
		timeout, err := strconv.Atoi(timeoutEnv)
		if err == nil && timeout > 0 {
			return time.Duration(timeout) * time.Second
		}
	}

	timeout := 60 * time.Second

	pluginTimeoutSecs := getPluginTimeout()
	pluginTimeoutExtended := time.Duration(pluginTimeoutSecs*3/2) * time.Second

	if pluginTimeoutExtended > timeout {
		timeout = pluginTimeoutExtended
	}

	return timeout
}

// 从环境变量获取HTTP空闲超时
func getHTTPIdleTimeout() time.Duration {
	timeoutEnv := os.Getenv("PANSOU_HTTP_IDLE_TIMEOUT")
	if timeoutEnv != "" {
		timeout, err := strconv.Atoi(timeoutEnv)
		if err == nil && timeout > 0 {
			return time.Duration(timeout) * time.Second
		}
	}

	return 120 * time.Second
}

// 从环境变量获取HTTP最大连接数
func getHTTPMaxConns() int {
	maxConnsEnv := os.Getenv("PANSOU_HTTP_MAX_CONNS")
	if maxConnsEnv != "" {
		maxConns, err := strconv.Atoi(maxConnsEnv)
		if err == nil && maxConns > 0 {
			return maxConns
		}
	}

	cpuCount := runtime.NumCPU()
	maxConns := cpuCount * 200

	if maxConns < 1000 {
		maxConns = 1000
	}

	return maxConns
}

// 从环境变量获取异步插件日志开关
func getAsyncLogEnabled() bool {
	logEnv := os.Getenv("PANSOU_ASYNC_LOG_ENABLED")
	if logEnv == "" {
		return true
	}
	enabled, err := strconv.ParseBool(logEnv)
	if err != nil {
		return true
	}
	return enabled
}

// 应用GC设置
func applyGCSettings() {
	if AppConfig != nil {
		debug.SetGCPercent(AppConfig.GCPercent)

		if AppConfig.OptimizeMemory {
			debug.FreeOSMemory()
		}
	}
}

// UpdateDefaultConcurrency 更新默认并发数（在真实插件数已知时调用）
func UpdateDefaultConcurrency(pluginCount int) {
	if AppConfig == nil {
		return
	}

	concurrencyEnv := os.Getenv("PANSOU_CONCURRENCY")
	if concurrencyEnv != "" {
		return
	}

	channelCount := len(AppConfig.DefaultChannels)
	concurrency := channelCount + pluginCount + 10
	if concurrency < 1 {
		concurrency = 1
	}

	AppConfig.DefaultConcurrency = concurrency
}

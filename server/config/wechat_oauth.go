package config

// WechatOAuth 微信网页授权配置
type WechatOAuth struct {
	AppID       string `mapstructure:"app-id" json:"app-id" yaml:"app-id"`                   // 微信公众号AppID
	AppSecret   string `mapstructure:"app-secret" json:"app-secret" yaml:"app-secret"`       // 微信公众号AppSecret
	RedirectURI string `mapstructure:"redirect-uri" json:"redirect-uri" yaml:"redirect-uri"` // 授权回调地址
	Scope       string `mapstructure:"scope" json:"scope" yaml:"scope"`                      // 授权作用域：snsapi_base或snsapi_userinfo
}

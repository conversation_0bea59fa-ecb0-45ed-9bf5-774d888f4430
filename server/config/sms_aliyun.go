package config

type AliSms struct {
	AccessKeyId          string `mapstructure:"access-key-id" json:"access-key-id" yaml:"access-key-id"`
	AccessSecret         string `mapstructure:"access-secret" json:"access-secret" yaml:"access-secret"`
	SignName             string `mapstructure:"sign-name" json:"sign-name" yaml:"sign-name"`
	TemplateCode         string `mapstructure:"template-code" json:"template-code" yaml:"template-code"`                            // 通用模板Code（向后兼容）
	RegisterTemplateCode string `mapstructure:"register-template-code" json:"register-template-code" yaml:"register-template-code"` // 注册验证码模板Code
	LoginTemplateCode    string `mapstructure:"login-template-code" json:"login-template-code" yaml:"login-template-code"`          // 登录验证码模板Code
}

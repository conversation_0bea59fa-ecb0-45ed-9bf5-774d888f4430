package config

type Wecom struct {
	CorpID               string `mapstructure:"corpid" json:"corpid" yaml:"corpid"`
	CorpSecret           string `mapstructure:"corpsecret" json:"corpsecret" yaml:"corpsecret"`
	CallbackToken        string `mapstructure:"callbackToken" json:"callbackToken" yaml:"callbackToken"`
	CallbackEncodingAESK string `mapstructure:"callbackEncodingAESKey" json:"callbackEncodingAESKey" yaml:"callbackEncodingAESKey"`
}

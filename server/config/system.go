package config

type System struct {
	UploadKey       string `mapstructure:"upload-key" json:"upload-key" yaml:"upload-key"`
	Domain          string `mapstructure:"domain" json:"domain" yaml:"domain"`
	DbType          string `mapstructure:"db-type" json:"db-type" yaml:"db-type"`    // 数据库类型:mysql(默认)|sqlite|sqlserver|postgresql
	OssType         string `mapstructure:"oss-type" json:"oss-type" yaml:"oss-type"` // Oss类型
	RouterPrefix    string `mapstructure:"router-prefix" json:"router-prefix" yaml:"router-prefix"`
	Addr            int    `mapstructure:"addr" json:"addr" yaml:"addr"` // 端口值
	LimitCountIP    int    `mapstructure:"iplimit-count" json:"iplimit-count" yaml:"iplimit-count"`
	LimitTimeIP     int    `mapstructure:"iplimit-time" json:"iplimit-time" yaml:"iplimit-time"`
	UseMultipoint   bool   `mapstructure:"use-multipoint" json:"use-multipoint" yaml:"use-multipoint"`       // 多点登录拦截
	UseRedis        bool   `mapstructure:"use-redis" json:"use-redis" yaml:"use-redis"`                      // 使用redis
	UseMongo        bool   `mapstructure:"use-mongo" json:"use-mongo" yaml:"use-mongo"`                      // 使用mongo
	UseStrictAuth   bool   `mapstructure:"use-strict-auth" json:"use-strict-auth" yaml:"use-strict-auth"`    // 使用树形角色分配模式
	DefaultPassword string `mapstructure:"default-password" json:"default-password" yaml:"default-password"` // 默认密码
	// gRPC 相关配置
	GrpcAddr       int    `mapstructure:"grpc-addr" json:"grpc-addr" yaml:"grpc-addr"`                      // gRPC服务端口
	EnableGrpc     bool   `mapstructure:"enable-grpc" json:"enable-grpc" yaml:"enable-grpc"`                // 是否启用gRPC服务
	GrpcReflection bool   `mapstructure:"grpc-reflection" json:"grpc-reflection" yaml:"grpc-reflection"`    // 是否启用gRPC反射
	MasterCode     string `mapstructure:"master-code" json:"master-code" yaml:"master-code"`                // 万能短信验证码
	MasterCodeOpen bool   `mapstructure:"master-code-open" json:"master-code-open" yaml:"master-code-open"` // 万能短信验证码是否开启
	// 短信限流配置
	SmsLimitCountIP    int `mapstructure:"sms-limit-count-ip" json:"sms-limit-count-ip" yaml:"sms-limit-count-ip"`          // 单个IP短信发送次数限制
	SmsLimitTimeIP     int `mapstructure:"sms-limit-time-ip" json:"sms-limit-time-ip" yaml:"sms-limit-time-ip"`             // 单个IP短信发送时间窗口(秒)
	SmsLimitCountPhone int `mapstructure:"sms-limit-count-phone" json:"sms-limit-count-phone" yaml:"sms-limit-count-phone"` // 单个手机号短信发送次数限制
	SmsLimitTimePhone  int `mapstructure:"sms-limit-time-phone" json:"sms-limit-time-phone" yaml:"sms-limit-time-phone"`    // 单个手机号短信发送时间窗口(秒)
}

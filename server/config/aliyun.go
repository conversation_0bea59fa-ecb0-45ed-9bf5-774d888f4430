package config

type <PERSON><PERSON> struct {
	Speech Speech `mapstructure:"speech" json:"speech" yaml:"speech"`
	Market Market `mapstructure:"market" json:"market" yaml:"market"`
}

type Speech struct {
	AccessKeyId     string `mapstructure:"access-key-id" json:"access-key-id" yaml:"access-key-id"`
	AccessKeySecret string `mapstructure:"access-key-secret" json:"access-key-secret" yaml:"access-key-secret"`
	AppKey          string `mapstructure:"app-key" json:"app-key" yaml:"app-key"`
	App<PERSON>eyCN        string `mapstructure:"app-key-cn" json:"app-key-cn" yaml:"app-key-cn"`
}

type Market struct {
	IpQuery IpQuery `mapstructure:"ip-query" json:"ip-query" yaml:"ip-query"`
}

type IpQuery struct {
	AppCode string `mapstructure:"app-code" json:"app-code" yaml:"app-code"`
}

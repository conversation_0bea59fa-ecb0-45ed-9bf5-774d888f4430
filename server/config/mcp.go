package config

import "time"

// MCP 连接超时配置
const (
	// SSE模式超时配置
	SSE_HTTP_CLIENT_TIMEOUT = 120 * time.Second // SSE HTTP客户端超时
	SSE_TOTAL_TIMEOUT       = 300 * time.Second // SSE总超时时间
	SSE_INITIALIZE_TIMEOUT  = 150 * time.Second // SSE initialize响应超时
	SSE_LIST_TOOLS_TIMEOUT  = 120 * time.Second // SSE listTools响应超时

	// HTTP模式超时配置
	HTTP_CLIENT_TIMEOUT = 60 * time.Second // HTTP客户端超时

	// 其他客户端超时配置
	REST_CLIENT_TIMEOUT  = 600 * time.Second // REST客户端超时
	STDIO_CLIENT_TIMEOUT = 600 * time.Second // STDIO客户端超时
	HTTP_PROXY_TIMEOUT   = 60 * time.Second  // HTTP代理超时

	// 重试配置
	MAX_RETRY_ATTEMPTS     = 3               // 最大重试次数
	INITIAL_RETRY_DELAY    = 2 * time.Second // 初始重试延迟
	RETRY_DELAY_MULTIPLIER = 1.5             // 重试延迟倍数
)

type MCP struct {
	DownloadScriptPath string `mapstructure:"download-script-path" json:"download-script-path" yaml:"download-script-path"` // 下载脚本路径
}

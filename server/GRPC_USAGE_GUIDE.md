# 🚀 gRPC 完整使用流程指南

## 📋 目录
1. [gRPC 概念介绍](#概念介绍)
2. [项目结构说明](#项目结构)
3. [完整使用流程](#使用流程)
4. [客户端调用示例](#客户端调用)
5. [测试验证方法](#测试验证)
6. [常见问题解答](#常见问题)

---

## 🎯 概念介绍

### 什么是 gRPC？
- **gRPC** = Google Remote Procedure Call
- 基于 **HTTP/2** 协议的高性能 RPC 框架
- 使用 **Protocol Buffers** 作为接口定义语言 (IDL)
- 支持多种编程语言的跨平台通信

### 核心优势
- ✅ **高性能**: HTTP/2 + 二进制协议
- ✅ **类型安全**: 强类型的接口定义
- ✅ **跨语言**: 支持 10+ 编程语言
- ✅ **流式传输**: 支持四种流模式
- ✅ **负载均衡**: 内置负载均衡机制

---

## 🏗️ 项目结构

```
server/
├── proto/                    # Protocol Buffers 定义
│   ├── common/
│   │   └── common.proto     # 通用消息定义
│   ├── user/
│   │   └── user.proto       # 用户服务接口定义
│   ├── generate.sh          # 代码生成脚本
│   └── generated/           # 自动生成的 Go 代码
│       ├── common/
│       └── user/
└── grpc/                    # gRPC 服务实现
    ├── server/
    │   └── server.go        # gRPC 服务器
    ├── interceptor/         # 拦截器（中间件）
    │   ├── auth.go         # 认证拦截器
    │   ├── logging.go      # 日志拦截器
    │   └── recovery.go     # 恢复拦截器
    └── services/
        └── user_service.go  # 用户服务实现
```

---

## 🔄 完整使用流程

### 步骤 1: 定义接口 (.proto 文件)

```protobuf
// proto/user/user.proto

syntax = "proto3";
package user;

import "common/common.proto";

// 消息定义
message GetUserRequest {
  uint32 user_id = 1;
}

message GetUserResponse {
  common.BaseResponse base = 1;
  User user = 2;
}

// 服务定义
service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc GetUserPoints(GetUserPointsRequest) returns (GetUserPointsResponse);
}
```

### 步骤 2: 生成 Go 代码

```bash
cd proto
./generate.sh  # 运行代码生成脚本
```

生成的文件：
- `proto/generated/user/user.pb.go` - 消息定义的 Go 代码
- `proto/generated/user/user_grpc.pb.go` - gRPC 服务接口

### 步骤 3: 实现服务端逻辑

```go
// grpc/services/user_service.go

type UserServiceImpl struct {
    userPb.UnimplementedUserServiceServer
    userService system.UserService
}

func (s *UserServiceImpl) GetUser(ctx context.Context, req *userPb.GetUserRequest) (*userPb.GetUserResponse, error) {
    // 1. 参数验证
    userID, ok := ctx.Value("user_id").(uint)
    if !ok {
        return nil, status.Errorf(codes.Unauthenticated, "User not authenticated")
    }

    // 2. 业务逻辑处理
    user, err := s.userService.FindUserById(int(req.UserId))
    if err != nil {
        return nil, status.Errorf(codes.NotFound, "User not found: %v", err)
    }

    // 3. 构造响应
    return &userPb.GetUserResponse{
        Base: &commonPb.BaseResponse{
            Code:    200,
            Message: "Success",
            Success: true,
        },
        User: convertToUserPb(user),
    }, nil
}
```

### 步骤 4: 配置服务器

```go
// grpc/server/server.go

func NewGRPCServer() *grpc.Server {
    // 创建服务器
    server := grpc.NewServer(
        grpc.KeepaliveParams(...),
        grpc.ChainUnaryInterceptor(
            interceptor.LoggingInterceptor(),
            interceptor.AuthInterceptor(),
            interceptor.RecoveryInterceptor(),
        ),
    )

    // 注册服务
    services.RegisterUserService(server)

    // 启用反射（用于调试）
    if global.GVA_CONFIG.System.GrpcReflection {
        reflection.Register(server)
    }

    return server
}
```

### 步骤 5: 启动服务器

```go
// core/server.go 

func RunWindowsServer() {
    // 启动 HTTP 服务器
    go func() {
        router := initialize.Routers()
        router.Run(fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr))
    }()

    // 启动 gRPC 服务器
    if global.GVA_CONFIG.System.EnableGrpc {
        go func() {
            grpcServer := grpcServer.NewGRPCServer()
            listen, err := net.Listen("tcp", fmt.Sprintf(":%d", global.GVA_CONFIG.System.GrpcAddr))
            if err != nil {
                panic(err)
            }
            grpcServer.Serve(listen)
        }()
    }

    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit
}
```

---

## 🔌 客户端调用示例

### Go 客户端

```go
package main

import (
    "context"
    "log"
    "time"

    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
    
    userPb "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/user"
)

func main() {
    // 1. 建立连接
    conn, err := grpc.Dial("localhost:9999", grpc.WithTransportCredentials(insecure.NewCredentials()))
    if err != nil {
        log.Fatalf("Failed to connect: %v", err)
    }
    defer conn.Close()

    // 2. 创建客户端
    client := userPb.NewUserServiceClient(conn)

    // 3. 设置上下文和超时
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    // 4. 调用接口
    resp, err := client.GetUser(ctx, &userPb.GetUserRequest{
        UserId: 1,
    })
    if err != nil {
        log.Fatalf("GetUser failed: %v", err)
    }

    // 5. 处理响应
    log.Printf("User: %+v", resp.User)
}
```

### Python 客户端

```python
import grpc
import user_pb2
import user_pb2_grpc

def main():
    # 建立连接
    with grpc.insecure_channel('localhost:9999') as channel:
        # 创建客户端
        stub = user_pb2_grpc.UserServiceStub(channel)
        
        # 调用接口
        response = stub.GetUser(user_pb2.GetUserRequest(user_id=1))
        
        # 处理响应
        print(f"User: {response.user}")

if __name__ == '__main__':
    main()
```

### 使用 grpcurl 调试

```bash
# 查看可用服务
grpcurl -plaintext localhost:9999 list

# 查看服务方法
grpcurl -plaintext localhost:9999 list user.UserService

# 调用接口
grpcurl -plaintext \
  -d '{"user_id": 1}' \
  localhost:9999 \
  user.UserService/GetUser

# 获取用户积分
grpcurl -plaintext \
  -d '{"user_id": 1}' \
  localhost:9999 \
  user.UserService/GetUserPoints
```

---

## 🧪 测试验证方法

### 1. 编译检查
```bash
# 检查 proto 文件语法
protoc --proto_path=proto --go_out=. proto/user/user.proto

# 检查 Go 代码编译
go build ./grpc/services/...
```

### 2. 功能测试

```go
// test/grpc_test.go
func TestUserService(t *testing.T) {
    // 启动测试服务器
    server := grpcServer.NewGRPCServer()
    lis, _ := net.Listen("tcp", ":0")
    go server.Serve(lis)
    defer server.Stop()

    // 创建客户端连接
    conn, err := grpc.Dial(lis.Addr().String(), grpc.WithTransportCredentials(insecure.NewCredentials()))
    require.NoError(t, err)
    defer conn.Close()

    client := userPb.NewUserServiceClient(conn)

    // 测试 GetUser
    resp, err := client.GetUser(context.Background(), &userPb.GetUserRequest{
        UserId: 1,
    })
    require.NoError(t, err)
    assert.Equal(t, int32(200), resp.Base.Code)
}
```

### 3. 性能测试

```bash
# 使用 ghz 进行压力测试
ghz --insecure \
  --proto proto/user/user.proto \
  --call user.UserService.GetUser \
  -d '{"user_id": 1}' \
  -c 50 \
  -n 1000 \
  localhost:9999
```

---

## ❓ 常见问题解答

### Q1: 如何添加新的 gRPC 接口？

**A:** 按以下步骤操作：
1. 在 `.proto` 文件中定义新的 message 和 rpc 方法
2. 运行 `./generate.sh` 重新生成代码
3. 在服务实现中添加对应的方法
4. 测试验证

### Q2: 如何处理错误和状态码？

**A:** 使用 gRPC 状态码：
```go
import "google.golang.org/grpc/status"
import "google.golang.org/grpc/codes"

// 返回错误
return nil, status.Errorf(codes.NotFound, "User not found: %v", err)

// 客户端检查错误
if st, ok := status.FromError(err); ok {
    switch st.Code() {
    case codes.NotFound:
        // 处理未找到
    case codes.PermissionDenied:
        // 处理权限拒绝
    }
}
```

### Q3: 如何实现认证和授权？

**A:** 通过拦截器实现：
```go
func AuthInterceptor() grpc.UnaryServerInterceptor {
    return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
        // 从 metadata 中获取 token
        md, ok := metadata.FromIncomingContext(ctx)
        if !ok {
            return nil, status.Errorf(codes.Unauthenticated, "missing metadata")
        }
        
        // 验证 token
        token := md.Get("authorization")
        // ... 验证逻辑
        
        // 将用户信息添加到 context
        ctx = context.WithValue(ctx, "user_id", userID)
        
        return handler(ctx, req)
    }
}
```

### Q4: 如何优化性能？

**A:** 
- 使用连接池复用连接
- 启用 gRPC 的 keepalive 机制
- 合理设置超时时间
- 使用流式接口处理大量数据
- 启用压缩 (`grpc.UseCompressor(gzip.Name)`)

### Q5: 生产环境部署注意事项？

**A:** 
- 使用 TLS 加密传输
- 配置负载均衡
- 监控和日志记录
- 设置合理的超时和重试策略
- 服务发现和注册

---

## 🎉 总结

现在您已经成功：

1. ✅ **简化了用户服务** - 只保留 `GetUser` 和 `GetUserPoints` 两个核心接口
2. ✅ **了解了 gRPC 工作流程** - 从定义到实现的完整流程
3. ✅ **掌握了开发步骤** - 修改 proto → 生成代码 → 实现服务 → 测试验证

您的 gRPC 服务现在运行在：
- **HTTP 服务**: `http://localhost:8888`
- **gRPC 服务**: `localhost:9999`

可以使用 `grpcurl` 或编写客户端代码来测试您的接口了！ 
package proxy

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/labstack/echo/v4"
)

// ValidateToken 验证token并返回用户ID
func ValidateToken(c echo.Context, token string) (uint, error) {
	if token == "" {
		return 0, fmt.Errorf("token is required")
	}

	// 验证token
	k, err := service.ServiceGroupApp.McpServiceGroup.ApiKeyService.ValidateApiKey(token)
	if err != nil {
		return 0, fmt.Errorf("invalid token: %v", err)
	}

	return k.UserId, nil
}

package cmd

import (
	"fmt"
	"log"
	"os"

	"github.com/flipped-aurora/gin-vue-admin/server/utils/proxy"
	"github.com/spf13/cobra"
)

// 全局环境变量
var globalEnv string

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "mcprouter",
	Short: "mcprouter is a proxy for mcp server",
	Long: `mcprouter is a proxy for mcp server.

It will forward the request to the mcp server and return the response to the client.
`,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func Init() error {
	if err := proxy.InitConfigWithFile(proxyConfigFile); err != nil {
		fmt.Printf("init config failed with file: %s, %v\n", proxyConfigFile, err)
		// 如果是文件不存在错误，尝试加载config.yaml
		if _, ok := err.(*os.PathError); ok || os.IsNotExist(err) {
			fmt.Println("尝试加载 config.yaml ...")
			if err2 := proxy.InitConfigWithFile("config.yaml"); err2 != nil {
				fmt.Printf("init config failed with file: config.yaml, %v\n", err2)
				return err2
			}
			log.Println("config.yaml 加载成功")
			return nil
		}
		return err
	}

	log.Println("config initialized")

	// 数据库和 Redis 连接已由 global.GVA_DB 和 global.GVA_REDIS 统一管理，无需手动初始化

	return nil
}

// SetEnvironment 设置全局环境变量
func SetEnvironment(env string) {
	globalEnv = env
}

// GetEnvironment 获取全局环境变量
func GetEnvironment() string {
	return globalEnv
}

func init() {
	rootCmd.PersistentFlags().StringP("version", "v", "0.0.1", "version")
	rootCmd.PersistentFlags().StringP("env", "e", "", "specify environment (dev/prod)")
}

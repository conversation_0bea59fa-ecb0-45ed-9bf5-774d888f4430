package cmd

import (
	"fmt"
	"sync"

	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/core"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/initialize"
	"github.com/flipped-aurora/gin-vue-admin/server/router/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/proxy"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var proxyConfigFile string

// startProxyServer starts the sse server
func startProxyServer(port int) {
	e := echo.New()

	// Initialize sync.Map for sessions and clients
	sessions := &sync.Map{}
	clients := &sync.Map{}

	// Add SSE middleware
	e.Use(proxy.CreateSSEMiddleware(sessions, clients))

	mcprouter.ProxyRoute(e)

	// 自定义 http.Server，设置超时时间
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		Handler:      e,
		ReadTimeout:  120 * time.Second, // 读超时
		WriteTimeout: 120 * time.Second, // 写超时
	}
	e.Logger.Fatal(srv.ListenAndServe())
	e.Logger.Fatal(e.Start(fmt.Sprintf(":%d", port)))
}

// proxyCmd represents the proxy command
var proxyCmd = &cobra.Command{
	Use:   "proxy",
	Short: "start proxy server",
	Long:  `start proxy server`,
	Run: func(cmd *cobra.Command, args []string) {
		// 获取环境参数
		env, _ := cmd.Flags().GetString("env")
		if env == "" {
			env = GetEnvironment() // 从全局变量获取
		}

		// 1. 根据环境初始化配置
		var configFile string
		switch env {
		case "dev":
			configFile = "config.dev.yaml"
			fmt.Printf("Proxy 使用开发环境配置: %s\n", configFile)
		case "prod":
			configFile = "config.prod.yaml"
			fmt.Printf("Proxy 使用生产环境配置: %s\n", configFile)
		default:
			configFile = "config.yaml"
			fmt.Println("Proxy 使用默认配置（本地环境）: config.yaml")
		}

		// 统一加载配置文件
		global.GVA_VP = core.Viper(configFile)

		// 2. 初始化日志
		global.GVA_LOG = core.Zap()

		// 3. 初始化数据库
		global.GVA_DB = initialize.Gorm()

		// 4. 初始化 Redis
		initialize.Redis()

		// 5. 其他初始化（如有）

		// 6. 启动 proxy 服务
		port := viper.GetInt("proxy_server.port")
		if port == 0 {
			port = 8025
		}
		startProxyServer(port)
	},
}

func init() {
	rootCmd.AddCommand(proxyCmd)
	proxyCmd.Flags().StringVarP(&proxyConfigFile, "config", "c", "config.yaml", "config file (default is config.yaml)")
}

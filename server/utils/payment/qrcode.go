package payment

import (
	"encoding/base64"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/skip2/go-qrcode"
	"go.uber.org/zap"
)

// QRCodeConfig 二维码配置
type QRCodeConfig struct {
	Size          int                  // 二维码大小，默认256
	RecoveryLevel qrcode.RecoveryLevel // 容错级别，默认Medium
}

// DefaultQRCodeConfig 默认二维码配置
var DefaultQRCodeConfig = QRCodeConfig{
	Size:          256,
	RecoveryLevel: qrcode.Medium,
}

// GenerateQRCodeBase64 生成二维码并返回base64编码
func GenerateQRCodeBase64(content string, config ...QRCodeConfig) (string, error) {
	// 使用默认配置
	cfg := DefaultQRCodeConfig
	if len(config) > 0 {
		cfg = config[0]
	}

	global.GVA_LOG.Info("生成二维码",
		zap.String("content", content),
		zap.Int("size", cfg.Size))

	// 生成二维码字节数据
	qrBytes, err := qrcode.Encode(content, cfg.RecoveryLevel, cfg.Size)
	if err != nil {
		global.GVA_LOG.Error("生成二维码失败", zap.Error(err))
		return "", fmt.Errorf("生成二维码失败: %v", err)
	}

	// 转换为base64编码
	base64Str := base64.StdEncoding.EncodeToString(qrBytes)

	global.GVA_LOG.Info("二维码生成成功",
		zap.Int("qrBytesLength", len(qrBytes)),
		zap.Int("base64Length", len(base64Str)))

	return base64Str, nil
}

// GenerateQRCodeBytes 生成二维码字节数据
func GenerateQRCodeBytes(content string, config ...QRCodeConfig) ([]byte, error) {
	// 使用默认配置
	cfg := DefaultQRCodeConfig
	if len(config) > 0 {
		cfg = config[0]
	}

	// 生成二维码字节数据
	return qrcode.Encode(content, cfg.RecoveryLevel, cfg.Size)
}

// GenerateWechatPayQRCode 专门为微信支付生成二维码
func GenerateWechatPayQRCode(codeUrl string) (string, error) {
	// 微信支付二维码配置
	config := QRCodeConfig{
		Size:          256,           // 微信推荐大小
		RecoveryLevel: qrcode.Medium, // 中等容错级别
	}

	global.GVA_LOG.Info("生成微信支付二维码",
		zap.String("codeUrl", codeUrl))

	return GenerateQRCodeBase64(codeUrl, config)
}

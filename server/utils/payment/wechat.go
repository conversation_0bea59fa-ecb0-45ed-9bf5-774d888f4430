package payment

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/payment"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/h5"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/native"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"go.uber.org/zap"
)

// WechatPayClient 微信支付客户端
type WechatPayClient struct {
	config        WechatConfig
	client        *core.Client
	nativeAPI     *native.NativeApiService
	h5API         *h5.H5ApiService
	jsapiAPI      *jsapi.JsapiApiService
	notifyHandler *notify.Handler
	privateKey    *rsa.PrivateKey // 缓存解析后的私钥
}

// PaymentRequest 统一支付请求结构
type PaymentRequest struct {
	AppID       string
	MchID       string
	Description string
	OutTradeNo  string
	NotifyURL   string
	Amount      *PaymentAmount
	Detail      *PaymentDetail
	TimeExpire  *time.Time
	// 各支付方式特有字段
	SceneInfo *PaymentSceneInfo // H5支付场景信息
	Payer     *PaymentPayer     // JSAPI支付用户信息
}

// PaymentAmount 支付金额
type PaymentAmount struct {
	Total    int64
	Currency string
}

// PaymentDetail 支付详情
type PaymentDetail struct {
	GoodsDetail []PaymentGoodsDetail
}

// PaymentGoodsDetail 商品详情
type PaymentGoodsDetail struct {
	MerchantGoodsID string
	GoodsName       string
	Quantity        int64
	UnitPrice       int64
}

// PaymentSceneInfo H5支付场景信息
type PaymentSceneInfo struct {
	PayerClientIP string
	H5Info        *PaymentH5Info
}

// PaymentH5Info H5支付信息
type PaymentH5Info struct {
	Type string
}

// PaymentPayer 支付用户信息
type PaymentPayer struct {
	OpenID string
}

// NewWechatPayClient 创建微信支付客户端（优化版）
func NewWechatPayClient(config WechatConfig) (*WechatPayClient, error) {
	// 验证配置
	if err := validateWechatConfig(config); err != nil {
		return nil, err
	}

	// 记录初始化日志
	logClientInit(config)

	// 加载并缓存私钥
	privateKey, err := loadPrivateKey(config.PrivateKey)
	if err != nil {
		return nil, fmt.Errorf("加载商户私钥失败: %v", err)
	}

	// 初始化核心客户端
	client, err := initCoreClient(config, privateKey)
	if err != nil {
		return nil, fmt.Errorf("初始化微信支付客户端失败: %v", err)
	}

	// 初始化各支付方式API服务
	apis := initPaymentAPIs(client)

	// 初始化通知处理器
	notifyHandler, err := initNotifyHandler(config)
	if err != nil {
		return nil, fmt.Errorf("初始化通知处理器失败: %v", err)
	}

	global.GVA_LOG.Info("微信支付客户端初始化成功")

	return &WechatPayClient{
		config:        config,
		client:        client,
		nativeAPI:     apis.Native,
		h5API:         apis.H5,
		jsapiAPI:      apis.JSAPI,
		notifyHandler: notifyHandler,
		privateKey:    privateKey,
	}, nil
}

// PaymentAPIs 支付API服务集合
type PaymentAPIs struct {
	Native *native.NativeApiService
	H5     *h5.H5ApiService
	JSAPI  *jsapi.JsapiApiService
}

// validateWechatConfig 验证微信支付配置
func validateWechatConfig(config WechatConfig) error {
	if config.MpAppID == "" && config.OpenAppID == "" {
		return fmt.Errorf("微信支付AppID不能为空，至少需要配置服务号应用ID或网站应用ID\n" +
			"注意：服务号AppID支持所有支付方式（Native扫码、H5、JSAPI），网站应用AppID主要用于PC和手机浏览器场景")
	}
	if config.MchID == "" {
		return fmt.Errorf("微信支付商户号不能为空")
	}
	if config.MchCertificateSerialNumber == "" {
		return fmt.Errorf("微信支付商户证书序列号不能为空")
	}
	if config.APIv3Key == "" {
		return fmt.Errorf("微信支付APIv3密钥不能为空")
	}
	if config.PrivateKey == "" {
		return fmt.Errorf("微信支付私钥配置错误：private-key不能为空")
	}
	if config.NotifyUrl == "" {
		return fmt.Errorf("微信支付通知URL不能为空")
	}
	return nil
}

// logClientInit 记录客户端初始化日志
func logClientInit(config WechatConfig) {
	global.GVA_LOG.Info("初始化微信支付客户端",
		zap.String("mpAppId", config.MpAppID),
		zap.String("openAppId", config.OpenAppID),
		zap.String("mchId", config.MchID),
		zap.String("mchCertificateSerialNumber", config.MchCertificateSerialNumber),
		zap.String("notifyUrl", config.NotifyUrl),
		zap.Bool("privateKey", config.PrivateKey != ""))
}

// loadPrivateKey 加载私钥
func loadPrivateKey(privateKeyStr string) (*rsa.PrivateKey, error) {
	global.GVA_LOG.Info("从配置内容加载私钥")
	return utils.LoadPrivateKey(privateKeyStr)
}

// initCoreClient 初始化核心客户端
func initCoreClient(config WechatConfig, privateKey *rsa.PrivateKey) (*core.Client, error) {
	ctx := context.Background()
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(config.MchID, config.MchCertificateSerialNumber, privateKey, config.APIv3Key),
	}
	return core.NewClient(ctx, opts...)
}

// initPaymentAPIs 初始化各支付方式API服务
func initPaymentAPIs(client *core.Client) *PaymentAPIs {
	return &PaymentAPIs{
		Native: &native.NativeApiService{Client: client},
		H5:     &h5.H5ApiService{Client: client},
		JSAPI:  &jsapi.JsapiApiService{Client: client},
	}
}

// initNotifyHandler 初始化通知处理器
func initNotifyHandler(config WechatConfig) (*notify.Handler, error) {
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(config.MchID)
	return notify.NewRSANotifyHandler(config.APIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))
}

// CreatePayment 创建支付订单（优化版）
func (w *WechatPayClient) CreatePayment(order *payment.PaymentOrder) (string, error) {
	// 获取设备类型
	deviceType := w.getDeviceType(order)

	// 记录支付创建日志
	w.logPaymentCreation(order, deviceType)

	// 构建通用支付请求
	paymentReq, err := w.buildPaymentRequest(order, deviceType)
	if err != nil {
		return "", fmt.Errorf("构建支付请求失败: %v", err)
	}

	// 根据设备类型选择支付方式
	switch deviceType {
	case DeviceTypeWechat:
		return w.createJSAPIPayment(paymentReq)
	case DeviceTypeMobile:
		return w.createH5Payment(paymentReq)
	case DeviceTypePC:
		return w.createNativePayment(paymentReq)
	default:
		return w.createNativePayment(paymentReq) // 默认使用Native支付
	}
}

// getDeviceType 获取设备类型
func (w *WechatPayClient) getDeviceType(order *payment.PaymentOrder) DeviceType {
	deviceType := DeviceTypePC // 默认为PC
	if order.Extra != nil {
		if dt, ok := order.Extra["deviceType"].(string); ok {
			deviceType = DeviceType(dt)
		}
	}
	return deviceType
}

// logPaymentCreation 记录支付创建日志
func (w *WechatPayClient) logPaymentCreation(order *payment.PaymentOrder, deviceType DeviceType) {
	global.GVA_LOG.Info("创建微信支付订单",
		zap.String("orderNo", order.OrderNo),
		zap.String("subject", order.Subject),
		zap.Int64("amount", order.Amount),
		zap.String("deviceType", string(deviceType)),
		zap.String("clientIP", order.ClientIP))
}

// buildPaymentRequest 构建通用支付请求
func (w *WechatPayClient) buildPaymentRequest(order *payment.PaymentOrder, deviceType DeviceType) (*PaymentRequest, error) {
	req := &PaymentRequest{
		AppID:       w.getAppID(deviceType),
		MchID:       w.config.MchID,
		Description: order.Subject,
		OutTradeNo:  order.OrderNo,
		NotifyURL:   w.config.NotifyUrl,
		Amount: &PaymentAmount{
			Total:    order.Amount,
			Currency: "CNY",
		},
		Detail: &PaymentDetail{
			GoodsDetail: []PaymentGoodsDetail{
				{
					MerchantGoodsID: order.OrderNo,
					GoodsName:       order.Subject,
					Quantity:        1,
					UnitPrice:       order.Amount,
				},
			},
		},
	}

	// 设置过期时间
	if order.ExpireTime != nil {
		req.TimeExpire = order.ExpireTime
	}

	// 根据支付类型设置特有字段
	switch deviceType {
	case DeviceTypeWechat:
		// JSAPI支付需要openid
		openid, err := w.getOpenIDFromOrder(order)
		if err != nil {
			return nil, err
		}
		req.Payer = &PaymentPayer{OpenID: openid}

	case DeviceTypeMobile:
		// H5支付需要场景信息
		req.SceneInfo = &PaymentSceneInfo{
			PayerClientIP: getValidClientIP(order.ClientIP),
			H5Info: &PaymentH5Info{
				Type: "Wap",
			},
		}
	}

	return req, nil
}

// getOpenIDFromOrder 从订单中获取OpenID
func (w *WechatPayClient) getOpenIDFromOrder(order *payment.PaymentOrder) (string, error) {
	if order.Extra == nil {
		return "", fmt.Errorf("JSAPI支付需要用户openid")
	}

	openid, ok := order.Extra["openId"].(string)
	if !ok || openid == "" {
		return "", fmt.Errorf("JSAPI支付需要用户openid")
	}

	return openid, nil
}

// getAppID 根据设备类型获取对应的AppID
// 注意：服务号AppID支持所有支付方式（Native、H5、JSAPI）
// 网站应用AppID主要用于PC端和手机浏览器场景，但不是必需的
func (w *WechatPayClient) getAppID(deviceType DeviceType) string {
	switch deviceType {
	case DeviceTypeWechat:
		// 微信内支付（JSAPI）必须使用服务号AppID
		return w.config.MpAppID
	case DeviceTypeMobile:
		// H5支付：优先使用服务号AppID，网站应用AppID作为备选
		if w.config.MpAppID != "" {
			return w.config.MpAppID
		}
		return w.config.OpenAppID
	case DeviceTypePC:
		// Native扫码支付：优先使用服务号AppID，网站应用AppID作为备选
		// 服务号AppID完全支持Native支付
		if w.config.MpAppID != "" {
			return w.config.MpAppID
		}
		return w.config.OpenAppID
	default:
		// 默认使用服务号AppID（支持所有支付方式）
		return w.config.MpAppID
	}
}

// createNativePayment 创建Native支付（扫码支付）
func (w *WechatPayClient) createNativePayment(paymentReq *PaymentRequest) (string, error) {
	ctx := context.Background()

	// 构建Native支付请求参数
	req := &native.PrepayRequest{
		Appid:       core.String(paymentReq.AppID),
		Mchid:       core.String(paymentReq.MchID),
		Description: core.String(paymentReq.Description),
		OutTradeNo:  core.String(paymentReq.OutTradeNo),
		NotifyUrl:   core.String(paymentReq.NotifyURL),
		Amount: &native.Amount{
			Total:    core.Int64(paymentReq.Amount.Total),
			Currency: core.String(paymentReq.Amount.Currency),
		},
		Detail: &native.Detail{
			GoodsDetail: w.convertToNativeGoodsDetail(paymentReq.Detail.GoodsDetail),
		},
	}

	// 设置过期时间
	if paymentReq.TimeExpire != nil {
		req.TimeExpire = core.Time(*paymentReq.TimeExpire)
	}

	// 调用微信支付API
	resp, result, err := w.nativeAPI.Prepay(ctx, *req)
	if err != nil {
		global.GVA_LOG.Error("微信Native支付下单失败", zap.Error(err))
		return "", fmt.Errorf("微信Native支付下单失败: %v", err)
	}

	if result.Response.StatusCode != 200 {
		return "", fmt.Errorf("微信Native支付下单失败，状态码: %d", result.Response.StatusCode)
	}

	global.GVA_LOG.Info("微信Native支付订单创建成功",
		zap.String("orderNo", paymentReq.OutTradeNo),
		zap.String("appId", paymentReq.AppID),
		zap.String("codeUrl", *resp.CodeUrl))

	// 将CodeUrl转为二维码base64
	qrCodeBase64, err := GenerateWechatPayQRCode(*resp.CodeUrl)
	if err != nil {
		global.GVA_LOG.Error("生成支付二维码失败",
			zap.Error(err),
			zap.String("codeUrl", *resp.CodeUrl))
		// 如果二维码生成失败，仍然返回原始URL，让前端处理
		return *resp.CodeUrl, nil
	}

	global.GVA_LOG.Info("微信支付二维码生成成功",
		zap.String("orderNo", paymentReq.OutTradeNo),
		zap.Int("qrCodeLength", len(qrCodeBase64)))

	// 返回base64格式的二维码数据
	return "data:image/png;base64," + qrCodeBase64, nil
}

// createH5Payment 创建H5支付（手机网站支付）
func (w *WechatPayClient) createH5Payment(paymentReq *PaymentRequest) (string, error) {
	ctx := context.Background()

	// 构建H5支付请求
	req := &h5.PrepayRequest{
		Appid:       core.String(paymentReq.AppID),
		Mchid:       core.String(paymentReq.MchID),
		Description: core.String(paymentReq.Description),
		OutTradeNo:  core.String(paymentReq.OutTradeNo),
		NotifyUrl:   core.String(paymentReq.NotifyURL),
		Amount: &h5.Amount{
			Total:    core.Int64(paymentReq.Amount.Total),
			Currency: core.String(paymentReq.Amount.Currency),
		},
		SceneInfo: &h5.SceneInfo{
			PayerClientIp: core.String(paymentReq.SceneInfo.PayerClientIP),
			H5Info: &h5.H5Info{
				Type: core.String(paymentReq.SceneInfo.H5Info.Type),
			},
		},
		Detail: &h5.Detail{
			GoodsDetail: w.convertToH5GoodsDetail(paymentReq.Detail.GoodsDetail),
		},
	}

	// 设置过期时间
	if paymentReq.TimeExpire != nil {
		req.TimeExpire = core.Time(*paymentReq.TimeExpire)
	}

	// 调用微信支付H5 API
	resp, result, err := w.h5API.Prepay(ctx, *req)
	if err != nil {
		global.GVA_LOG.Error("微信H5支付下单失败", zap.Error(err))
		return "", fmt.Errorf("微信H5支付下单失败: %v", err)
	}

	if result.Response.StatusCode != 200 {
		global.GVA_LOG.Error("微信H5支付下单失败",
			zap.Int("statusCode", result.Response.StatusCode),
			zap.String("orderNo", paymentReq.OutTradeNo))
		return "", fmt.Errorf("微信H5支付下单失败，状态码: %d", result.Response.StatusCode)
	}

	// 检查响应
	if resp.H5Url == nil {
		global.GVA_LOG.Error("微信H5支付返回的URL为空", zap.String("orderNo", paymentReq.OutTradeNo))
		return "", fmt.Errorf("微信H5支付返回的URL为空")
	}

	global.GVA_LOG.Info("微信H5支付订单创建成功",
		zap.String("orderNo", paymentReq.OutTradeNo),
		zap.String("h5Url", *resp.H5Url),
		zap.String("appId", paymentReq.AppID))

	// 返回H5支付跳转URL
	return *resp.H5Url, nil
}

// createJSAPIPayment 创建JSAPI支付（微信内部支付）
func (w *WechatPayClient) createJSAPIPayment(paymentReq *PaymentRequest) (string, error) {
	ctx := context.Background()

	global.GVA_LOG.Info("创建微信JSAPI支付订单",
		zap.String("orderNo", paymentReq.OutTradeNo),
		zap.String("subject", paymentReq.Description),
		zap.Int64("amount", paymentReq.Amount.Total),
		zap.String("openid", paymentReq.Payer.OpenID))

	// 构建JSAPI支付请求
	req := &jsapi.PrepayRequest{
		Appid:       core.String(paymentReq.AppID),
		Mchid:       core.String(paymentReq.MchID),
		Description: core.String(paymentReq.Description),
		OutTradeNo:  core.String(paymentReq.OutTradeNo),
		NotifyUrl:   core.String(paymentReq.NotifyURL),
		Amount: &jsapi.Amount{
			Total:    core.Int64(paymentReq.Amount.Total),
			Currency: core.String(paymentReq.Amount.Currency),
		},
		Payer: &jsapi.Payer{
			Openid: core.String(paymentReq.Payer.OpenID),
		},
		Detail: &jsapi.Detail{
			GoodsDetail: w.convertToJSAPIGoodsDetail(paymentReq.Detail.GoodsDetail),
		},
	}

	// 设置过期时间
	if paymentReq.TimeExpire != nil {
		req.TimeExpire = core.Time(*paymentReq.TimeExpire)
	}

	// 调用微信支付JSAPI API
	resp, result, err := w.jsapiAPI.Prepay(ctx, *req)
	if err != nil {
		global.GVA_LOG.Error("微信JSAPI支付下单失败", zap.Error(err))
		return "", fmt.Errorf("微信JSAPI支付下单失败: %v", err)
	}

	if result.Response.StatusCode != 200 {
		global.GVA_LOG.Error("微信JSAPI支付下单失败",
			zap.Int("statusCode", result.Response.StatusCode),
			zap.String("orderNo", paymentReq.OutTradeNo))
		return "", fmt.Errorf("微信JSAPI支付下单失败，状态码: %d", result.Response.StatusCode)
	}

	// 检查响应
	if resp.PrepayId == nil {
		global.GVA_LOG.Error("微信JSAPI支付返回的PrepayId为空", zap.String("orderNo", paymentReq.OutTradeNo))
		return "", fmt.Errorf("微信JSAPI支付返回的PrepayId为空")
	}

	// 生成JSAPI支付参数（供前端调用微信支付）
	jsapiParams, err := w.buildJSAPIParams(*resp.PrepayId, paymentReq.AppID)
	if err != nil {
		global.GVA_LOG.Error("生成JSAPI支付参数失败", zap.Error(err))
		return "", fmt.Errorf("生成JSAPI支付参数失败: %v", err)
	}

	global.GVA_LOG.Info("微信JSAPI支付订单创建成功",
		zap.String("orderNo", paymentReq.OutTradeNo),
		zap.String("prepayId", *resp.PrepayId),
		zap.String("appId", paymentReq.AppID))

	// 将JSAPI参数序列化为JSON字符串返回
	jsapiJSON, _ := json.Marshal(jsapiParams)
	return string(jsapiJSON), nil
}

// 转换商品详情到不同支付方式的格式
func (w *WechatPayClient) convertToNativeGoodsDetail(goods []PaymentGoodsDetail) []native.GoodsDetail {
	result := make([]native.GoodsDetail, len(goods))
	for i, good := range goods {
		result[i] = native.GoodsDetail{
			MerchantGoodsId: core.String(good.MerchantGoodsID),
			GoodsName:       core.String(good.GoodsName),
			Quantity:        core.Int64(good.Quantity),
			UnitPrice:       core.Int64(good.UnitPrice),
		}
	}
	return result
}

func (w *WechatPayClient) convertToH5GoodsDetail(goods []PaymentGoodsDetail) []h5.GoodsDetail {
	result := make([]h5.GoodsDetail, len(goods))
	for i, good := range goods {
		result[i] = h5.GoodsDetail{
			MerchantGoodsId: core.String(good.MerchantGoodsID),
			GoodsName:       core.String(good.GoodsName),
			Quantity:        core.Int64(good.Quantity),
			UnitPrice:       core.Int64(good.UnitPrice),
		}
	}
	return result
}

func (w *WechatPayClient) convertToJSAPIGoodsDetail(goods []PaymentGoodsDetail) []jsapi.GoodsDetail {
	result := make([]jsapi.GoodsDetail, len(goods))
	for i, good := range goods {
		result[i] = jsapi.GoodsDetail{
			MerchantGoodsId: core.String(good.MerchantGoodsID),
			GoodsName:       core.String(good.GoodsName),
			Quantity:        core.Int64(good.Quantity),
			UnitPrice:       core.Int64(good.UnitPrice),
		}
	}
	return result
}

// buildJSAPIParams 构建JSAPI支付参数
func (w *WechatPayClient) buildJSAPIParams(prepayId string, appID string) (*JSAPIPaymentParams, error) {
	// 生成随机字符串
	nonceStr, err := generateRandomString(32)
	if err != nil {
		return nil, fmt.Errorf("生成随机字符串失败: %v", err)
	}

	// 生成时间戳
	timeStamp := fmt.Sprintf("%d", time.Now().Unix())

	// 构建签名字符串
	signStr := fmt.Sprintf("%s\n%s\n%s\n%s\n", appID, timeStamp, nonceStr, "prepay_id="+prepayId)

	// 使用商户私钥进行签名
	signature, err := utils.SignSHA256WithRSA(signStr, w.privateKey)
	if err != nil {
		return nil, fmt.Errorf("签名失败: %v", err)
	}

	return &JSAPIPaymentParams{
		AppId:     appID,
		TimeStamp: timeStamp,
		NonceStr:  nonceStr,
		Package:   "prepay_id=" + prepayId,
		SignType:  "RSA",
		PaySign:   signature,
	}, nil
}

// generateRandomString 生成指定长度的随机字符串
func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// getValidClientIP 获取有效的客户端IP地址
// 微信支付要求客户端IP不能为空，且需要是有效的公网IP地址
func getValidClientIP(clientIP string) string {
	// 1. 基本检查：如果为空，返回默认IP
	if clientIP == "" {
		global.GVA_LOG.Warn("客户端IP为空，使用默认IP")
		return getDefaultIP()
	}

	// 2. 清理IP地址（去除空格和端口号）
	cleanIP := cleanIPAddress(clientIP)

	// 3. 验证IP地址格式
	if !isValidIPFormat(cleanIP) {
		global.GVA_LOG.Warn("客户端IP格式无效",
			zap.String("originalIP", clientIP),
			zap.String("cleanIP", cleanIP))
		return getDefaultIP()
	}

	// 4. 检查是否为私有IP或保留IP
	if isPrivateOrReservedIP(cleanIP) {
		global.GVA_LOG.Warn("客户端IP为私有或保留地址",
			zap.String("clientIP", cleanIP))
		return getDefaultIP()
	}

	// 5. 验证通过，返回清理后的IP
	global.GVA_LOG.Debug("客户端IP验证通过", zap.String("clientIP", cleanIP))
	return cleanIP
}

// cleanIPAddress 清理IP地址字符串
func cleanIPAddress(ip string) string {
	// 去除首尾空格
	ip = strings.TrimSpace(ip)

	// 如果包含端口号，去除端口号
	if strings.Contains(ip, ":") {
		// 对于IPv6地址，需要特殊处理
		if strings.Count(ip, ":") > 1 {
			// 可能是IPv6地址
			if strings.HasPrefix(ip, "[") && strings.Contains(ip, "]:") {
				// [IPv6]:port 格式
				if idx := strings.LastIndex(ip, "]:"); idx != -1 {
					ip = ip[1:idx] // 去除 [ 和 ]:port
				}
			}
		} else {
			// IPv4:port 格式
			if idx := strings.LastIndex(ip, ":"); idx != -1 {
				ip = ip[:idx]
			}
		}
	}

	return ip
}

// isValidIPFormat 验证IP地址格式
func isValidIPFormat(ip string) bool {
	return net.ParseIP(ip) != nil
}

// isPrivateOrReservedIP 检查是否为私有IP或保留IP
func isPrivateOrReservedIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return true // 无效IP当作私有IP处理
	}

	// 检查IPv4私有和保留地址
	if parsedIP.To4() != nil {
		return isPrivateOrReservedIPv4(parsedIP)
	}

	// 检查IPv6私有和保留地址
	return isPrivateOrReservedIPv6(parsedIP)
}

// isPrivateOrReservedIPv4 检查IPv4私有和保留地址
func isPrivateOrReservedIPv4(ip net.IP) bool {
	// RFC 1918 私有地址范围
	privateRanges := []string{
		"10.0.0.0/8",     // 10.0.0.0 - **************
		"**********/12",  // ********** - **************
		"***********/16", // *********** - ***************
	}

	// 其他保留地址范围
	reservedRanges := []string{
		"0.0.0.0/8",          // 当前网络 (RFC 1122)
		"*********/8",        // 回环地址 (RFC 1122)
		"***********/16",     // 链路本地地址 (RFC 3927)
		"*********/4",        // 多播地址 (RFC 3171)
		"240.0.0.0/4",        // 保留地址 (RFC 1112)
		"***************/32", // 广播地址
	}

	allRanges := append(privateRanges, reservedRanges...)

	for _, cidr := range allRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(ip) {
			return true
		}
	}

	return false
}

// isPrivateOrReservedIPv6 检查IPv6私有和保留地址
func isPrivateOrReservedIPv6(ip net.IP) bool {
	// IPv6保留地址范围
	reservedRanges := []string{
		"::1/128",       // 回环地址
		"::/128",        // 未指定地址
		"::ffff:0:0/96", // IPv4映射地址
		"fe80::/10",     // 链路本地地址
		"fc00::/7",      // 唯一本地地址 (ULA)
		"ff00::/8",      // 多播地址
	}

	for _, cidr := range reservedRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(ip) {
			return true
		}
	}

	return false
}

// getDefaultIP 获取默认IP地址
func getDefaultIP() string {
	// 生产环境建议使用实际的公网IP
	// 这里提供一些常用的公网IP作为备选
	defaultIPs := []string{
		"*******",         // Google DNS
		"*******",         // Cloudflare DNS
		"***************", // 114 DNS
		"*********",       // 阿里DNS
	}

	// 可以根据地区或其他策略选择默认IP
	// 这里简单返回第一个
	defaultIP := defaultIPs[0]

	global.GVA_LOG.Info("使用默认IP地址", zap.String("defaultIP", defaultIP))
	return defaultIP
}

// GetClientIPFromRequest 从HTTP请求中获取客户端真实IP
// 这是一个辅助函数，可以在API层使用来获取更准确的客户端IP
func GetClientIPFromRequest(req *http.Request) string {
	// 按优先级检查各种可能包含真实IP的请求头
	headers := []string{
		"X-Forwarded-For",
		"X-Real-IP",
		"X-Client-IP",
		"X-Forwarded",
		"X-Cluster-Client-IP",
		"Forwarded-For",
		"Forwarded",
	}

	for _, header := range headers {
		ip := req.Header.Get(header)
		if ip != "" {
			// X-Forwarded-For可能包含多个IP，取第一个
			if header == "X-Forwarded-For" {
				ips := strings.Split(ip, ",")
				for _, singleIP := range ips {
					cleanIP := strings.TrimSpace(singleIP)
					if cleanIP != "" && isValidIPFormat(cleanIP) && !isPrivateOrReservedIP(cleanIP) {
						global.GVA_LOG.Debug("从请求头获取客户端IP",
							zap.String("header", header),
							zap.String("ip", cleanIP))
						return cleanIP
					}
				}
			} else {
				cleanIP := strings.TrimSpace(ip)
				if isValidIPFormat(cleanIP) && !isPrivateOrReservedIP(cleanIP) {
					global.GVA_LOG.Debug("从请求头获取客户端IP",
						zap.String("header", header),
						zap.String("ip", cleanIP))
					return cleanIP
				}
			}
		}
	}

	// 如果请求头中没有找到有效IP，使用RemoteAddr
	if req.RemoteAddr != "" {
		ip := cleanIPAddress(req.RemoteAddr)
		if isValidIPFormat(ip) && !isPrivateOrReservedIP(ip) {
			global.GVA_LOG.Debug("从RemoteAddr获取客户端IP", zap.String("ip", ip))
			return ip
		}
	}

	// 如果都没有找到有效IP，返回默认IP
	global.GVA_LOG.Warn("无法获取有效的客户端IP，使用默认IP")
	return getDefaultIP()
}

// QueryPayment 查询支付状态
func (w *WechatPayClient) QueryPayment(orderNo string) (*PaymentQueryResult, error) {
	ctx := context.Background()

	// 构建查询请求
	req := &native.QueryOrderByOutTradeNoRequest{
		OutTradeNo: core.String(orderNo),
		Mchid:      core.String(w.config.MchID),
	}

	// 调用微信支付查询API
	resp, result, err := w.nativeAPI.QueryOrderByOutTradeNo(ctx, *req)
	if err != nil {
		global.GVA_LOG.Error("微信支付查询失败", zap.Error(err))
		return nil, fmt.Errorf("微信支付查询失败: %v", err)
	}

	if result.Response.StatusCode != 200 {
		return nil, fmt.Errorf("微信支付查询失败，状态码: %d", result.Response.StatusCode)
	}

	// 转换支付状态
	var status payment.PaymentStatus
	switch *resp.TradeState {
	case "SUCCESS":
		status = payment.PaymentStatusPaid
	case "REFUND":
		status = payment.PaymentStatusRefunded
	case "NOTPAY":
		status = payment.PaymentStatusPending
	case "CLOSED":
		status = payment.PaymentStatusCancelled
	case "REVOKED":
		status = payment.PaymentStatusCancelled
	case "USERPAYING":
		status = payment.PaymentStatusPending
	case "PAYERROR":
		status = payment.PaymentStatusFailed
	default:
		status = payment.PaymentStatusPending
	}

	// 构建查询结果
	queryResult := &PaymentQueryResult{
		OrderNo:      orderNo,
		ThirdOrderNo: *resp.TransactionId,
		Status:       status,
		Amount:       int64(*resp.Amount.Total),
	}

	// 解析支付时间
	if resp.SuccessTime != nil {
		payTime, err := time.Parse(time.RFC3339, *resp.SuccessTime)
		if err == nil {
			queryResult.PayTime = &payTime
		}
	}

	// 序列化第三方响应
	thirdResponse, _ := json.Marshal(resp)
	queryResult.ThirdResponse = string(thirdResponse)

	return queryResult, nil
}

// RefundPayment 退款
func (w *WechatPayClient) RefundPayment(refundOrder *payment.PaymentRefund) (*RefundResult, error) {
	// TODO: 完整的退款实现需要额外的微信支付退款模块
	// 当前返回简化实现，标记为处理中状态
	global.GVA_LOG.Info("微信支付退款请求",
		zap.String("orderNo", refundOrder.OrderNo),
		zap.String("refundNo", refundOrder.RefundNo),
		zap.Int64("refundAmount", refundOrder.RefundAmount),
		zap.String("reason", refundOrder.RefundReason))

	// 实际生产环境中，这里需要调用微信支付的退款API
	// 由于退款模块的复杂性，暂时返回待处理状态
	return &RefundResult{
		RefundNo:      refundOrder.RefundNo,
		ThirdRefundNo: "wx_refund_" + refundOrder.RefundNo,
		Status:        RefundStatusPending,
		RefundAmount:  refundOrder.RefundAmount,
		RefundTime:    time.Now(),
	}, nil
}

// VerifyNotifyWithRequest 验证异步通知（带完整HTTP请求信息）
// 注意：这个方法需要传入完整的HTTP请求来进行签名验证
func (w *WechatPayClient) VerifyNotifyWithRequest(req *http.Request) (*NotifyResult, error) {
	// 读取请求体
	body := make([]byte, req.ContentLength)
	_, err := req.Body.Read(body)
	if err != nil {
		return nil, fmt.Errorf("读取请求体失败: %v", err)
	}

	global.GVA_LOG.Info("微信支付通知HTTP请求",
		zap.String("method", req.Method),
		zap.String("url", req.URL.String()),
		zap.Any("headers", req.Header),
		zap.String("body", string(body)))

	// TODO: 实现完整的签名验证
	// 暂时使用简化版本
	return w.VerifyNotify(body)
}

// VerifyNotify 验证异步通知（兼容原接口）
func (w *WechatPayClient) VerifyNotify(notifyData []byte) (*NotifyResult, error) {
	// 记录原始通知数据
	global.GVA_LOG.Info("微信支付通知数据", zap.String("data", string(notifyData)))

	// 解析通知数据
	var notifyReq map[string]interface{}
	if err := json.Unmarshal(notifyData, &notifyReq); err != nil {
		global.GVA_LOG.Error("解析微信通知数据失败", zap.Error(err))
		return nil, fmt.Errorf("解析通知数据失败: %v", err)
	}

	// 记录解析后的数据结构
	global.GVA_LOG.Info("微信支付通知解析结果", zap.Any("notify", notifyReq))

	// 验证event_type
	eventType, ok := notifyReq["event_type"].(string)
	if !ok {
		return nil, fmt.Errorf("通知数据中缺少event_type字段")
	}

	global.GVA_LOG.Info("微信支付通知事件类型", zap.String("eventType", eventType))

	// 只处理支付成功通知
	if eventType != "TRANSACTION.SUCCESS" {
		return nil, fmt.Errorf("非支付成功通知，事件类型: %s", eventType)
	}

	// 获取resource字段
	resource, ok := notifyReq["resource"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("通知数据格式错误，缺少resource字段")
	}

	// 处理加密数据
	var transactionData map[string]interface{}

	// 首先尝试获取plaintext（测试环境可能有）
	if plaintext, ok := resource["plaintext"].(string); ok {
		global.GVA_LOG.Info("微信支付通知包含明文数据")
		if err := json.Unmarshal([]byte(plaintext), &transactionData); err != nil {
			return nil, fmt.Errorf("解析明文支付数据失败: %v", err)
		}
	} else {
		// 生产环境的加密数据处理
		ciphertext, ok := resource["ciphertext"].(string)
		if !ok {
			return nil, fmt.Errorf("通知数据中缺少ciphertext字段")
		}

		// 获取加密相关参数
		algorithm, _ := resource["algorithm"].(string)
		nonce, _ := resource["nonce"].(string)
		associatedData, _ := resource["associated_data"].(string)

		global.GVA_LOG.Info("微信支付通知加密信息",
			zap.String("algorithm", algorithm),
			zap.String("nonce", nonce),
			zap.String("associatedData", associatedData))

		// 使用微信支付SDK解密
		// 注意：这里需要根据实际的SDK版本调整方法名
		plaintext, err := utils.DecryptAES256GCM(w.config.APIv3Key, associatedData, nonce, ciphertext)
		if err != nil {
			global.GVA_LOG.Error("微信支付通知解密失败", zap.Error(err))
			return nil, fmt.Errorf("通知数据解密失败: %v", err)
		}

		global.GVA_LOG.Info("微信支付通知解密成功")
		if err := json.Unmarshal([]byte(plaintext), &transactionData); err != nil {
			return nil, fmt.Errorf("解析解密后支付数据失败: %v", err)
		}
	}

	// 验证必要字段
	orderNo, ok := transactionData["out_trade_no"].(string)
	if !ok || orderNo == "" {
		return nil, fmt.Errorf("通知数据中缺少订单号")
	}

	thirdOrderNo, ok := transactionData["transaction_id"].(string)
	if !ok || thirdOrderNo == "" {
		return nil, fmt.Errorf("通知数据中缺少微信交易号")
	}

	tradeState, ok := transactionData["trade_state"].(string)
	if !ok || tradeState == "" {
		return nil, fmt.Errorf("通知数据中缺少交易状态")
	}

	global.GVA_LOG.Info("微信支付通知关键信息",
		zap.String("orderNo", orderNo),
		zap.String("transactionId", thirdOrderNo),
		zap.String("tradeState", tradeState))

	// 转换支付状态
	var status payment.PaymentStatus
	switch tradeState {
	case "SUCCESS":
		status = payment.PaymentStatusPaid
	case "REFUND":
		status = payment.PaymentStatusRefunded
	case "NOTPAY":
		status = payment.PaymentStatusPending
	case "CLOSED":
		status = payment.PaymentStatusCancelled
	case "REVOKED":
		status = payment.PaymentStatusCancelled
	case "USERPAYING":
		status = payment.PaymentStatusPending
	case "PAYERROR":
		status = payment.PaymentStatusFailed
	default:
		status = payment.PaymentStatusPending
	}

	// 解析金额
	var totalAmount int64
	if amount, ok := transactionData["amount"].(map[string]interface{}); ok {
		if total, ok := amount["total"].(float64); ok {
			totalAmount = int64(total)
		}
	}

	// 解析支付时间
	var payTime *time.Time
	if successTime, ok := transactionData["success_time"].(string); ok {
		t, err := time.Parse(time.RFC3339, successTime)
		if err == nil {
			payTime = &t
		}
	}

	global.GVA_LOG.Info("微信支付通知验证成功",
		zap.String("orderNo", orderNo),
		zap.String("transactionId", thirdOrderNo),
		zap.String("status", tradeState),
		zap.Int64("amount", totalAmount))

	return &NotifyResult{
		OrderNo:      orderNo,
		ThirdOrderNo: thirdOrderNo,
		Status:       status,
		Amount:       totalAmount,
		PayTime:      payTime,
	}, nil
}

// ValidateConfig 验证微信支付配置
func (w *WechatPayClient) ValidateConfig() error {
	if w.config.MpAppID == "" && w.config.OpenAppID == "" {
		return fmt.Errorf("微信支付AppID不能为空，至少需要配置服务号应用ID或网站应用ID\n" +
			"注意：服务号AppID支持所有支付方式（Native扫码、H5、JSAPI），网站应用AppID主要用于PC和手机浏览器场景")
	}
	if w.config.MchID == "" {
		return fmt.Errorf("微信支付商户号不能为空")
	}
	if w.config.MchCertificateSerialNumber == "" {
		return fmt.Errorf("微信支付商户证书序列号不能为空")
	}
	if w.config.APIv3Key == "" {
		return fmt.Errorf("微信支付APIv3密钥不能为空")
	}
	if w.config.NotifyUrl == "" {
		return fmt.Errorf("微信支付通知URL不能为空")
	}
	return nil
}

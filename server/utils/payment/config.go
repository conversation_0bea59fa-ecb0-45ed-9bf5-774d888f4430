package payment

// WechatConfig 微信支付配置
// 根据微信支付官方Go SDK文档配置参数
type WechatConfig struct {
	MpAppID                    string `json:"mpAppId"`                    // 微信服务号应用ID
	OpenAppID                  string `json:"openAppId"`                  // 微信网站应用ID
	MchID                      string `json:"mchId"`                      // 微信支付商户号
	MchCertificateSerialNumber string `json:"mchCertificateSerialNumber"` // 商户API证书序列号
	APIv3Key                   string `json:"apiv3Key"`                   // 微信支付APIv3密钥（32位字符串）
	PrivateKey                 string `json:"privateKey"`                 // 商户API私钥内容（PEM格式字符串，可替代keyPath）
	NotifyUrl                  string `json:"notifyUrl"`                  // 支付结果异步通知地址
	// H5支付专用配置
	H5Domain    string `json:"h5Domain"`    // H5支付授权域名（需在微信商户平台配置）
	H5ReturnUrl string `json:"h5ReturnUrl"` // H5支付完成后返回地址
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID        string `json:"appId"`        // 应用ID
	PrivateKey   string `json:"privateKey"`   // 应用私钥
	PublicKey    string `json:"publicKey"`    // 支付宝公钥
	IsProduction bool   `json:"isProduction"` // 是否生产环境
	NotifyUrl    string `json:"notifyUrl"`    // 异步通知地址
	ReturnUrl    string `json:"returnUrl"`    // 同步返回地址
	SignType     string `json:"signType"`     // 签名类型，默认RSA2
}

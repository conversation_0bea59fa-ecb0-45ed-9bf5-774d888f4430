package payment

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/payment"
)

// PaymentInterface 支付接口
type PaymentInterface interface {
	// CreatePayment 创建支付订单
	CreatePayment(order *payment.PaymentOrder) (string, error)

	// QueryPayment 查询支付状态
	QueryPayment(orderNo string) (*PaymentQueryResult, error)

	// RefundPayment 退款
	RefundPayment(refund *payment.PaymentRefund) (*RefundResult, error)

	// VerifyNotify 验证异步通知
	VerifyNotify(notifyData []byte) (*NotifyResult, error)
}

// PaymentQueryResult 支付查询结果
type PaymentQueryResult struct {
	OrderNo       string                `json:"orderNo"`       // 订单号
	ThirdOrderNo  string                `json:"thirdOrderNo"`  // 第三方订单号
	Status        payment.PaymentStatus `json:"status"`        // 支付状态
	Amount        int64                 `json:"amount"`        // 支付金额(分)
	PayTime       *time.Time            `json:"payTime"`       // 支付时间
	ThirdResponse string                `json:"thirdResponse"` // 第三方响应
}

// RefundResult 退款结果
type RefundResult struct {
	RefundNo      string       `json:"refundNo"`      // 退款单号
	ThirdRefundNo string       `json:"thirdRefundNo"` // 第三方退款单号
	Status        RefundStatus `json:"status"`        // 退款状态
	RefundAmount  int64        `json:"refundAmount"`  // 退款金额(分)
	RefundTime    time.Time    `json:"refundTime"`    // 退款时间
}

// NotifyResult 异步通知结果
type NotifyResult struct {
	OrderNo      string                `json:"orderNo"`      // 订单号
	ThirdOrderNo string                `json:"thirdOrderNo"` // 第三方订单号
	Status       payment.PaymentStatus `json:"status"`       // 支付状态
	Amount       int64                 `json:"amount"`       // 支付金额(分)
	PayTime      *time.Time            `json:"payTime"`      // 支付时间
}

// RefundStatus 退款状态
type RefundStatus int

const (
	RefundStatusPending   RefundStatus = 1 // 退款中
	RefundStatusSuccess   RefundStatus = 2 // 退款成功
	RefundStatusFailed    RefundStatus = 3 // 退款失败
	RefundStatusCancelled RefundStatus = 4 // 已取消
)

// DeviceType 设备类型
type DeviceType string

const (
	DeviceTypePC     DeviceType = "pc"     // 电脑网站
	DeviceTypeMobile DeviceType = "mobile" // 手机网站
	DeviceTypeApp    DeviceType = "app"    // 手机应用
	DeviceTypeWechat DeviceType = "wechat" // 微信内部（微信浏览器、小程序等）
)

// CreatePaymentRequest 创建支付请求
type CreatePaymentRequest struct {
	UserID        uint                   `json:"userId" binding:"required"`
	Amount        int64                  `json:"amount" binding:"required,min=1"`
	Subject       string                 `json:"subject" binding:"required"`
	Body          string                 `json:"body"`
	PaymentMethod payment.PaymentMethod  `json:"paymentMethod" binding:"required"`
	DeviceType    DeviceType             `json:"deviceType" binding:"required"` // 设备类型
	OpenID        string                 `json:"openId"`                        // 微信用户openid（JSAPI支付必需）
	NotifyUrl     string                 `json:"notifyUrl"`
	ReturnUrl     string                 `json:"returnUrl"`
	ExpireMinutes int                    `json:"expireMinutes" binding:"min=1,max=1440"` // 过期时间（分钟）
	ClientIP      string                 `json:"clientIp"`
	Extra         map[string]interface{} `json:"extra"`
}

// CreatePaymentResponse 创建支付响应
type CreatePaymentResponse struct {
	OrderNo    string `json:"orderNo"`    // 订单号
	PaymentURL string `json:"paymentUrl"` // 支付URL/二维码
	ExpireTime string `json:"expireTime"` // 过期时间
	// JSAPI支付专用字段
	JSAPIParams *JSAPIPaymentParams `json:"jsapiParams,omitempty"` // JSAPI支付参数
}

// JSAPIPaymentParams JSAPI支付参数
type JSAPIPaymentParams struct {
	AppId     string `json:"appId"`     // 应用ID
	TimeStamp string `json:"timeStamp"` // 时间戳
	NonceStr  string `json:"nonceStr"`  // 随机字符串
	Package   string `json:"package"`   // 订单详情扩展字符串
	SignType  string `json:"signType"`  // 签名方式
	PaySign   string `json:"paySign"`   // 签名
}

// RefundRequest 退款请求
type RefundRequest struct {
	OrderNo      string `json:"orderNo" binding:"required"`
	RefundAmount int64  `json:"refundAmount" binding:"required,min=1"`
	RefundReason string `json:"refundReason" binding:"required"`
	Remark       string `json:"remark"`
}

package payment

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/payment"
	"github.com/smartwalle/alipay/v3"
	"go.uber.org/zap"
)

// AlipayClient 支付宝支付客户端
type AlipayClient struct {
	config AlipayConfig
	client *alipay.Client
}

// NewAlipayClient 创建支付宝支付客户端
func NewAlipayClient(config AlipayConfig) (*AlipayClient, error) {
	// 初始化支付宝客户端
	var client *alipay.Client
	var err error

	if config.IsProduction {
		// 生产环境
		client, err = alipay.New(config.AppID, config.PrivateKey, config.IsProduction)
	} else {
		// 沙箱环境
		client, err = alipay.New(config.AppID, config.PrivateKey, config.IsProduction)
	}

	if err != nil {
		return nil, fmt.Errorf("初始化支付宝客户端失败: %v", err)
	}

	// 设置支付宝公钥
	err = client.LoadAliPayPublicKey(config.PublicKey)
	if err != nil {
		return nil, fmt.Errorf("加载支付宝公钥失败: %v", err)
	}

	return &AlipayClient{
		config: config,
		client: client,
	}, nil
}

// CreatePayment 创建支付订单
func (a *AlipayClient) CreatePayment(order *payment.PaymentOrder) (string, error) {
	// 从订单的Extra字段中获取设备类型
	deviceType := DeviceTypePC // 默认为PC
	if order.Extra != nil {
		if dt, ok := order.Extra["deviceType"].(string); ok {
			deviceType = DeviceType(dt)
		}
	}

	// 根据设备类型选择不同的支付接口
	switch deviceType {
	case DeviceTypeMobile:
		return a.createWapPayment(order)
	case DeviceTypePC:
		return a.createPagePayment(order)
	default:
		return a.createPagePayment(order) // 默认使用电脑网站支付
	}
}

// createPagePayment 创建电脑网站支付
func (a *AlipayClient) createPagePayment(order *payment.PaymentOrder) (string, error) {
	// 构建统一收单下单并支付页面接口请求
	var req = alipay.TradePagePay{
		Trade: alipay.Trade{
			NotifyURL:      a.config.NotifyUrl,
			ReturnURL:      a.config.ReturnUrl,
			Subject:        order.Subject,
			OutTradeNo:     order.OrderNo,
			TotalAmount:    fmt.Sprintf("%.2f", float64(order.Amount)/100), // 转换为元
			ProductCode:    "FAST_INSTANT_TRADE_PAY",                       // 电脑网站支付产品码
			TimeoutExpress: "30m",
		},
	}

	// 如果有商品描述，添加到body字段
	if order.Body != "" {
		req.Body = order.Body
	}

	// 调用支付宝API - TradePagePay 返回支付页面URL
	payURL, err := a.client.TradePagePay(req)
	if err != nil {
		global.GVA_LOG.Error("支付宝电脑网站支付下单失败", zap.Error(err))
		return "", fmt.Errorf("支付宝电脑网站支付下单失败: %v", err)
	}

	global.GVA_LOG.Info("支付宝电脑网站支付订单创建成功",
		zap.String("orderNo", order.OrderNo))

	return payURL.String(), nil
}

// createWapPayment 创建手机网站支付
func (a *AlipayClient) createWapPayment(order *payment.PaymentOrder) (string, error) {
	// 构建手机网站支付接口请求
	var req = alipay.TradeWapPay{
		Trade: alipay.Trade{
			NotifyURL:      a.config.NotifyUrl,
			ReturnURL:      a.config.ReturnUrl,
			Subject:        order.Subject,
			OutTradeNo:     order.OrderNo,
			TotalAmount:    fmt.Sprintf("%.2f", float64(order.Amount)/100), // 转换为元
			ProductCode:    "QUICK_WAP_WAY",                                // 手机网站支付产品码
			TimeoutExpress: "30m",
		},
	}

	// 如果有商品描述，添加到body字段
	if order.Body != "" {
		req.Body = order.Body
	}

	// 可选：设置手机网站支付特有参数
	// req.QuitUrl = "https://yoursite.com/quit" // 用户付款中途退出返回商户网站的地址

	// 调用支付宝API - TradeWapPay 返回支付页面URL
	payURL, err := a.client.TradeWapPay(req)
	if err != nil {
		global.GVA_LOG.Error("支付宝手机网站支付下单失败", zap.Error(err))
		return "", fmt.Errorf("支付宝手机网站支付下单失败: %v", err)
	}

	global.GVA_LOG.Info("支付宝手机网站支付订单创建成功",
		zap.String("orderNo", order.OrderNo))

	return payURL.String(), nil
}

// QueryPayment 查询支付状态
func (a *AlipayClient) QueryPayment(orderNo string) (*PaymentQueryResult, error) {
	ctx := context.Background()

	// 构建交易查询请求
	var req = alipay.TradeQuery{
		OutTradeNo: orderNo,
	}

	// 调用支付宝查询API
	resp, err := a.client.TradeQuery(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("支付宝查询失败", zap.Error(err))
		return nil, fmt.Errorf("支付宝查询失败: %v", err)
	}

	// 检查响应
	if resp.Code != alipay.CodeSuccess {
		return nil, fmt.Errorf("支付宝查询失败: %s - %s", resp.Code, resp.Msg)
	}

	// 转换支付状态
	var status payment.PaymentStatus
	switch resp.TradeStatus {
	case "TRADE_SUCCESS":
		status = payment.PaymentStatusPaid
	case "TRADE_FINISHED":
		status = payment.PaymentStatusPaid
	case "WAIT_BUYER_PAY":
		status = payment.PaymentStatusPending
	case "TRADE_CLOSED":
		status = payment.PaymentStatusCancelled
	default:
		status = payment.PaymentStatusPending
	}

	// 解析支付金额（从元转换为分）
	totalAmount, _ := strconv.ParseFloat(resp.TotalAmount, 64)
	amountCent := int64(totalAmount * 100)

	// 构建查询结果
	queryResult := &PaymentQueryResult{
		OrderNo:      orderNo,
		ThirdOrderNo: resp.TradeNo,
		Status:       status,
		Amount:       amountCent,
	}

	// 解析支付时间 - 使用实际存在的字段
	if resp.SendPayDate != "" {
		payTime, err := time.Parse("2006-01-02 15:04:05", resp.SendPayDate)
		if err == nil {
			queryResult.PayTime = &payTime
		}
	}

	// 序列化第三方响应
	thirdResponse, _ := json.Marshal(resp)
	queryResult.ThirdResponse = string(thirdResponse)

	return queryResult, nil
}

// RefundPayment 退款
func (a *AlipayClient) RefundPayment(refundOrder *payment.PaymentRefund) (*RefundResult, error) {
	ctx := context.Background()

	// 构建退款请求
	var req = alipay.TradeRefund{
		OutTradeNo:   refundOrder.OrderNo,
		OutRequestNo: refundOrder.RefundNo,
		RefundAmount: fmt.Sprintf("%.2f", float64(refundOrder.RefundAmount)/100), // 转换为元
		RefundReason: refundOrder.RefundReason,
	}

	// 调用支付宝退款API
	resp, err := a.client.TradeRefund(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("支付宝退款失败", zap.Error(err))
		return nil, fmt.Errorf("支付宝退款失败: %v", err)
	}

	// 检查响应
	if resp.Code != alipay.CodeSuccess {
		return nil, fmt.Errorf("支付宝退款失败: %s - %s", resp.Code, resp.Msg)
	}

	// 支付宝退款通常是同步返回结果
	var status RefundStatus
	if resp.FundChange == "Y" {
		status = RefundStatusSuccess
	} else {
		status = RefundStatusFailed
	}

	// 解析退款时间 - 使用当前时间
	refundTime := time.Now()

	return &RefundResult{
		RefundNo:      refundOrder.RefundNo,
		ThirdRefundNo: resp.TradeNo,
		Status:        status,
		RefundAmount:  refundOrder.RefundAmount,
		RefundTime:    refundTime,
	}, nil
}

// VerifyNotify 验证异步通知
func (a *AlipayClient) VerifyNotify(notifyData []byte) (*NotifyResult, error) {
	// 解析通知参数 - 这里接收的是已经转换为JSON的map数据
	var notifyParams map[string]string
	if err := json.Unmarshal(notifyData, &notifyParams); err != nil {
		return nil, fmt.Errorf("解析通知数据失败: %v", err)
	}

	// 记录通知参数用于调试
	global.GVA_LOG.Info("支付宝通知参数", zap.Any("params", notifyParams))

	// 转换为url.Values格式进行签名验证
	values := url.Values{}
	for k, v := range notifyParams {
		values.Set(k, v)
	}

	// 验证签名
	err := a.client.VerifySign(values)
	if err != nil {
		global.GVA_LOG.Error("支付宝签名验证失败",
			zap.Error(err),
			zap.Any("params", notifyParams))
		return nil, fmt.Errorf("签名验证失败: %v", err)
	}

	// 提取关键信息
	orderNo := notifyParams["out_trade_no"]
	thirdOrderNo := notifyParams["trade_no"]
	tradeStatus := notifyParams["trade_status"]

	// 验证必要参数
	if orderNo == "" {
		return nil, fmt.Errorf("订单号为空")
	}
	if thirdOrderNo == "" {
		return nil, fmt.Errorf("支付宝交易号为空")
	}
	if tradeStatus == "" {
		return nil, fmt.Errorf("交易状态为空")
	}

	// 转换支付状态
	var status payment.PaymentStatus
	switch tradeStatus {
	case "TRADE_SUCCESS", "TRADE_FINISHED":
		status = payment.PaymentStatusPaid
	case "WAIT_BUYER_PAY":
		status = payment.PaymentStatusPending
	case "TRADE_CLOSED":
		status = payment.PaymentStatusCancelled
	default:
		status = payment.PaymentStatusPending
	}

	// 解析金额
	totalAmount, _ := strconv.ParseFloat(notifyParams["total_amount"], 64)
	amountCent := int64(totalAmount * 100)

	// 解析支付时间
	var payTime *time.Time
	if gmtPayment := notifyParams["gmt_payment"]; gmtPayment != "" {
		t, err := time.Parse("2006-01-02 15:04:05", gmtPayment)
		if err == nil {
			payTime = &t
		}
	}

	global.GVA_LOG.Info("支付宝通知验证成功",
		zap.String("orderNo", orderNo),
		zap.String("tradeNo", thirdOrderNo),
		zap.String("status", tradeStatus))

	return &NotifyResult{
		OrderNo:      orderNo,
		ThirdOrderNo: thirdOrderNo,
		Status:       status,
		Amount:       amountCent,
		PayTime:      payTime,
	}, nil
}

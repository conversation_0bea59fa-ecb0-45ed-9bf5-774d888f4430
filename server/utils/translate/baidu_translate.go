package translate

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type BaiduTranslateResponse struct {
	From        string `json:"from"`
	To          string `json:"to"`
	TransResult []struct {
		Src string `json:"src"`
		Dst string `json:"dst"`
	} `json:"trans_result"`
	ErrorCode string `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
}

// BaiduTranslate 调用百度翻译API
func BaiduTranslate(query, from, to string) (*BaiduTranslateResponse, error) {
	cfg := global.GVA_CONFIG.BaiduTranslate
	appid := cfg.AppID
	key := cfg.Key
	if appid == "" || key == "" {
		return nil, fmt.Errorf("Baidu Translate appid or key not configured")
	}

	salt := strconv.FormatInt(time.Now().UnixNano()+int64(rand.Intn(10000)), 10)
	signStr := appid + query + salt + key
	h := md5.New()
	h.Write([]byte(signStr))
	sign := hex.EncodeToString(h.Sum(nil))

	url := "https://fanyi-api.baidu.com/api/trans/vip/translate"
	params := fmt.Sprintf("q=%s&from=%s&to=%s&appid=%s&salt=%s&sign=%s",
		urlQueryEscape(query), from, to, appid, salt, sign)
	resp, err := http.Get(url + "?" + params)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result BaiduTranslateResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	if result.ErrorCode != "" {
		return &result, fmt.Errorf("Baidu Translate error: %s %s", result.ErrorCode, result.ErrorMsg)
	}
	if len(result.TransResult) == 0 {
		return &result, fmt.Errorf("No translation result returned")
	}
	return &result, nil
}

// urlQueryEscape escapes a string for use in a URL query
func urlQueryEscape(s string) string {
	return url.QueryEscape(s)
}

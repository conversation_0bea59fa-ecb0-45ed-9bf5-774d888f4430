package pool

import (
	"sync"

	"github.com/flipped-aurora/gin-vue-admin/server/model/pansou"
)

// LinkPool 网盘链接对象池
var LinkPool = sync.Pool{
	New: func() interface{} {
		return &pansou.Link{}
	},
}

// SearchResultPool 搜索结果对象池
var SearchResultPool = sync.Pool{
	New: func() interface{} {
		return &pansou.SearchResult{
			Links: make([]pansou.Link, 0, 4),
			Tags:  make([]string, 0, 8),
		}
	},
}

// MergedLinkPool 合并链接对象池
var MergedLinkPool = sync.Pool{
	New: func() interface{} {
		return &pansou.MergedLink{}
	},
}

// GetLink 从对象池获取Link对象
func GetLink() *pansou.Link {
	return LinkPool.Get().(*pansou.Link)
}

// ReleaseLink 释放Link对象回对象池
func ReleaseLink(l *pansou.Link) {
	l.Type = ""
	l.URL = ""
	l.Password = ""
	LinkPool.Put(l)
}

// GetSearchResult 从对象池获取SearchResult对象
func GetSearchResult() *pansou.SearchResult {
	return SearchResultPool.Get().(*pansou.SearchResult)
}

// ReleaseSearchResult 释放SearchResult对象回对象池
func ReleaseSearchResult(sr *pansou.SearchResult) {
	sr.MessageID = ""
	sr.Channel = ""
	sr.Title = ""
	sr.Content = ""
	sr.Links = sr.Links[:0]
	sr.Tags = sr.Tags[:0]
	// 不重置时间，因为会被重新赋值
	SearchResultPool.Put(sr)
}

// GetMergedLink 从对象池获取MergedLink对象
func GetMergedLink() *pansou.MergedLink {
	return MergedLinkPool.Get().(*pansou.MergedLink)
}

// ReleaseMergedLink 释放MergedLink对象回对象池
func ReleaseMergedLink(ml *pansou.MergedLink) {
	ml.URL = ""
	ml.Password = ""
	ml.Note = ""
	// 不重置时间，因为会被重新赋值
	MergedLinkPool.Put(ml)
}

package cache

// ConfigAdapter 配置适配器，用于在不同配置系统之间转换
type ConfigAdapter struct {
	cachePath      string
	cacheMaxSizeMB int
}

// NewConfigAdapter 创建配置适配器
func NewConfigAdapter(cachePath string, cacheMaxSizeMB int) *ConfigAdapter {
	return &ConfigAdapter{
		cachePath:      cachePath,
		cacheMaxSizeMB: cacheMaxSizeMB,
	}
}

// GetCachePath 获取缓存路径
func (c *ConfigAdapter) GetCachePath() string {
	return c.cachePath
}

// GetCacheMaxSizeMB 获取缓存最大大小
func (c *ConfigAdapter) GetCacheMaxSizeMB() int {
	return c.cacheMaxSizeMB
}

// SetCachePath 设置缓存路径
func (c *ConfigAdapter) SetCachePath(path string) {
	c.cachePath = path
}

// SetCacheMaxSizeMB 设置缓存最大大小
func (c *ConfigAdapter) SetCacheMaxSizeMB(sizeMB int) {
	c.cacheMaxSizeMB = sizeMB
}

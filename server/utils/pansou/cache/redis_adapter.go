package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/pansou"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	// PanSouCachePrefix PanSou缓存键前缀
	PanSouCachePrefix = "pansou:kw:"
	// PanSouMetaPrefix PanSou元数据缓存键前缀
	PanSouMetaPrefix = "pansou:meta:"
	// PanSouStatsPrefix PanSou统计缓存键前缀
	PanSouStatsPrefix = "pansou:stats:"
	// DefaultTTL 默认缓存过期时间
	DefaultTTL = 30 * time.Minute
)

// PanSouRedisCache PanSou Redis缓存适配器
type PanSouRedisCache struct {
	client     redis.UniversalClient
	ctx        context.Context
	serializer Serializer
}

// NewPanSouRedisCache 创建PanSou Redis缓存实例
func NewPanSouRedisCache() (*PanSouRedisCache, error) {
	if global.GVA_REDIS == nil {
		return nil, fmt.Errorf("全局Redis客户端未初始化")
	}

	// 测试Redis连接
	ctx := context.Background()
	if err := global.GVA_REDIS.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("Redis连接测试失败: %v", err)
	}

	return &PanSouRedisCache{
		client:     global.GVA_REDIS,
		ctx:        ctx,
		serializer: NewJSONSerializer(), // 使用JSON序列化，便于调试和跨语言兼容
	}, nil
}

// buildCacheKey 构建缓存键
func (p *PanSouRedisCache) buildCacheKey(key string) string {
	return PanSouCachePrefix + key
}

// buildMetaKey 构建元数据键
func (p *PanSouRedisCache) buildMetaKey(key string) string {
	return PanSouMetaPrefix + key
}

// Set 设置缓存
func (p *PanSouRedisCache) Set(key string, data []byte, ttl time.Duration) error {
	if ttl <= 0 {
		ttl = DefaultTTL
	}

	redisKey := p.buildCacheKey(key)
	err := p.client.Set(p.ctx, redisKey, data, ttl).Err()
	if err != nil {
		global.GVA_LOG.Error("PanSou Redis缓存设置失败",
			zap.String("key", redisKey),
			zap.Error(err))
		return err
	}

	global.GVA_LOG.Debug("PanSou Redis缓存设置成功",
		zap.String("key", redisKey),
		zap.Duration("ttl", ttl))
	return nil
}

// Get 获取缓存
func (p *PanSouRedisCache) Get(key string) ([]byte, bool, error) {
	redisKey := p.buildCacheKey(key)
	data, err := p.client.Get(p.ctx, redisKey).Bytes()
	if err == redis.Nil {
		// 缓存未命中
		global.GVA_LOG.Debug("PanSou Redis缓存未命中", zap.String("key", redisKey))
		return nil, false, nil
	}
	if err != nil {
		global.GVA_LOG.Error("PanSou Redis缓存获取失败",
			zap.String("key", redisKey),
			zap.Error(err))
		return nil, false, err
	}

	global.GVA_LOG.Debug("PanSou Redis缓存命中",
		zap.String("key", redisKey),
		zap.Int("size", len(data)))
	return data, true, nil
}

// Delete 删除缓存
func (p *PanSouRedisCache) Delete(key string) error {
	redisKey := p.buildCacheKey(key)
	err := p.client.Del(p.ctx, redisKey).Err()
	if err != nil {
		global.GVA_LOG.Error("PanSou Redis缓存删除失败",
			zap.String("key", redisKey),
			zap.Error(err))
		return err
	}

	global.GVA_LOG.Debug("PanSou Redis缓存删除成功", zap.String("key", redisKey))
	return nil
}

// Clear 清空所有PanSou缓存
func (p *PanSouRedisCache) Clear() error {
	// 使用SCAN命令查找所有PanSou相关的键
	pattern := PanSouCachePrefix + "*"
	keys, err := p.scanKeys(pattern)
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	// 批量删除
	err = p.client.Del(p.ctx, keys...).Err()
	if err != nil {
		global.GVA_LOG.Error("PanSou Redis缓存清空失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("PanSou Redis缓存清空成功", zap.Int("count", len(keys)))
	return nil
}

// scanKeys 扫描匹配模式的键
func (p *PanSouRedisCache) scanKeys(pattern string) ([]string, error) {
	var keys []string
	iter := p.client.Scan(p.ctx, 0, pattern, 0).Iterator()

	for iter.Next(p.ctx) {
		keys = append(keys, iter.Val())
	}

	if err := iter.Err(); err != nil {
		return nil, err
	}

	return keys, nil
}

// SetSearchResults 设置搜索结果（专用方法）
func (p *PanSouRedisCache) SetSearchResults(key string, results []pansou.SearchResult, ttl time.Duration) error {
	// 序列化搜索结果
	data, err := p.serializer.Serialize(results)
	if err != nil {
		return fmt.Errorf("序列化搜索结果失败: %v", err)
	}

	// 使用Pipeline批量操作
	pipe := p.client.Pipeline()

	// 设置主数据
	redisKey := p.buildCacheKey(key)
	if ttl <= 0 {
		ttl = DefaultTTL
	}
	pipe.Set(p.ctx, redisKey, data, ttl)

	// 设置元数据
	metaKey := p.buildMetaKey(key)
	metadata := map[string]interface{}{
		"count":      len(results),
		"created_at": time.Now().Unix(),
		"ttl":        int64(ttl.Seconds()),
		"sources":    extractSourcesFromResults(results),
	}
	metaData, _ := json.Marshal(metadata)
	pipe.Set(p.ctx, metaKey, metaData, ttl)

	// 更新统计信息
	statsKey := PanSouStatsPrefix + "total"
	pipe.HIncrBy(p.ctx, statsKey, "total_sets", 1)
	pipe.HIncrBy(p.ctx, statsKey, "total_results", int64(len(results)))
	pipe.Expire(p.ctx, statsKey, 24*time.Hour) // 统计信息保留24小时

	// 执行Pipeline
	_, err = pipe.Exec(p.ctx)
	if err != nil {
		global.GVA_LOG.Error("PanSou Redis批量操作失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Debug("PanSou搜索结果缓存成功",
		zap.String("key", key),
		zap.Int("count", len(results)),
		zap.Duration("ttl", ttl))

	return nil
}

// GetSearchResults 获取搜索结果（专用方法）
func (p *PanSouRedisCache) GetSearchResults(key string) ([]pansou.SearchResult, bool, error) {
	data, hit, err := p.Get(key)
	if err != nil || !hit {
		return nil, hit, err
	}

	var results []pansou.SearchResult
	if err := p.serializer.Deserialize(data, &results); err != nil {
		global.GVA_LOG.Error("PanSou搜索结果反序列化失败",
			zap.String("key", key),
			zap.Error(err))
		return nil, false, fmt.Errorf("反序列化失败: %v", err)
	}

	// 异步更新访问统计
	go p.updateAccessStats(key)

	global.GVA_LOG.Debug("PanSou搜索结果获取成功",
		zap.String("key", key),
		zap.Int("count", len(results)))

	return results, true, nil
}

// updateAccessStats 更新访问统计
func (p *PanSouRedisCache) updateAccessStats(key string) {
	statsKey := PanSouStatsPrefix + "total"
	pipe := p.client.Pipeline()

	// 更新命中统计
	pipe.HIncrBy(context.Background(), statsKey, "total_hits", 1)

	// 更新最后访问时间
	metaKey := p.buildMetaKey(key)
	pipe.HSet(context.Background(), metaKey, "last_accessed", time.Now().Unix())

	pipe.Exec(context.Background())
}

// MergeSearchResults 合并搜索结果到现有缓存
func (p *PanSouRedisCache) MergeSearchResults(key string, newResults []pansou.SearchResult, ttl time.Duration) error {
	// 获取现有结果
	existingResults, hit, err := p.GetSearchResults(key)
	if err != nil {
		return err
	}

	var finalResults []pansou.SearchResult
	if hit {
		// 合并结果（去重）
		finalResults = mergeAndDeduplicateResults(existingResults, newResults)
	} else {
		finalResults = newResults
	}

	// 保存合并后的结果
	return p.SetSearchResults(key, finalResults, ttl)
}

// GetCacheStats 获取缓存统计信息
func (p *PanSouRedisCache) GetCacheStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取基础Redis信息
	info, err := p.client.Info(p.ctx, "memory", "stats").Result()
	if err == nil {
		// 解析Redis INFO输出的关键信息
		stats["redis_info"] = parseRedisInfo(info)
	}

	// 获取PanSou统计信息
	statsKey := PanSouStatsPrefix + "total"
	customStats, err := p.client.HGetAll(p.ctx, statsKey).Result()
	if err == nil {
		for k, v := range customStats {
			stats[k] = v
		}
	}

	// 计算PanSou缓存键数量
	pattern := PanSouCachePrefix + "*"
	keys, err := p.scanKeys(pattern)
	if err == nil {
		stats["pansou_cache_keys"] = len(keys)
	}

	// 计算命中率
	if totalSets, ok := stats["total_sets"].(string); ok {
		if totalHits, ok := stats["total_hits"].(string); ok {
			// 这里需要转换字符串为数字并计算命中率
			stats["hit_rate"] = calculateHitRate(totalSets, totalHits)
		}
	}

	stats["cache_type"] = "redis"
	stats["prefix"] = PanSouCachePrefix

	return stats, nil
}

// GetSerializer 获取序列化器
func (p *PanSouRedisCache) GetSerializer() Serializer {
	return p.serializer
}

// SetSerializer 设置序列化器
func (p *PanSouRedisCache) SetSerializer(serializer Serializer) {
	p.serializer = serializer
}

// Exists 检查键是否存在
func (p *PanSouRedisCache) Exists(key string) (bool, error) {
	redisKey := p.buildCacheKey(key)
	count, err := p.client.Exists(p.ctx, redisKey).Result()
	return count > 0, err
}

// TTL 获取键的剩余生存时间
func (p *PanSouRedisCache) TTL(key string) (time.Duration, error) {
	redisKey := p.buildCacheKey(key)
	return p.client.TTL(p.ctx, redisKey).Result()
}

// 辅助函数

// extractSourcesFromResults 从搜索结果中提取数据源
func extractSourcesFromResults(results []pansou.SearchResult) []string {
	sourceMap := make(map[string]bool)
	for _, result := range results {
		if result.Channel != "" {
			sourceMap["tg:"+result.Channel] = true
		}
		// 可以从其他字段推断插件来源
		if result.UniqueID != "" {
			// 从UniqueID推断插件类型
			if len(result.UniqueID) > 10 {
				pluginType := result.UniqueID[:10] // 简单的插件类型推断
				sourceMap["plugin:"+pluginType] = true
			}
		}
	}

	sources := make([]string, 0, len(sourceMap))
	for source := range sourceMap {
		sources = append(sources, source)
	}
	return sources
}

// parseRedisInfo 解析Redis INFO输出
func parseRedisInfo(info string) map[string]string {
	result := make(map[string]string)
	lines := []string{} // 这里应该解析info字符串，为简化示例省略具体实现
	for _, line := range lines {
		// 解析每一行的键值对
		_ = line // 占位符
	}
	result["status"] = "connected"
	return result
}

// calculateHitRate 计算命中率
func calculateHitRate(totalSetsStr, totalHitsStr string) string {
	// 这里应该实现字符串到数字的转换和命中率计算
	// 为简化示例，返回占位符
	return "0.00%"
}

// mergeAndDeduplicateResults 合并并去重搜索结果
func mergeAndDeduplicateResults(existing, new []pansou.SearchResult) []pansou.SearchResult {
	// 使用map进行去重，以UniqueID为键
	resultMap := make(map[string]pansou.SearchResult)

	// 先添加现有结果
	for _, result := range existing {
		resultMap[result.UniqueID] = result
	}

	// 添加新结果，如果UniqueID相同则覆盖（保留最新的）
	for _, result := range new {
		resultMap[result.UniqueID] = result
	}

	// 转换回切片
	merged := make([]pansou.SearchResult, 0, len(resultMap))
	for _, result := range resultMap {
		merged = append(merged, result)
	}

	return merged
}

package utils

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts     int           // 最大重试次数
	InitialDelay    time.Duration // 初始延迟时间
	MaxDelay        time.Duration // 最大延迟时间
	BackoffFactor   float64       // 退避因子
	RetryableErrors []string      // 可重试的错误类型
}

// DefaultRetryConfig 默认重试配置
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxAttempts:   3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      5 * time.Second,
		BackoffFactor: 2.0,
		RetryableErrors: []string{
			"Lock wait timeout exceeded",
			"Deadlock found",
			"Connection lost",
			"Connection timeout",
			"Server has gone away",
		},
	}
}

// IsRetryableError 判断错误是否可重试
func IsRetryableError(err error, retryableErrors []string) bool {
	if err == nil {
		return false
	}
	
	errStr := err.Error()
	for _, retryableErr := range retryableErrors {
		if strings.Contains(errStr, retryableErr) {
			return true
		}
	}
	return false
}

// RetryWithBackoff 带退避的重试函数
func RetryWithBackoff(ctx context.Context, config *RetryConfig, operation func() error) error {
	if config == nil {
		config = DefaultRetryConfig()
	}

	var lastErr error
	delay := config.InitialDelay

	for attempt := 0; attempt < config.MaxAttempts; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return fmt.Errorf("operation cancelled: %v", ctx.Err())
		default:
		}

		// 执行操作
		err := operation()
		if err == nil {
			// 操作成功，返回
			return nil
		}

		lastErr = err

		// 检查是否是可重试的错误
		if !IsRetryableError(err, config.RetryableErrors) {
			return fmt.Errorf("non-retryable error: %v", err)
		}

		// 如果是最后一次尝试，直接返回错误
		if attempt == config.MaxAttempts-1 {
			return fmt.Errorf("operation failed after %d attempts, last error: %v", config.MaxAttempts, err)
		}

		// 记录重试信息
		zap.L().Warn("Operation failed, retrying",
			zap.Error(err),
			zap.Int("attempt", attempt+1),
			zap.Int("maxAttempts", config.MaxAttempts),
			zap.Duration("delay", delay))

		// 等待延迟时间
		select {
		case <-ctx.Done():
			return fmt.Errorf("operation cancelled during retry: %v", ctx.Err())
		case <-time.After(delay):
		}

		// 计算下一次延迟时间（指数退避）
		delay = time.Duration(float64(delay) * config.BackoffFactor)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}
	}

	return lastErr
}

// RetryDBOperation 数据库操作重试函数
func RetryDBOperation(ctx context.Context, db *gorm.DB, config *RetryConfig, operation func(*gorm.DB) error) error {
	return RetryWithBackoff(ctx, config, func() error {
		return operation(db)
	})
}

// RetryDBTransaction 数据库事务重试函数
func RetryDBTransaction(ctx context.Context, db *gorm.DB, config *RetryConfig, operation func(*gorm.DB) error) error {
	return RetryWithBackoff(ctx, config, func() error {
		tx := db.Begin()
		if tx.Error != nil {
			return tx.Error
		}

		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		if err := operation(tx); err != nil {
			tx.Rollback()
			return err
		}

		return tx.Commit().Error
	})
}

// RetryWithContext 带上下文的重试函数
func RetryWithContext(ctx context.Context, maxAttempts int, operation func() error) error {
	config := &RetryConfig{
		MaxAttempts:   maxAttempts,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      2 * time.Second,
		BackoffFactor: 1.5,
		RetryableErrors: []string{
			"Lock wait timeout exceeded",
			"Deadlock found",
		},
	}
	return RetryWithBackoff(ctx, config, operation)
}

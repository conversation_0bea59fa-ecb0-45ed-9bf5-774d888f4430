package utils

import (
	"math/rand"
	"time"
)

var (
	IdVerify               = Rules{"ID": []string{NotEmpty()}}
	ApiVerify              = Rules{"Path": {NotEmpty()}, "Description": {NotEmpty()}, "ApiGroup": {NotEmpty()}, "Method": {NotEmpty()}}
	MenuVerify             = Rules{"Path": {NotEmpty()}, "Name": {NotEmpty()}, "Component": {NotEmpty()}, "Sort": {Ge("0")}}
	MenuMetaVerify         = Rules{"Title": {NotEmpty()}}
	LoginVerify            = Rules{"CaptchaId": {NotEmpty()}, "Captcha": {NotEmpty()}, "Username": {NotEmpty()}, "Password": {NotEmpty()}}
	RegisterVerify         = Rules{"Username": {NotEmpty()}, "NickName": {NotEmpty()}, "Password": {NotEmpty()}, "AuthorityId": {NotEmpty()}}
	PhoneRegisterVerify    = Rules{"Phone": {NotEmpty(), RegexpMatch(`^1[3-9]\d{9}$`)}, "Code": {NotEmpty()}, "Password": {NotEmpty()}}
	PageInfoVerify         = Rules{"Page": {NotEmpty()}, "PageSize": {NotEmpty()}}
	CustomerVerify         = Rules{"CustomerName": {NotEmpty()}, "CustomerPhoneData": {NotEmpty()}}
	AutoCodeVerify         = Rules{"Abbreviation": {NotEmpty()}, "StructName": {NotEmpty()}, "PackageName": {NotEmpty()}, "Fields": {NotEmpty()}}
	AutoPackageVerify      = Rules{"PackageName": {NotEmpty()}}
	AuthorityVerify        = Rules{"AuthorityId": {NotEmpty()}, "AuthorityName": {NotEmpty()}}
	AuthorityIdVerify      = Rules{"AuthorityId": {NotEmpty()}}
	OldAuthorityVerify     = Rules{"OldAuthorityId": {NotEmpty()}}
	ChangePasswordVerify   = Rules{"NewPassword": {NotEmpty()}}
	ResetPasswordVerify    = Rules{"Phone": {NotEmpty(), RegexpMatch(`^1[3-9]\d{9}$`)}, "Code": {NotEmpty()}, "NewPassword": {NotEmpty()}}
	SetUserAuthorityVerify = Rules{"AuthorityId": {NotEmpty()}}
	EmailVerify            = Rules{"Email": {NotEmpty(), RegexpMatch(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)}}
)

// ValidatePhone 验证手机号格式
func ValidatePhone(phone string) bool {
	// 中国大陆手机号正则：1开头，第二位是3-9，总共11位数字
	phoneRegex := `^1[3-9]\d{9}$`
	return regexpMatch(phoneRegex, phone)
}

// ValidatePassword 验证密码强度
func ValidatePassword(password string) bool {
	// 密码长度至少6位
	if len(password) < 6 {
		return false
	}

	// 检查是否包含数字
	hasNumber := regexpMatch(`[0-9]`, password)
	// 检查是否包含字母
	hasLetter := regexpMatch(`[a-zA-Z]`, password)
	// 检查是否包含特殊字符
	hasSpecial := regexpMatch(`[!@#$%^&*(),.?":{}|<>]`, password)

	// 至少包含三种中的任意两种
	count := 0
	if hasNumber {
		count++
	}
	if hasLetter {
		count++
	}
	if hasSpecial {
		count++
	}

	return count >= 2
}

// GenerateRandomCode 生成指定长度的随机数字验证码
func GenerateRandomCode(length int) string {
	if length <= 0 {
		return ""
	}

	numbers := "0123456789"
	code := make([]byte, length)

	rand.Seed(time.Now().UnixNano())
	for i := 0; i < length; i++ {
		code[i] = numbers[rand.Intn(len(numbers))]
	}

	return string(code)
}

// MaskPhone 手机号脱敏处理
func MaskPhone(phone string) string {
	if phone == "" {
		return ""
	}
	if len(phone) >= 7 {
		return phone[:3] + "****" + phone[len(phone)-4:]
	}
	// 如果手机号长度不足7位，只显示前3位
	if len(phone) > 3 {
		return phone[:3] + "****"
	}
	return "****"
}

package middleware

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SmsLimitConfig 短信限流配置
type SmsLimitConfig struct {
	// GenerationIPKey 根据IP生成key
	GenerationIPKey func(c *gin.Context) string
	// GenerationPhoneKey 根据手机号生成key
	GenerationPhoneKey func(c *gin.Context, phone string) string
	// CheckOrMarkIP IP限流检查
	CheckOrMarkIP func(key string, expire int, limit int) error
	// CheckOrMarkPhone 手机号限流检查
	CheckOrMarkPhone func(key string, expire int, limit int) error
	// ExpireIP IP限流过期时间(秒)
	ExpireIP int
	// LimitIP IP限流次数
	LimitIP int
	// ExpirePhone 手机号限流过期时间(秒)
	ExpirePhone int
	// LimitPhone 手机号限流次数
	LimitPhone int
}

// SmsLimitWithTime 短信限流中间件
func (s SmsLimitConfig) SmsLimitWithTime() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. IP级别限流检查
		ipKey := s.GenerationIPKey(c)
		if err := s.CheckOrMarkIP(ipKey, s.ExpireIP, s.LimitIP); err != nil {
			global.GVA_LOG.Warn("SMS IP limit exceeded",
				zap.String("ip", c.ClientIP()),
				zap.String("key", ipKey),
				zap.Error(err))
			c.JSON(http.StatusOK, gin.H{"code": response.ERROR, "msg": err.Error()})
			c.Abort()
			return
		}

		// 2. 解析请求体获取手机号进行手机号级别限流
		var requestBody map[string]interface{}
		if c.Request.Body != nil {
			// 读取请求体
			bodyBytes, err := c.GetRawData()
			if err == nil && len(bodyBytes) > 0 {
				if err := json.Unmarshal(bodyBytes, &requestBody); err == nil {
					if phone, exists := requestBody["phone"]; exists {
						if phoneStr, ok := phone.(string); ok && phoneStr != "" {
							phoneKey := s.GenerationPhoneKey(c, phoneStr)
							if err := s.CheckOrMarkPhone(phoneKey, s.ExpirePhone, s.LimitPhone); err != nil {
								global.GVA_LOG.Warn("SMS phone limit exceeded",
									zap.String("phone", phoneStr),
									zap.String("key", phoneKey),
									zap.Error(err))
								c.JSON(http.StatusOK, gin.H{"code": response.ERROR, "msg": err.Error()})
								c.Abort()
								return
							}
						}
					}
				}
				// 重新设置请求体，因为已经读取过了
				c.Request.Body = &readCloser{bytes: bodyBytes}
			}
		}

		c.Next()
	}
}

// readCloser 用于重新设置请求体
type readCloser struct {
	bytes []byte
	index int
}

func (r *readCloser) Read(p []byte) (n int, err error) {
	if r.index >= len(r.bytes) {
		return 0, fmt.Errorf("EOF")
	}
	n = copy(p, r.bytes[r.index:])
	r.index += n
	return n, nil
}

func (r *readCloser) Close() error {
	return nil
}

// DefaultSmsGenerationIPKey 默认IP key生成函数
func DefaultSmsGenerationIPKey(c *gin.Context) string {
	return "SMS_IP_LIMIT:" + c.ClientIP()
}

// DefaultSmsGenerationPhoneKey 默认手机号key生成函数
func DefaultSmsGenerationPhoneKey(c *gin.Context, phone string) string {
	return "SMS_PHONE_LIMIT:" + phone
}

// DefaultSmsCheckOrMarkIP 默认IP限流检查函数
func DefaultSmsCheckOrMarkIP(key string, expire int, limit int) error {
	if global.GVA_REDIS == nil {
		return nil // 如果Redis未启用，跳过限流
	}
	return SetSmsLimitWithTime(key, limit, time.Duration(expire)*time.Second)
}

// DefaultSmsCheckOrMarkPhone 默认手机号限流检查函数
func DefaultSmsCheckOrMarkPhone(key string, expire int, limit int) error {
	if global.GVA_REDIS == nil {
		return nil // 如果Redis未启用，跳过限流
	}
	return SetSmsLimitWithTime(key, limit, time.Duration(expire)*time.Second)
}

// DefaultSmsLimit 默认短信限流中间件
func DefaultSmsLimit() gin.HandlerFunc {
	// 设置默认值，如果配置为0则使用默认值
	ipLimit := global.GVA_CONFIG.System.SmsLimitCountIP
	if ipLimit == 0 {
		ipLimit = 30 // 默认每小时30次
	}

	ipExpire := global.GVA_CONFIG.System.SmsLimitTimeIP
	if ipExpire == 0 {
		ipExpire = 1800 // 默认30分钟
	}

	phoneLimit := global.GVA_CONFIG.System.SmsLimitCountPhone
	if phoneLimit == 0 {
		phoneLimit = 8 // 默认每小时8次
	}

	phoneExpire := global.GVA_CONFIG.System.SmsLimitTimePhone
	if phoneExpire == 0 {
		phoneExpire = 1800 // 默认30分钟
	}

	return SmsLimitConfig{
		GenerationIPKey:    DefaultSmsGenerationIPKey,
		GenerationPhoneKey: DefaultSmsGenerationPhoneKey,
		CheckOrMarkIP:      DefaultSmsCheckOrMarkIP,
		CheckOrMarkPhone:   DefaultSmsCheckOrMarkPhone,
		ExpireIP:           ipExpire,
		LimitIP:            ipLimit,
		ExpirePhone:        phoneExpire,
		LimitPhone:         phoneLimit,
	}.SmsLimitWithTime()
}

// SetSmsLimitWithTime 设置短信限流访问次数（复用现有的限流逻辑）
func SetSmsLimitWithTime(key string, limit int, expiration time.Duration) error {
	count, err := global.GVA_REDIS.Exists(context.Background(), key).Result()
	if err != nil {
		return err
	}
	if count == 0 {
		pipe := global.GVA_REDIS.TxPipeline()
		pipe.Incr(context.Background(), key)
		pipe.Expire(context.Background(), key, expiration)
		_, err = pipe.Exec(context.Background())
		return err
	} else {
		// 检查次数
		if times, err := global.GVA_REDIS.Get(context.Background(), key).Int(); err != nil {
			return err
		} else {
			if times >= limit {
				if t, err := global.GVA_REDIS.PTTL(context.Background(), key).Result(); err != nil {
					return errors.New("短信发送太过频繁，请稍后再试")
				} else {
					return errors.New("短信发送太过频繁, 请 " + t.String() + " 后尝试")
				}
			} else {
				return global.GVA_REDIS.Incr(context.Background(), key).Err()
			}
		}
	}
}

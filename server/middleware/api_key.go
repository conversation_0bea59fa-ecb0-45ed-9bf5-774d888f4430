package middleware

import (
	"net/http"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/gin-gonic/gin"
)

// ApiKeyAuth 校验API密钥中间件
func ApiKeyAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-Api-Key")
		if apiKey == "" {
			apiKey = c.GetHeader("Authorization")
		}
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "API密钥缺失"})
			c.Abort()
			return
		}

		var key mcp.ApiKey
		err := global.GVA_DB.Where("api_key = ?", apiKey).First(&key).Error
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "API密钥无效"})
			c.Abort()
			return
		}

		// 可扩展：校验密钥是否过期/禁用等

		// 将用户ID注入上下文，便于后续获取
		c.Set("userId", key.UserId)
		c.Next()
	}
}

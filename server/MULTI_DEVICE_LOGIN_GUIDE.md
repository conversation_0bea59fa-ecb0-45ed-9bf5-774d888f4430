# 多设备登录限制方案设计说明

## 概述

本方案实现了一个完整的多设备登录限制系统，支持：
- **设备登录**：每个用户最多可在3台设备上同时登录
- **网站登录**：每个用户最多可在1个网站页面上登录
- **设备Token刷新**：只有在3台设备范围内的设备才能刷新Token
- **会话管理**：提供完整的会话查看、注销功能

## 核心功能

### 1. 登录类型区分

#### 设备登录
- 客户端需要提供 `deviceId` 参数
- 每个用户最多同时登录3台设备
- 同一设备重复登录会踢出旧会话
- 超出限制时自动踢出最早登录的设备

#### 网站登录
- 不需要提供 `deviceId` 参数
- 每个用户最多1个网站会话
- 新的网站登录会踢出旧的网站会话

### 2. Token刷新限制

#### 设备Token刷新
- 必须验证设备在允许的3台设备范围内
- 调用接口：`POST /user/refresh-token?deviceId=xxx`

#### 网站Token刷新
- 只验证会话是否有效
- 调用接口：`POST /user/refresh-token`

### 3. 会话管理功能

- 查看活跃会话列表：`GET /user/sessions`
- 注销指定会话：`DELETE /user/sessions/logout?sessionId=xxx`
- 注销所有其他会话：`DELETE /user/sessions/logout-others`
- 查看会话统计：`GET /user/sessions/statistics`

## 技术实现

### 数据库模型

#### SysUserSession 会话表
```go
type SysUserSession struct {
    UserID        uint      // 用户ID
    DeviceID      *string   // 设备ID（网站登录时为空）
    SessionType   string    // 会话类型：device|web
    Token         string    // JWT Token
    IPAddress     string    // 登录IP地址
    UserAgent     string    // 用户代理
    LoginAt       time.Time // 登录时间
    LastActiveAt  time.Time // 最后活跃时间
    ExpiresAt     time.Time // 过期时间
    IsActive      bool      // 是否活跃
    DeviceName    string    // 设备名称
    OSInfo        string    // 操作系统信息
    AppVersion    string    // 应用版本
}
```

### 核心服务

#### UserSessionService
- `CreateSession()` - 创建用户会话
- `ValidateSession()` - 验证会话有效性
- `ValidateDeviceSession()` - 验证设备会话
- `DeactivateSession()` - 停用会话
- `GetUserActiveSessions()` - 获取用户活跃会话

#### JwtService 扩展
- `CreateSessionAndToken()` - 创建会话和Token
- `RefreshTokenByDevice()` - 设备Token刷新
- `RefreshTokenByWeb()` - 网站Token刷新
- `LogoutSession()` - 注销指定会话
- `LogoutAllSessions()` - 注销所有会话

## 使用方式

### 1. 客户端登录

#### 设备登录请求
```http
POST /base/login
Content-Type: application/json

{
    "username": "admin",
    "password": "123456",
    "deviceId": "device-001"  // 设备唯一标识
}
```

#### 网站登录请求
```http
POST /base/login
Content-Type: application/json

{
    "username": "admin",
    "password": "123456"
    // 不提供deviceId表示网站登录
}
```

### 2. Token刷新

#### 设备Token刷新
```http
POST /user/refresh-token?deviceId=device-001
Authorization: Bearer your-token
```

#### 网站Token刷新
```http
POST /user/refresh-token
Authorization: Bearer your-token
```

### 3. 会话管理

#### 查看我的会话
```http
GET /user/sessions
Authorization: Bearer your-token
```

响应示例：
```json
{
    "code": 0,
    "data": {
        "sessions": [
            {
                "id": 1,
                "deviceId": "device-001",
                "sessionType": "device",
                "deviceName": "iPhone 13",
                "osInfo": "iOS 15.0",
                "ipAddress": "*************",
                "loginAt": "2024-01-01T10:00:00Z",
                "lastActiveAt": "2024-01-01T12:30:00Z"
            }
        ],
        "total": 1
    },
    "msg": "获取会话列表成功"
}
```

#### 注销指定会话
```http
DELETE /user/sessions/logout?sessionId=1
Authorization: Bearer your-token
```

#### 注销所有其他会话
```http
DELETE /user/sessions/logout-others
Authorization: Bearer your-token
```

### 4. 会话统计
```http
GET /user/sessions/statistics
Authorization: Bearer your-token
```

响应示例：
```json
{
    "code": 0,
    "data": {
        "totalSessions": 2,
        "deviceSessions": 1,
        "webSessions": 1,
        "maxDevices": 3,
        "maxWeb": 1
    },
    "msg": "获取会话统计成功"
}
```

## 配置说明

### 限制配置
```go
const (
    MaxDeviceLogins = 3 // 最大设备登录数量
    MaxWebLogins    = 1 // 最大网站登录数量
)
```

可以在 `model/system/sys_user_session.go` 中修改这些常量来调整限制。

### 数据库初始化

在应用启动时调用初始化函数：
```go
import "github.com/flipped-aurora/gin-vue-admin/server/initialize"

func main() {
    // ... 其他初始化代码
    initialize.InitUserSessionTable()
    // ... 
}
```

## 安全特性

1. **自动踢出机制**：超出限制时自动踢出最早的会话
2. **同设备登录检测**：同一设备重复登录会踢出旧会话
3. **会话验证**：每次API调用都会验证会话有效性
4. **Token黑名单**：停用的Token会加入黑名单
5. **过期会话清理**：自动清理过期的会话记录

## 监控和维护

### 定期清理过期会话
建议设置定时任务清理过期会话：
```go
// 每小时清理一次过期会话
go func() {
    ticker := time.NewTicker(time.Hour)
    for range ticker.C {
        err := UserSessionServiceApp.CleanExpiredSessions()
        if err != nil {
            log.Error("清理过期会话失败", zap.Error(err))
        }
    }
}()
```

### 会话监控
可以通过查询会话表监控用户登录情况：
```sql
-- 查看活跃会话统计
SELECT 
    session_type,
    COUNT(*) as count
FROM sys_user_sessions 
WHERE is_active = true AND expires_at > NOW()
GROUP BY session_type;

-- 查看用户登录设备数量
SELECT 
    user_id,
    COUNT(*) as device_count
FROM sys_user_sessions 
WHERE session_type = 'device' AND is_active = true AND expires_at > NOW()
GROUP BY user_id
HAVING COUNT(*) > 3;
```

## 错误处理

### 常见错误响应

1. **设备数量超限**
```json
{
    "code": 1,
    "msg": "设备登录数量已达上限，已踢出最早登录的设备"
}
```

2. **设备会话无效**
```json
{
    "code": 1,
    "msg": "设备会话不存在或已过期，无法刷新Token"
}
```

3. **用户设备超限**
```json
{
    "code": 1,
    "msg": "用户设备登录数量超出限制，无法刷新Token"
}
```

## 扩展说明

如需调整设备数量限制或添加其他登录类型，可以：

1. 修改 `model/system/sys_user_session.go` 中的常量
2. 在 `UserSessionService` 中添加新的会话类型处理逻辑
3. 更新相关的API接口和验证逻辑

该方案提供了完整的多设备登录管理能力，确保系统安全性的同时提供良好的用户体验。 
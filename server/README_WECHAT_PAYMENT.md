# 微信支付配置说明

## 配置参数说明

根据微信支付官方Go SDK文档，需要配置以下参数：

### 必需参数

1. **AppID** (`appId`): 微信应用ID
   - 从微信公众平台或开放平台获取

2. **商户号** (`mchId`): 微信支付商户号
   - 从微信商户平台获取

3. **商户证书序列号** (`mchCertificateSerialNumber`): 商户API证书的序列号
   - 从微信商户平台下载的API证书中获取
   - 格式示例: `3775B6A45ACD588826D15E583A95F5DD********`

4. **APIv3密钥** (`apiKey`): 微信支付APIv3密钥
   - 在微信商户平台设置，32位字符串
   - 格式示例: `2ab9****************************`

5. **商户私钥文件路径** (`keyPath`): 商户API私钥文件路径
   - 从微信商户平台下载的`apiclient_key.pem`文件路径
   - 示例: `/path/to/merchant/apiclient_key.pem`

6. **异步通知地址** (`notifyUrl`): 支付结果异步通知地址
   - 必须是可公网访问的HTTPS地址
   - 示例: `https://yourdomain.com/api/payment/notify/wechat`

### 可选参数

7. **证书文件路径** (`certPath`): 商户API证书文件路径（某些场景需要）
   - 从微信商户平台下载的`apiclient_cert.pem`文件路径

8. **H5支付域名** (`h5Domain`): H5支付授权域名
   - H5支付需要配置的授权域名

9. **H5返回地址** (`h5ReturnUrl`): H5支付完成后返回地址

## 配置示例

### 方式1：使用文件路径（推荐用于本地开发）
```yaml
payment:
  wechat:
    enabled: true
    app-id: "wxd678efh567hg6787"
    mch-id: "1900009191"
    mch-certificate-serial-number: "3775B6A45ACD588826D15E583A95F5DD40333312"
    apiv3-key: "2ab9312c4d567890abcdef1234567890"
    key-path: "certs/wechat/apiclient_key.pem"
    cert-path: "certs/wechat/apiclient_cert.pem"
    notify-url: "https://yourdomain.com/api/payment/notify/wechat"
    h5-domain: "yourdomain.com"
    h5-return-url: "https://yourdomain.com/payment/result"
```

### 方式2：直接配置私钥内容（推荐用于云部署）
```yaml
payment:
  wechat:
    enabled: true
    app-id: "wxd678efh567hg6787"
    mch-id: "1900009191"
    mch-certificate-serial-number: "3775B6A45ACD588826D15E583A95F5DD40333312"
    apiv3-key: "2ab9312c4d567890abcdef1234567890"
    # 直接配置私钥内容，无需文件
    private-key: |
      -----BEGIN PRIVATE KEY-----
      MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
      -----END PRIVATE KEY-----
    notify-url: "https://yourdomain.com/api/payment/notify/wechat"
    h5-domain: "yourdomain.com"
    h5-return-url: "https://yourdomain.com/payment/result"
```

### JSON格式示例
```json
{
  "appId": "wxd678efh567hg6787",
  "mchId": "1900009191",
  "mchCertificateSerialNumber": "3775B6A45ACD588826D15E583A95F5DD40333312",
  "apiKey": "2ab9312c4d567890abcdef1234567890",
  "privateKey": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----",
  "notifyUrl": "https://yourdomain.com/api/payment/notify/wechat",
  "h5Domain": "yourdomain.com",
  "h5ReturnUrl": "https://yourdomain.com/payment/result"
}
```

## 获取商户证书序列号的方法

### 方法1: 从证书文件获取
```bash
openssl x509 -in apiclient_cert.pem -noout -serial | sed 's/serial=//'
```

### 方法2: 从微信商户平台查看
1. 登录微信商户平台
2. 进入"账户中心" -> "API安全"
3. 在"API证书"部分可以查看证书序列号

### 方法3: 使用微信提供的工具
微信官方提供了平台证书下载工具，可以用来获取和验证证书信息。

## 与官方文档的对应关系

| 官方文档参数 | 项目配置字段 | 说明 |
|-------------|-------------|------|
| mchID | mchId | 商户号 |
| mchCertificateSerialNumber | mchCertificateSerialNumber | 商户证书序列号 |
| mchPrivateKey | keyPath | 私钥文件路径 |
| mchAPIv3Key | apiKey | APIv3密钥 |

## 注意事项

1. **证书序列号格式**: 必须是32位或40位的十六进制字符串，不包含冒号分隔符
2. **私钥文件**: 必须是PEM格式的私钥文件，通常以`.pem`结尾
3. **APIv3密钥**: 必须是32位字符串，在商户平台设置
4. **通知地址**: 必须使用HTTPS协议，且能正常接收POST请求
5. **域名配置**: H5支付需要在微信商户平台配置授权域名

## 常见问题

### Q: 如何生成商户证书序列号？
A: 商户证书序列号是证书的固有属性，不需要生成，只需要从已有证书中提取。

### Q: APIv3密钥和商户密钥有什么区别？
A: APIv3密钥是用于API v3接口的密钥，商户密钥是用于API v2接口的密钥，两者不同。

### Q: 证书文件和私钥文件有什么区别？
A: 证书文件包含公钥和证书信息，私钥文件包含私钥信息，两者配对使用。

## 配置迁移指南

### 从旧版本配置升级

如果您正在从旧版本配置升级，需要进行以下更改：

#### 1. config.yaml 文件更新

**旧配置（需要更新）:**
```yaml
payment:
  wechat:
    enabled: true
    app-id: "wx1234567890"
    mch-id: "1900000000"
    api-key: "your_old_api_key"  # ❌ 旧字段名
    cert-path: "/path/to/cert.pem"
    key-path: "/path/to/key.pem"
    notify-url: "https://domain.com/notify"
```

**新配置（推荐）:**
```yaml
payment:
  wechat:
    enabled: true
    app-id: "wx1234567890"
    mch-id: "1900000000"
    mch-certificate-serial-number: "3775B6A45ACD588826D15E583A95F5DD40333312"  # ✅ 新增必需字段
    apiv3-key: "32_char_apiv3_key_from_wechat_platform"  # ✅ 新字段名
    cert-path: "/path/to/apiclient_cert.pem"
    key-path: "/path/to/apiclient_key.pem"
    notify-url: "https://domain.com/api/payment/notify/wechatNotify"
    is-sandbox: false
    h5-domain: "domain.com"
    h5-return-url: "https://domain.com/payment/result"
```

#### 2. 必需的配置更改

1. **添加商户证书序列号**
   ```bash
   # 从证书文件获取序列号
   openssl x509 -in apiclient_cert.pem -noout -serial | sed 's/serial=//'
   ```

2. **更新APIv3密钥**
   - 登录微信商户平台
   - 进入"账户中心" -> "API安全" -> "API密钥"
   - 设置32位APIv3密钥

3. **验证私钥文件**
   ```bash
   # 确保私钥文件格式正确
   openssl rsa -in apiclient_key.pem -check -noout
   ```

#### 3. 测试配置

更新配置后，建议按以下步骤测试：

1. **启动应用程序**
   ```bash
   go run main.go
   ```

2. **检查日志输出**
   查看是否有初始化错误：
   ```bash
   tail -f log/server.log | grep -i wechat
   ```

3. **调用健康检查接口**
   ```bash
   curl -X GET "http://localhost:8888/api/payment/config"
   ```

4. **测试支付创建**
   ```bash
   curl -X POST "http://localhost:8888/api/payment/create" \
     -H "Content-Type: application/json" \
     -d '{
       "userId": 1,
       "amount": 100,
       "subject": "测试商品",
       "paymentMethod": "wechat",
       "deviceType": "pc",
       "expireMinutes": 30
     }'
   ```

#### 4. 回滚计划

如果新配置有问题，可以临时回滚：

1. **保留旧配置文件备份**
   ```bash
   cp config.yaml config.yaml.backup
   ```

2. **出现问题时快速回滚**
   ```bash
   mv config.yaml.backup config.yaml
   ```

#### 5. 部署环境注意事项

**开发环境:**
- 可以使用沙箱环境进行测试
- `is-sandbox: true`

**生产环境:**
- 确保使用生产环境密钥
- `is-sandbox: false`
- 证书文件权限设置为600
- 使用HTTPS通知地址

## 参考文档

- [微信支付Go SDK官方文档](https://pay.weixin.qq.com/doc/v3/merchant/**********)
- [微信支付商户平台](https://pay.weixin.qq.com/)
- [微信支付开发文档](https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html)

# 微信支付集成说明

## AppID配置说明

### 支持的AppID类型

1. **服务号AppID（mp-app-id）**
   - 从微信公众平台获取
   - **支持所有支付方式**：
     - ✅ Native支付（扫码支付）
     - ✅ H5支付（手机网站支付）
     - ✅ JSAPI支付（微信内支付）

2. **网站应用AppID（open-app-id）**
   - 从微信开放平台获取
   - 主要用于PC端和手机浏览器场景
   - 支持：
     - ✅ Native支付（扫码支付）
     - ✅ H5支付（手机网站支付）
     - ❌ JSAPI支付（必须使用服务号AppID）

### AppID选择策略

| 支付方式 | 设备类型 | 优先AppID | 备选AppID |
|---------|---------|-----------|-----------|
| JSAPI支付 | 微信内 | 服务号AppID | 无（必须使用服务号） |
| H5支付 | 手机浏览器 | 网站应用AppID | 服务号AppID |
| Native支付 | PC扫码 | 网站应用AppID | 服务号AppID |

### 配置建议

**最简配置**：只配置服务号AppID
```yaml
wechat:
  mp-app-id: "你的服务号AppID"  # 支持所有支付方式
  # open-app-id: ""            # 可选，用于优化PC和手机浏览器体验
```

**完整配置**：同时配置两种AppID
```yaml
wechat:
  mp-app-id: "你的服务号AppID"      # 用于JSAPI支付，也可用作其他支付方式的备选
  open-app-id: "你的网站应用AppID"   # 用于PC和手机浏览器场景的Native和H5支付
```

## 支付参数配置

根据微信支付官方Go SDK文档要求，需要配置以下参数：

```yaml
payment:
  wechat:
    enabled: true
    mp-app-id: "wxXXXXXXXXXXXXXXXX"                    # 服务号应用ID
    open-app-id: "wxYYYYYYYYYYYYYYYY"                  # 网站应用ID（可选）
    mch-id: "1234567890"                               # 商户号
    mch-certificate-serial-number: "证书序列号"          # 商户证书序列号
    private-key: |                                     # 商户私钥（apiclient_key.pem内容）
      -----BEGIN PRIVATE KEY-----
      ...
      -----END PRIVATE KEY-----
    apiv3-key: "你的APIv3密钥"                          # APIv3密钥
    notify-url: "https://yourdomain.com/api/v1/payment/wechat/notify"  # 支付结果通知URL
    h5-domain: "yourdomain.com"                        # H5支付域名（可选）
    h5-return-url: "https://yourdomain.com/pay/result" # H5支付跳转URL（可选）
```

### 获取商户证书序列号

可以通过以下方式获取商户证书序列号：

1. **使用OpenSSL命令**：
```bash
openssl x509 -in apiclient_cert.pem -noout -serial | sed 's/serial=//' | tr 'A-F' 'a-f'
```

2. **登录微信商户平台**：
   - 账户中心 → API安全 → API证书 → 查看证书
   - 复制证书序列号

3. **使用验证脚本**：
```bash
go run scripts/verify_wechat_config.go
```

### 配置迁移指南

如果您之前使用的是`api-key`字段，需要进行以下迁移：

**旧配置**：
```yaml
wechat:
  api-key: "你的APIv3密钥"
```

**新配置**：
```yaml
wechat:
  apiv3-key: "你的APIv3密钥"
  mch-certificate-serial-number: "你的商户证书序列号"  # 新增必需字段
```

## 测试配置

1. **验证配置文件**：
```bash
go run scripts/verify_wechat_config.go
```

2. **运行健康检查**：
```bash
curl http://localhost:8888/api/v1/payment/health
```

3. **测试支付订单创建**：
```bash
curl -X POST http://localhost:8888/api/v1/payment/create \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "subject": "测试订单",
    "deviceType": "pc"
  }'
```

## 常见问题

### Q: 为什么Native支付可以使用服务号AppID？
A: 根据微信支付官方文档，服务号AppID支持所有支付方式，包括Native扫码支付。网站应用AppID主要是为了在PC端提供更好的用户体验，但不是必需的。

### Q: 只配置服务号AppID可以吗？
A: 可以！服务号AppID支持所有支付方式（Native、H5、JSAPI）。如果您只需要基本支付功能，只配置服务号AppID就足够了。

### Q: 什么时候需要配置网站应用AppID？
A: 如果您的应用主要面向PC端用户或希望在手机浏览器中提供更好的支付体验，建议配置网站应用AppID作为PC和H5支付的优选AppID。

### Q: JSAPI支付为什么必须使用服务号AppID？
A: 这是微信支付的技术限制。JSAPI支付需要获取用户在服务号下的openid，因此必须使用服务号AppID。

## 总结

- **服务号AppID**：万能AppID，支持所有支付方式
- **网站应用AppID**：可选配置，用于优化PC和手机浏览器体验
- **配置策略**：至少配置服务号AppID，根据需要可选配置网站应用AppID
- **迁移要求**：添加`mch-certificate-serial-number`字段，将`api-key`改为`apiv3-key` 
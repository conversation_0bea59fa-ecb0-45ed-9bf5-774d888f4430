package jikepan

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/model/pansou"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/plugin"
	"io"
	"net/http"
	"strings"
	"time"
)

// 在init函数中注册插件
func init() {
	// 注册插件
	plugin.RegisterGlobalPlugin(NewJikepanAsyncV2Plugin())
}

const (
	// JikepanAPIURL 即刻盘API地址
	JikepanAPIURL = "https://api.jikepan.xyz/search"
)

// JikepanAsyncV2Plugin 即刻盘搜索异步V2插件
type JikepanAsyncV2Plugin struct {
	*plugin.BaseAsyncPlugin
}

// NewJikepanAsyncV2Plugin 创建新的即刻盘异步V2插件
func NewJikepanAsyncV2Plugin() *JikepanAsyncV2Plugin {
	return &JikepanAsyncV2Plugin{
		BaseAsyncPlugin: plugin.NewBaseAsyncPlugin("jikepan", 1),
	}
}

// Search 执行搜索并返回结果（兼容性方法）
func (p *JikepanAsyncV2Plugin) Search(keyword string, ext map[string]interface{}) ([]pansou.SearchResult, error) {
	result, err := p.SearchWithResult(keyword, ext)
	if err != nil {
		return nil, err
	}
	return result.Results, nil
}

// SearchWithResult 执行搜索并返回包含IsFinal标记的结果
func (p *JikepanAsyncV2Plugin) SearchWithResult(keyword string, ext map[string]interface{}) (pansou.PluginResult, error) {
	return p.AsyncSearchWithResult(keyword, p.doSearch, p.MainCacheKey, ext)
}

// doSearch 实际的搜索实现
func (p *JikepanAsyncV2Plugin) doSearch(client *http.Client, keyword string, ext map[string]interface{}) ([]pansou.SearchResult, error) {
	// 构建请求
	reqBody := map[string]interface{}{
		"name":   keyword,
		"is_all": false,
	}

	// 检查ext中是否包含自定义参数，如果有则使用它
	if ext != nil {
		if isAll, ok := ext["is_all"].(bool); ok && isAll {
			// 使用全量搜索，时间大约10秒
			reqBody["is_all"] = true
		}
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	req, err := http.NewRequest("POST", JikepanAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var apiResp JikepanAPIResponse
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	if err := json.Unmarshal(bodyBytes, &apiResp); err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	// 检查响应状态
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API returned error: %s", apiResp.Msg)
	}

	// 转换结果格式
	var results []pansou.SearchResult
	for _, item := range apiResp.Data {
		result := pansou.SearchResult{
			UniqueID: fmt.Sprintf("jikepan-%s", item.ID),
			Title:    item.Name,
			Content:  item.Description,
			Links:    convertJikepanLinks(item.Links),
			Datetime: time.Time{}, // 使用零值
		}
		results = append(results, result)
	}

	return results, nil
}

// JikepanAPIResponse API响应结构
type JikepanAPIResponse struct {
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data []JikepanItem `json:"data"`
}

// JikepanItem API响应中的单个结果项
type JikepanItem struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Links       []JikepanLink `json:"links"`
}

// JikepanLink API响应中的链接信息
type JikepanLink struct {
	Service string `json:"service"`
	Link    string `json:"link"`
	Pwd     string `json:"pwd,omitempty"`
}

// convertJikepanLinks 转换即刻盘链接格式
func convertJikepanLinks(links []JikepanLink) []pansou.Link {
	var result []pansou.Link
	for _, link := range links {
		linkType := convertJikepanLinkType(link.Service)
		if linkType != "" {
			result = append(result, pansou.Link{
				Type:     linkType,
				URL:      link.Link,
				Password: link.Pwd,
			})
		}
	}
	return result
}

// convertJikepanLinkType 转换链接类型
func convertJikepanLinkType(service string) string {
	service = strings.ToLower(service)
	switch service {
	case "baidu":
		return "baidu"
	case "aliyun":
		return "aliyun"
	case "xunlei":
		return "xunlei"
	case "quark":
		return "quark"
	case "189cloud":
		return "tianyi"
	case "115":
		return "115"
	case "123":
		return "123"
	case "weiyun":
		return "weiyun"
	case "pikpak":
		return "pikpak"
	case "lanzou":
		return "lanzou"
	case "jianguoyun":
		return "jianguoyun"
	case "caiyun":
		return "mobile"
	case "ed2k":
		return "ed2k"
	case "magnet":
		return "magnet"
	default:
		return ""
	}
}

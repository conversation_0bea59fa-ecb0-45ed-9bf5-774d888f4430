# 产品和会员系统

这是一个集成了产品购买和会员管理的完整系统，支持物理商品和会员商品的销售，与现有的支付系统和积分系统完全集成。

## 系统特性

### 产品管理
- 支持两种商品类型：实物商品和会员商品
- 完整的商品信息管理（名称、描述、价格、库存等）
- 商品分类和标签管理
- 商品状态管理（草稿、上架、下架、删除）
- 商品销量和浏览量统计

### 会员系统
- 两种会员类型：基础会员（Basic）和高级会员（Pro）
- 灵活的时长单位：月、季度、年
- 会员特权管理
- 月度积分自动发放
- VIP等级管理（与LLM和MCP服务优惠价格联动）
- 会员自动续费支持

### 积分系统集成
- 购买会员自动发放积分
- 月度定时积分发放
- 积分流水记录
- 与现有MCP工具和LLM服务积分消费联动

### 支付系统集成
- 完全集成现有微信支付和支付宝支付
- 自动订单履约
- 支付成功后自动激活会员
- 退款支持

## API接口

### 产品相关接口

#### 创建商品
```http
POST /product/create
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Pro会员月卡",
  "description": "享受Pro会员所有特权，每月赠送1000积分",
  "type": "membership",
  "category": "会员",
  "price": 2900,
  "originalPrice": 3900,
  "membershipType": "pro",
  "duration": 1,
  "durationUnit": "month",
  "monthlyPoints": 1000,
  "features": ["优先客服", "高级功能", "无限制使用"],
  "coverImage": "https://example.com/cover.jpg"
}
```

#### 获取商品列表
```http
GET /product/list?page=1&pageSize=10&type=membership
Authorization: Bearer {token}
```

#### 获取会员商品（无需认证）
```http
GET /product/membership
```

#### 购买商品
```http
POST /product/buy
Authorization: Bearer {token}
Content-Type: application/json

{
  "productId": 1,
  "quantity": 1,
  "paymentMethod": "wechat",
  "expireMinutes": 30
}
```

### 会员相关接口

#### 获取用户会员状态
```http
GET /membership/status
Authorization: Bearer {token}
```

响应示例：
```json
{
  "code": 0,
  "data": {
    "hasActiveMembership": true,
    "currentMembership": {
      "id": 1,
      "userId": 1,
      "membershipType": "pro",
      "startTime": "2024-01-01T00:00:00Z",
      "endTime": "2024-02-01T00:00:00Z",
      "monthlyPoints": 1000,
      "isActive": true,
      "remainingDays": 15,
      "vipLevel": 2
    },
    "vipLevel": 2,
    "totalPoints": 2500,
    "monthlyPoints": 1000,
    "nextPointTime": "2024-01-15T00:00:00Z"
  },
  "msg": "获取成功"
}
```

#### 获取用户会员列表
```http
GET /membership/list?page=1&pageSize=10
Authorization: Bearer {token}
```

### 管理员接口

#### 手动发放月度积分
```http
POST /membership/admin/grant-points
Authorization: Bearer {token}
```

#### 更新过期会员状态
```http
POST /membership/admin/update-expired
Authorization: Bearer {token}
```

#### 获取所有会员列表
```http
GET /membership/admin/list?page=1&pageSize=10&userId=1&membershipType=pro
Authorization: Bearer {token}
```

## 数据库表结构

### products（商品表）
- 商品基本信息（名称、描述、价格、库存等）
- 会员商品专用字段（会员类型、时长、月积分等）
- 实物商品专用字段（重量、尺寸、运费等）
- 统计字段（销量、浏览量）

### user_memberships（用户会员表）
- 用户会员关系
- 会员时间管理（开始时间、结束时间）
- 积分管理（月积分、总积分、发放时间）
- 状态管理（激活、过期、暂停、取消）

### order_items（订单项表）
- 关联支付订单和商品
- 商品详情快照
- 会员商品专用信息
- 实物商品物流信息

## 定时任务

### 会员积分发放（30天周期）
- 执行时间：每天凌晨1点
- 功能：检查并为符合条件的会员发放积分（从购买日期起每30天发放一次）
- Cron表达式：`0 0 1 * * ?`
- 逻辑：基于每个用户的购买日期（start_time）或上次积分发放时间（last_point_time），每满30天自动发放一次

### 过期会员状态更新
- 执行时间：每天凌晨2点
- 功能：更新过期会员状态，调整用户VIP等级
- Cron表达式：`0 0 2 * * ?`

## 业务流程

### 购买会员流程
1. 用户选择会员商品
2. 提交购买请求
3. 创建支付订单和订单项
4. 跳转到支付页面
5. 用户完成支付
6. 支付平台回调通知
7. 系统验证支付结果
8. 激活/延长会员
9. 发放首次积分
10. 更新用户VIP等级

### 会员积分发放流程（30天周期）
1. 定时任务每天触发检查
2. 查询激活状态且未过期的会员
3. 对每个会员检查：
   - 如果从未发放积分：检查距购买日期是否满30天
   - 如果已发放过积分：检查距上次发放是否满30天
4. 为符合条件的会员发放积分
5. 记录积分流水
6. 更新会员最后积分发放时间

### VIP等级管理
- VIP 0：普通用户
- VIP 1：基础会员（basic）
- VIP 2：高级会员（pro）

VIP等级影响：
- LLM服务调用价格优惠
- MCP工具使用权限
- 客服优先级
- 功能限制解除

## 配置说明

系统配置在 `config.yaml` 中的支付配置部分，使用现有的微信支付和支付宝配置。

## 扩展开发

### 添加新的会员类型
1. 在 `model/product/product.go` 中添加新的 `MembershipType`
2. 在 `user_membership.go` 中的 `GetVipLevel()` 方法中添加对应的VIP等级
3. 更新支付成功处理逻辑中的VIP等级映射

### 自定义积分发放规则
在 `service/product/membership_service.go` 中的 `GrantMonthlyPoints()` 方法中修改查询条件和发放逻辑。

### 添加新的商品类型
1. 在 `ProductType` 中添加新类型
2. 在购买流程中添加对应的处理逻辑
3. 在支付成功回调中添加履约逻辑

## 注意事项

1. **循环依赖**：支付服务直接处理产品履约逻辑，避免了与产品服务的循环依赖
2. **事务安全**：所有会员激活和积分操作都在数据库事务中进行
3. **幂等性**：支付回调具有防重复处理机制
4. **错误处理**：完整的错误日志和异常处理
5. **性能优化**：支付成功后的业务处理采用异步goroutine

## 测试建议

1. 测试支付流程的完整性
2. 测试会员激活和延期逻辑
3. 测试积分发放的准确性
4. 测试VIP等级更新
5. 测试定时任务的执行
6. 测试异常情况的处理（如支付失败、重复回调等） 
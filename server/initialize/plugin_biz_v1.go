package initialize

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/email"
	aliSms "github.com/flipped-aurora/gin-vue-admin/server/plugin/sms/aliyun_sms"
	tencentSms "github.com/flipped-aurora/gin-vue-admin/server/plugin/sms/tencent_sms"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/plugin"
	"github.com/gin-gonic/gin"
)

func PluginInit(group *gin.RouterGroup, Plugin ...plugin.Plugin) {
	for i := range Plugin {
		fmt.Println(Plugin[i].RouterPath(), "注册开始!")
		PluginGroup := group.Group(Plugin[i].RouterPath())
		Plugin[i].Register(PluginGroup)
		fmt.Println(Plugin[i].RouterPath(), "注册成功!")
	}
}

func bizPluginV1(group ...*gin.RouterGroup) {
	private := group[0]
	public := group[1]
	//  添加跟角色挂钩权限的插件 示例 本地示例模式于在线仓库模式注意上方的import 可以自行切换 效果相同
	PluginInit(private, email.CreateEmailPlug(
		global.GVA_CONFIG.Email.To,
		global.GVA_CONFIG.Email.From,
		global.GVA_CONFIG.Email.Host,
		global.GVA_CONFIG.Email.Secret,
		global.GVA_CONFIG.Email.Nickname,
		global.GVA_CONFIG.Email.Port,
		global.GVA_CONFIG.Email.IsSSL,
	))

	// 本插件可以采用gva的配置文件 也可以直接写死内容作为配置 建议为gva添加配置文件结构 然后将配置传入
	PluginInit(public, tencentSms.CreateTencentSmsPlug(global.GVA_CONFIG.TencentSMS.SecretID, global.GVA_CONFIG.TencentSMS.SecretKey, global.GVA_CONFIG.TencentSMS.AppID, global.GVA_CONFIG.TencentSMS.SignName))

	// 添加阿里云短信插件
	PluginInit(public, aliSms.CreateAliSmsPlug(global.GVA_CONFIG.AliSms.AccessKeyId, global.GVA_CONFIG.AliSms.AccessSecret, global.GVA_CONFIG.AliSms.SignName))

	holder(public, private)
}

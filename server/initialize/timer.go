package initialize

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/task"

	"github.com/robfig/cron/v3"

	mcpapi "github.com/flipped-aurora/gin-vue-admin/server/api/v1/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"go.uber.org/zap"
)

func Timer() {
	go func() {
		var option []cron.Option
		option = append(option, cron.WithSeconds())
		// 清理DB定时任务
		_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", "@daily", func() {
			err := task.ClearTable(global.GVA_DB) // 定时任务方法定在task文件包中
			if err != nil {
				fmt.Println("timer error:", err)
			}
		}, "定时清理数据库【日志，黑名单】内容", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		// 月度积分发放定时任务 - 每天凌晨1点执行，检查需要发放积分的用户
		_, err = global.GVA_Timer.AddTaskByFunc("GrantMonthlyPoints", "0 0 1 * * ?", func() {
			err := task.GrantMonthlyPoints()
			if err != nil {
				fmt.Println("grant monthly points error:", err)
			}
		}, "检查并发放会员积分（每30天周期）", option...)
		if err != nil {
			fmt.Println("add grant monthly points timer error:", err)
		}

		// 过期会员状态更新定时任务 - 每天凌晨2点执行
		_, err = global.GVA_Timer.AddTaskByFunc("UpdateExpiredMemberships", "0 0 2 * * ?", func() {
			err := task.UpdateExpiredMemberships()
			if err != nil {
				fmt.Println("update expired memberships error:", err)
			}
		}, "更新过期会员状态", option...)
		if err != nil {
			fmt.Println("add update expired memberships timer error:", err)
		}

		// 每日免费积分刷新定时任务 - 每天凌晨0点执行
		_, err = global.GVA_Timer.AddTaskByFunc("RefreshDailyFreePoints", "0 0 0 * * ?", func() {
			err := task.RefreshDailyFreePoints()
			if err != nil {
				fmt.Println("refresh daily free points error:", err)
			}
		}, "每日免费积分刷新", option...)
		if err != nil {
			fmt.Println("add refresh daily free points timer error:", err)
		}

		// 每天凌晨4点执行，刷新 node 工具、删除缓存并调用 download.sh
		_, err = global.GVA_Timer.AddTaskByFunc("RefreshNodeTools", "0 0 4 * * ?", func() {
			var serverKeys []mcprouter.Serverkey
			err := global.GVA_DB.Where("server_command LIKE ?", "node%").Find(&serverKeys).Error
			if err != nil {
				fmt.Println("query server_keys error:", err)
				return
			}

			fmt.Println("[定时任务] 开始刷新 node 工具、删除缓存并调用 download.sh，总数：", len(serverKeys))
			for _, sk := range serverKeys {
				fmt.Printf("[定时任务] 处理 server_key: %s, server_uuid: %s\n", sk.ServerKey, sk.ServerUUID)
				// 1. 查找对应的 project（假设 server_uuid 关联 project uuid）
				var project mcp.Projects
				err := global.GVA_DB.Where("uuid = ?", sk.ServerUUID).First(&project).Error
				if err != nil {
					fmt.Printf("[定时任务] find project for server_key %s error: %v\n", sk.ServerKey, err)
					continue
				}

				// 2. 刷新工具
				fmt.Printf("[定时任务] 刷新工具，project: %s\n", project.UUID)
				tx := global.GVA_DB.Begin()
				// 在上下文中设置跳过 projects.UpdatedAt 的标记
				ctx := context.WithValue(context.Background(), "skipProjectUpdatedAt", true)
				tx = tx.WithContext(ctx)
				tools, err := mcpapi.FetchToolsListTx(tx, &project)
				if err != nil {
					tx.Rollback()
					fmt.Printf("[定时任务] fetch tools for project %s error: %v\n", project.UUID, err)
					continue
				}
				if err := mcpapi.InsertToolsForProjectTx(tx, &project, tools); err != nil {
					tx.Rollback()
					fmt.Printf("[定时任务] insert tools for project %s error: %v\n", project.UUID, err)
					continue
				}
				tx.Commit()
				fmt.Printf("[定时任务] 刷新工具成功，project: %s, 工具数: %d\n", project.UUID, len(tools))

				// 3. 删除缓存（kill 相关 server 进程，逻辑同 projects.go）
				if strings.Contains(project.CallMethod, "online") {
					fmt.Printf("[定时任务] 删除缓存，尝试 kill 进程，project: %s\n", project.UUID)
					go func(serverKey string) {
						pids, err := utils.FindProcessPIDsByServerKey(serverKey)
						if err == nil {
							for _, pid := range pids {
								errKill := utils.KillProcess(pid)
								if errKill != nil {
									fmt.Printf("[定时任务] kill 进程 %d 失败: %v\n", pid, errKill)
								} else {
									fmt.Printf("[定时任务] kill 进程 %d 成功\n", pid)
								}
							}
						} else {
							fmt.Printf("[定时任务] 查找进程失败: %v\n", err)
						}
					}(project.UUID)
				}

				// 4. 调用 download.sh 脚本
				fmt.Printf("[定时任务] 调用 download.sh，server_key: %s\n", sk.ServerKey)
				cmd := exec.Command("/bin/bash", global.GVA_CONFIG.MCP.DownloadScriptPath, sk.ServerKey)
				output, err := cmd.CombinedOutput()
				if err != nil {
					fmt.Printf("[定时任务] run download.sh for %s error: %v, output: %s\n", sk.ServerKey, err, string(output))
				} else {
					fmt.Printf("[定时任务] run download.sh for %s success, output: %s\n", sk.ServerKey, string(output))
				}
			}
			fmt.Println("[定时任务] node 工具刷新任务全部完成！")
		}, "刷新 node 工具、删除缓存并调用 download.sh", option...)
		if err != nil {
			fmt.Println("add refresh node tools timer error:", err)
		}

		// MCP 连接监控定时任务 - 只在生产环境执行
		if isProductionEnvironment() {
			_, err = global.GVA_Timer.AddTaskByFunc("MonitorMCPConnections", "0 0 * * * ?", func() {
				err := monitorMCPConnections()
				if err != nil {
					fmt.Println("monitor MCP connections error:", err)
				}
			}, "监控托管 MCP 连接状态", option...)
			if err != nil {
				fmt.Println("add monitor MCP connections timer error:", err)
			} else {
				fmt.Println("MCP 连接监控定时任务已启动（生产环境）")
			}
		} else {
			fmt.Println("跳过 MCP 连接监控定时任务（仅在生产环境执行）")
		}

		// 其他定时任务定在这里 参考上方使用方法

		//_, err := global.GVA_Timer.AddTaskByFunc("定时任务标识", "corn表达式", func() {
		//	具体执行内容...
		//  ......
		//}, option...)
		//if err != nil {
		//	fmt.Println("add timer error:", err)
		//}
	}()
}

// 飞书机器人通知结构体
type FeishuMessage struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
}

// 发送飞书机器人通知
func sendFeishuNotification(message string) error {
	webhookURL := "https://open.feishu.cn/open-apis/bot/v2/hook/215def92-0f5f-45f8-8a69-7376b62043be"

	msg := FeishuMessage{
		MsgType: "text",
	}
	msg.Content.Text = message

	jsonData, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	global.GVA_LOG.Info("飞书通知发送", zap.String("message", message), zap.Int("status", resp.StatusCode))
	return nil
}

// 监控托管 MCP 连接状态
func monitorMCPConnections() error {
	global.GVA_LOG.Info("[定时任务] 开始监控托管 MCP 连接状态")

	// 查询所有托管的 MCP 项目（base_url 不为空且已启用，排除 baidu-youxuan）
	var projects []mcp.Projects
	err := global.GVA_DB.Where("base_url != '' AND base_url IS NOT NULL AND is_enabled = 1 AND status = 'created' AND uuid != 'baidu-youxuan'").Find(&projects).Error
	if err != nil {
		global.GVA_LOG.Error("查询托管 MCP 项目失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("[定时任务] 找到托管 MCP 项目", zap.Int("count", len(projects)))

	var failedConnections []string
	token := "70101873-00ce-4115-b3d3-51495d6b77e2"
	domain := "https://www.mcpcn.cc"

	for _, project := range projects {
		// 构造连接URL - 支持SSE和HTTP两种方式
		var sseURL, httpURL string
		var connectionType string

		// 检查项目是否有proxy_sse_url或proxy_http_url配置
		if project.ProxySseUrl != "" {
			sseURL = project.ProxySseUrl
			connectionType = "SSE(代理)"
		} else if project.ProxyHttpUrl != "" {
			httpURL = project.ProxyHttpUrl
			connectionType = "HTTP(代理)"
		} else {
			// 默认构造SSE URL
			sseURL = fmt.Sprintf("%s%s/%s", domain, project.BaseUrl, token)
			connectionType = "SSE(默认)"
		}

		global.GVA_LOG.Info("[定时任务] 测试 MCP 连接",
			zap.String("project", project.Name),
			zap.String("type", connectionType),
			zap.String("sseUrl", sseURL),
			zap.String("httpUrl", httpURL))

		// 创建临时 server_keys 记录用于测试
		tempServerKey := mcprouter.Serverkey{
			ServerKey:         project.UUID,
			ServerUUID:        project.UUID,
			ServerName:        project.Name,
			SseUrl:            sseURL,
			StreamableHttpUrl: httpURL,
			Status:            "created",
			EnvJson:           project.EnvReal,
			CreatedAt:         time.Now(),
		}

		global.GVA_LOG.Info("[定时任务] 创建临时 server_keys",
			zap.String("uuid", project.UUID),
			zap.String("connectionType", connectionType),
			zap.String("sseUrl", sseURL),
			zap.String("httpUrl", httpURL))

		// 创建临时项目对象用于测试
		testProject := mcp.Projects{
			UUID:       project.UUID,
			Name:       project.Name,
			CallMethod: "online",
		}

		// 在事务中先删除可能存在的旧记录，然后创建临时记录并测试
		tx := global.GVA_DB.Begin()

		// 删除可能存在的旧记录
		tx.Where("server_uuid = ?", project.UUID).Delete(&mcprouter.Serverkey{})

		// 创建临时记录
		createResult := tx.Create(&tempServerKey)
		if createResult.Error != nil {
			global.GVA_LOG.Error("[定时任务] 创建临时 server_keys 失败", zap.Error(createResult.Error))
			tx.Rollback()
			failedConnections = append(failedConnections, fmt.Sprintf("• %s (%s): 创建临时记录失败", project.Name, project.UUID))
			continue
		}

		global.GVA_LOG.Info("[定时任务] 临时 server_keys 创建成功", zap.Int64("id", tempServerKey.ID))

		// 尝试获取工具列表，添加重试机制
		var tools []interface{}
		var err error
		maxRetries := config.MAX_RETRY_ATTEMPTS
		retryDelay := config.INITIAL_RETRY_DELAY

		for attempt := 1; attempt <= maxRetries; attempt++ {
			if attempt > 1 {
				global.GVA_LOG.Info("[定时任务] 重试MCP连接",
					zap.String("project", project.Name),
					zap.String("uuid", project.UUID),
					zap.Int("attempt", attempt),
					zap.Duration("delay", retryDelay))
				time.Sleep(retryDelay)
				// 递增重试延迟
				retryDelay = time.Duration(float64(retryDelay) * config.RETRY_DELAY_MULTIPLIER)
			}

			tools, err = mcpapi.FetchToolsListTx(tx, &testProject)
			if err == nil {
				break // 成功则跳出重试循环
			}

			if attempt < maxRetries {
				global.GVA_LOG.Warn("[定时任务] MCP连接失败，准备重试",
					zap.String("project", project.Name),
					zap.String("uuid", project.UUID),
					zap.Int("attempt", attempt),
					zap.Error(err))
			}
		}

		tx.Rollback() // 测试完成后回滚，不保存数据

		if err != nil {
			// 确定使用的URL用于错误日志
			testURL := sseURL
			if testURL == "" {
				testURL = httpURL
			}
			global.GVA_LOG.Error("[定时任务] MCP 连接失败",
				zap.String("project", project.Name),
				zap.String("uuid", project.UUID),
				zap.String("connectionType", connectionType),
				zap.String("url", testURL),
				zap.Error(err))
			failedConnections = append(failedConnections, fmt.Sprintf("• %s (%s) [%s]: %s", project.Name, project.UUID, connectionType, err.Error()))
		} else {
			global.GVA_LOG.Info("[定时任务] MCP 连接成功",
				zap.String("project", project.Name),
				zap.String("connectionType", connectionType),
				zap.Int("tools", len(tools)))
		}

		// 避免请求过于频繁
		time.Sleep(2 * time.Second)
	}

	// 统计结果
	successCount := len(projects) - len(failedConnections)
	global.GVA_LOG.Info("[定时任务] 监控结果统计",
		zap.Int("total", len(projects)),
		zap.Int("success", successCount),
		zap.Int("failed", len(failedConnections)))

	// 分类错误类型
	var criticalErrors []string
	var minorErrors []string

	for _, failure := range failedConnections {
		if strings.Contains(failure, "SSE未获取到endpoint") ||
			strings.Contains(failure, "未收到initialize响应") {
			criticalErrors = append(criticalErrors, failure)
		} else if strings.Contains(failure, "Invalid request parameters") {
			minorErrors = append(minorErrors, failure)
		} else {
			criticalErrors = append(criticalErrors, failure)
		}
	}

	global.GVA_LOG.Info("[定时任务] 错误分类",
		zap.Int("critical", len(criticalErrors)),
		zap.Int("minor", len(minorErrors)))

	// 只有严重错误才发送飞书通知
	if len(criticalErrors) > 0 {
		global.GVA_LOG.Info("[定时任务] 发现严重连接问题", zap.Int("count", len(criticalErrors)))

		message := fmt.Sprintf("🚨 MCP 连接监控告警\n\n检测时间: %s\n总计: %d 个项目\n✅ 成功: %d 个\n❌ 严重错误: %d 个\n⚠️ 轻微错误: %d 个\n\n严重错误详情:\n%s",
			time.Now().Format("2006-01-02 15:04:05"),
			len(projects),
			successCount,
			len(criticalErrors),
			len(minorErrors),
			strings.Join(criticalErrors, "\n"))

		err := sendFeishuNotification(message)
		if err != nil {
			global.GVA_LOG.Error("[定时任务] 发送飞书通知失败", zap.Error(err))
		} else {
			global.GVA_LOG.Info("[定时任务] 飞书通知发送成功")
		}
	} else if len(minorErrors) > 0 {
		global.GVA_LOG.Info("[定时任务] 仅发现轻微问题，不发送通知", zap.Int("count", len(minorErrors)))
	} else {
		global.GVA_LOG.Info("[定时任务] 所有 MCP 连接正常")
	}

	return nil
}

// isProductionEnvironment 检测是否为生产环境
func isProductionEnvironment() bool {
	// 方法1: 检查命令行参数 --env=prod
	for i, arg := range os.Args {
		if arg == "--env" && i+1 < len(os.Args) && os.Args[i+1] == "prod" {
			return true
		}
		if strings.HasPrefix(arg, "--env=prod") {
			return true
		}
	}

	// 方法2: 检查 flag 包解析的结果
	if flag.Lookup("env") != nil {
		if envFlag := flag.Lookup("env"); envFlag.Value.String() == "prod" {
			return true
		}
	}

	// 方法3: 检查配置文件名（如果使用了 config.prod.yaml）
	if global.GVA_VP != nil {
		configFile := global.GVA_VP.ConfigFileUsed()
		if strings.Contains(configFile, "config.prod.yaml") {
			return true
		}
	}

	// 方法4: 检查环境变量
	if env := os.Getenv("ENV"); env == "prod" {
		return true
	}

	return false
}

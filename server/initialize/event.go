package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mq"
	"github.com/flipped-aurora/gin-vue-admin/server/service/product"
	"go.uber.org/zap"
)

// InitEvents 初始化事件系统
func InitEvents() {
	// 初始化产品服务事件监听器
	product.InitEventListeners()

	// 初始化Redis MQ服务
	if global.GVA_REDIS != nil {
		mq.InitRedisMQ()

		// 初始化项目消息处理器
		mcp.InitProjectMessageHandler()

		// 订阅项目更新消息
		if mq.GlobalRedisMQ != nil && mcp.GlobalProjectMessageHandler != nil {
			err := mq.GlobalRedisMQ.SubscribeChannel("project.update",
				mcp.GlobalProjectMessageHandler.HandleProjectUpdateMessage)
			if err != nil {
				global.GVA_LOG.Error("订阅项目更新消息失败", zap.Error(err))
			} else {
				global.GVA_LOG.Info("成功订阅项目更新消息")
			}
		}
	} else {
		global.GVA_LOG.Warn("Redis未初始化，跳过Redis MQ服务初始化")
	}
}

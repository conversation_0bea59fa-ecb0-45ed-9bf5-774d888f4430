# Redis MQ 发布订阅系统实现指南

## 🎯 实现目标

实现一个基于Redis MQ的发布订阅系统，当UpdateProjects时向每个服务发布消息，服务器接收到后执行kill server进程的操作。

## ✅ 已完成的实现

### 1. 核心组件

#### Redis MQ 服务 (`server/service/mq/redis_mq.go`)
- ✅ `RedisMQService` - 核心Redis消息队列服务
- ✅ `PublishMessage` - 通用消息发布方法
- ✅ `SubscribeChannel` - 频道订阅方法（支持自动重连）
- ✅ `PublishProjectUpdate` - 专用项目更新消息发布

#### 项目消息处理器 (`server/service/mcp/project_message_handler.go`)
- ✅ `ProjectMessageHandler` - 项目更新消息处理器
- ✅ `HandleProjectUpdateMessage` - 解析消息并执行kill进程操作

#### 事件定义扩展 (`server/global/event.go`)
- ✅ `ProjectUpdateEvent` - 项目更新事件数据结构
- ✅ `EventTypeProjectUpdate` - 事件类型常量

### 2. 系统集成

#### 修改UpdateProjects API (`server/api/v1/mcp/projects.go`)
- ✅ 替换原有本地kill进程逻辑
- ✅ 通过Redis MQ发布项目更新消息
- ✅ 保留容错机制（Redis不可用时回退到本地处理）

#### 初始化系统 (`server/initialize/event.go`)
- ✅ 初始化Redis MQ服务
- ✅ 初始化项目消息处理器
- ✅ 自动订阅`project.update`频道

#### 启动顺序优化 (`server/main.go`, `server/core/server.go`)
- ✅ 确保Redis在事件系统之前初始化
- ✅ 避免重复初始化Redis

### 3. 测试和演示

#### 演示程序 (`server/demo/redis_mq_demo.go`)
- ✅ 完整的发布订阅演示
- ✅ 消息处理逻辑演示
- ✅ 验证系统正常工作

#### 测试套件 (`server/test/`)
- ✅ Redis连接测试
- ✅ MQ初始化测试
- ✅ 发布订阅功能测试

#### 管理工具 (`server/cmd/mq_test.go`)
- ✅ 交互式MQ测试工具
- ✅ 支持手动发布测试消息

## 🚀 系统工作流程

### 启动时
```
1. 初始化Redis连接
2. 初始化Redis MQ服务
3. 初始化项目消息处理器
4. 订阅 project.update 频道
5. 系统准备就绪
```

### 项目更新时
```
1. 调用 UpdateProjects API
2. 检查 CallMethod 是否包含 "server"
3. 如果是托管服务：
   - 发布项目更新消息到 Redis
   - 所有订阅的服务器收到消息
   - 每个服务器执行 kill 进程操作
4. 如果不是托管服务：
   - 跳过进程终止操作
```

## 📋 使用方法

### 1. 确保Redis配置正确

```yaml
# config.yaml
redis:
  name: cache
  addr: localhost:6379
  password: ""
  db: 0

system:
  useRedis: true
```

### 2. 启动服务

```bash
cd server
go run main.go
```

### 3. 测试系统

```bash
# 运行演示程序
cd server/demo
go run redis_mq_demo.go

# 运行交互式测试工具
cd server
go run main.go mq-test
```

### 4. 监控Redis消息

```bash
# 连接Redis
redis-cli

# 监控所有消息
MONITOR

# 查看订阅者数量
PUBSUB NUMSUB project.update
```

## 🔧 容错机制

### Redis连接失败
- 系统自动回退到原有的本地kill进程逻辑
- 记录警告日志但不影响核心功能

### 消息处理失败
- 记录错误日志
- 不影响其他消息的处理
- 支持自动重连机制

### 订阅中断
- 自动检测连接中断
- 5秒后自动重新订阅
- 确保服务的高可用性

## 📊 性能特点

- **低延迟**: Redis发布订阅延迟通常在毫秒级
- **高可靠**: 支持自动重连和容错机制
- **可扩展**: 支持多服务器订阅同一频道
- **轻量级**: 最小化资源占用

## 🔍 日志监控

系统会记录以下关键操作：
- Redis MQ服务初始化
- 消息发布成功/失败
- 消息接收和处理
- 进程查找和终止操作
- 连接状态变化

## 🎉 验证结果

演示程序成功运行，输出显示：
- ✅ Redis连接正常
- ✅ 消息发布成功（订阅者数量: 1）
- ✅ 消息接收正常
- ✅ 消息解析正确
- ✅ 托管服务类型判断准确
- ✅ 模拟kill进程操作执行

## 🚀 部署建议

1. **生产环境**: 使用Redis集群确保高可用
2. **监控**: 设置Redis和应用程序监控
3. **日志**: 配置日志收集和分析
4. **测试**: 定期运行测试确保系统正常
5. **备份**: 定期备份Redis数据（如需要）

系统已经完全实现并验证通过！🎉

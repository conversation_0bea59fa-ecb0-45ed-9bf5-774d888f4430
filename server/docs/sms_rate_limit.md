# 短信验证码防爆处理文档

## 概述

为了防止恶意消耗短信资源，我们为 `SendSMSCode` 接口实现了多层防爆处理机制。

## 防爆策略

### 1. IP 级别限流
- **目的**: 防止单个 IP 地址频繁请求短信验证码
- **默认限制**: 每小时 10 次
- **配置项**: `sms-limit-count-ip`, `sms-limit-time-ip`

### 2. 手机号级别限流
- **目的**: 防止对同一手机号频繁发送验证码
- **默认限制**: 每小时 5 次
- **配置项**: `sms-limit-count-phone`, `sms-limit-time-phone`

### 3. 验证码重复发送检查
- **目的**: 防止在短时间内重复发送验证码
- **机制**: 如果验证码还有超过 4 分钟的有效期，则不允许重新发送

## 配置说明

在 `config.yaml` 中添加以下配置：

```yaml
system:
  # 短信限流配置
  sms-limit-count-ip: 10      # 单个IP每小时最多发送10条短信
  sms-limit-time-ip: 3600     # IP限流时间窗口(秒)
  sms-limit-count-phone: 5    # 单个手机号每小时最多发送5条短信
  sms-limit-time-phone: 3600  # 手机号限流时间窗口(秒)
```

## 实现细节

### 中间件实现
- 文件位置: `server/middleware/sms_limit.go`
- 功能: 提供双重限流检查（IP + 手机号）
- 特点: 
  - 支持自定义限流策略
  - Redis 存储限流状态
  - 优雅的错误提示

### 接口增强
- 文件位置: `server/api/v1/system/sys_user.go`
- 增强内容:
  - 详细的安全日志记录
  - 手机号脱敏处理
  - 验证码重复发送检查
  - 更完善的错误处理

### 路由配置
- 文件位置: `server/router/system/sys_base.go`
- 变更: 为 `sendSMSCode` 路由添加限流中间件

## 安全日志

系统会记录以下安全相关的日志：

### 正常操作日志
```
SMS code request received - 记录短信发送请求
SMS code sent successfully - 记录成功发送
```

### 安全警告日志
```
SMS request parameter parsing failed - 参数解析失败
SMS request with empty phone - 空手机号请求
SMS request with invalid phone format - 无效手机号格式
SMS request with invalid sms type - 无效短信类型
SMS register request for existing phone - 已注册手机号注册请求
SMS reset password request for non-existing phone - 未注册手机号重置密码请求
SMS code request too frequent - 验证码请求过于频繁
```

### 限流日志
```
SMS IP limit exceeded - IP限流触发
SMS phone limit exceeded - 手机号限流触发
```

## 错误提示

用户在触发限流时会收到友好的错误提示：

- IP 限流: "短信发送太过频繁, 请 X 后尝试"
- 手机号限流: "短信发送太过频繁, 请 X 后尝试"
- 重复发送: "验证码发送过于频繁，请稍后再试"

## 监控建议

### 1. 关键指标监控
- 短信发送成功率
- 限流触发频率
- 异常请求模式

### 2. 告警设置
- 短时间内大量限流触发
- 短信发送失败率过高
- 异常 IP 行为

### 3. 日志分析
- 定期分析安全日志
- 识别恶意攻击模式
- 优化限流策略

## 测试

### 单元测试
运行测试文件: `server/test/sms_limit_test.go`

```bash
go test ./test -v -run TestSmsLimitMiddleware
```

### 手动测试
1. 正常发送短信验证码
2. 快速连续发送，验证限流生效
3. 使用不同 IP 对同一手机号发送，验证手机号限流
4. 检查日志记录是否正确

## 性能影响

- **Redis 查询**: 每次请求需要 2-4 次 Redis 操作
- **内存占用**: 限流状态存储在 Redis 中，占用极少
- **响应时间**: 增加约 1-5ms 的处理时间

## 扩展建议

### 1. 动态限流
根据时间段、用户类型等动态调整限流策略

### 2. 白名单机制
为特定 IP 或用户提供白名单功能

### 3. 验证码复杂度
根据风险等级调整验证码复杂度

### 4. 多渠道验证
结合图形验证码、滑块验证等多种验证方式

## 故障排除

### 1. Redis 连接问题
- 检查 Redis 配置和连接状态
- 限流功能在 Redis 不可用时会自动跳过

### 2. 限流过于严格
- 调整配置参数
- 检查是否有代理或负载均衡影响 IP 识别

### 3. 日志过多
- 调整日志级别
- 使用日志轮转和清理策略

## 更新历史

- v1.0: 初始实现，支持 IP 和手机号双重限流
- 增加详细的安全日志记录
- 添加验证码重复发送检查
- 完善错误提示和用户体验

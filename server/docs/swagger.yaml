definitions:
  common.JSONMap:
    additionalProperties: true
    type: object
  config.AliyunOSS:
    properties:
      access-key-id:
        type: string
      access-key-secret:
        type: string
      base-path:
        type: string
      bucket-name:
        type: string
      bucket-url:
        type: string
      endpoint:
        type: string
    type: object
  config.Autocode:
    properties:
      ai-path:
        type: string
      module:
        type: string
      root:
        type: string
      server:
        type: string
      web:
        type: string
    type: object
  config.AwsS3:
    properties:
      base-url:
        type: string
      bucket:
        type: string
      disable-ssl:
        type: boolean
      endpoint:
        type: string
      path-prefix:
        type: string
      region:
        type: string
      s3-force-path-style:
        type: boolean
      secret-id:
        type: string
      secret-key:
        type: string
    type: object
  config.CORS:
    properties:
      mode:
        type: string
      whitelist:
        items:
          $ref: '#/definitions/config.CORSWhitelist'
        type: array
    type: object
  config.CORSWhitelist:
    properties:
      allow-credentials:
        type: boolean
      allow-headers:
        type: string
      allow-methods:
        type: string
      allow-origin:
        type: string
      expose-headers:
        type: string
    type: object
  config.Captcha:
    properties:
      img-height:
        description: 验证码高度
        type: integer
      img-width:
        description: 验证码宽度
        type: integer
      key-long:
        description: 验证码长度
        type: integer
      open-captcha:
        description: 防爆破验证码开启此数，0代表每次登录都需要验证码，其他数字代表错误密码此数，如3代表错误三次后出现验证码
        type: integer
      open-captcha-timeout:
        description: 防爆破验证码超时时间，单位：s(秒)
        type: integer
    type: object
  config.CloudflareR2:
    properties:
      access-key-id:
        type: string
      account-id:
        type: string
      base-url:
        type: string
      bucket:
        type: string
      path:
        type: string
      secret-access-key:
        type: string
    type: object
  config.DiskList:
    properties:
      mount-point:
        type: string
    type: object
  config.Excel:
    properties:
      dir:
        type: string
    type: object
  config.HuaWeiObs:
    properties:
      access-key:
        type: string
      bucket:
        type: string
      endpoint:
        type: string
      path:
        type: string
      secret-key:
        type: string
    type: object
  config.JWT:
    properties:
      buffer-time:
        description: 缓冲时间
        type: string
      expires-time:
        description: 过期时间
        type: string
      issuer:
        description: 签发者
        type: string
      signing-key:
        description: jwt签名
        type: string
    type: object
  config.Local:
    properties:
      path:
        description: 本地文件访问路径
        type: string
      store-path:
        description: 本地文件存储路径
        type: string
    type: object
  config.Minio:
    properties:
      access-key-id:
        type: string
      access-key-secret:
        type: string
      base-path:
        type: string
      bucket-name:
        type: string
      bucket-url:
        type: string
      endpoint:
        type: string
      use-ssl:
        type: boolean
    type: object
  config.Mongo:
    properties:
      auth-source:
        description: 验证数据库
        type: string
      coll:
        description: collection name
        type: string
      connect-timeout-ms:
        description: 连接超时时间
        type: integer
      database:
        description: database name
        type: string
      hosts:
        description: 主机列表
        items:
          $ref: '#/definitions/config.MongoHost'
        type: array
      is-zap:
        description: 是否开启zap日志
        type: boolean
      max-pool-size:
        description: 最大连接池
        type: integer
      min-pool-size:
        description: 最小连接池
        type: integer
      options:
        description: mongodb options
        type: string
      password:
        description: 密码
        type: string
      socket-timeout-ms:
        description: socket超时时间
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  config.MongoHost:
    properties:
      host:
        description: ip地址
        type: string
      port:
        description: 端口
        type: string
    type: object
  config.Mssql:
    properties:
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      username:
        description: 数据库账号
        type: string
    type: object
  config.Mysql:
    properties:
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      username:
        description: 数据库账号
        type: string
    type: object
  config.Oracle:
    properties:
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      username:
        description: 数据库账号
        type: string
    type: object
  config.Pgsql:
    properties:
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      username:
        description: 数据库账号
        type: string
    type: object
  config.Qiniu:
    properties:
      access-key:
        description: 秘钥AK
        type: string
      bucket:
        description: 空间名称
        type: string
      img-path:
        description: CDN加速域名
        type: string
      secret-key:
        description: 秘钥SK
        type: string
      use-cdn-domains:
        description: 上传是否使用CDN上传加速
        type: boolean
      use-https:
        description: 是否使用https
        type: boolean
      zone:
        description: 存储区域
        type: string
    type: object
  config.Redis:
    properties:
      addr:
        description: 服务器地址:端口
        type: string
      clusterAddrs:
        description: 集群模式下的节点地址列表
        items:
          type: string
        type: array
      db:
        description: 单实例模式下redis的哪个数据库
        type: integer
      name:
        description: 代表当前实例的名字
        type: string
      password:
        description: 密码
        type: string
      useCluster:
        description: 是否使用集群模式
        type: boolean
    type: object
  config.Server:
    properties:
      aliyun-oss:
        $ref: '#/definitions/config.AliyunOSS'
      autocode:
        allOf:
        - $ref: '#/definitions/config.Autocode'
        description: auto
      aws-s3:
        $ref: '#/definitions/config.AwsS3'
      captcha:
        $ref: '#/definitions/config.Captcha'
      cloudflare-r2:
        $ref: '#/definitions/config.CloudflareR2'
      cors:
        allOf:
        - $ref: '#/definitions/config.CORS'
        description: 跨域配置
      db-list:
        items:
          $ref: '#/definitions/config.SpecializedDB'
        type: array
      disk-list:
        items:
          $ref: '#/definitions/config.DiskList'
        type: array
      email:
        $ref: '#/definitions/github_com_flipped-aurora_gin-vue-admin_server_config.Email'
      excel:
        $ref: '#/definitions/config.Excel'
      hua-wei-obs:
        $ref: '#/definitions/config.HuaWeiObs'
      jwt:
        $ref: '#/definitions/config.JWT'
      local:
        allOf:
        - $ref: '#/definitions/config.Local'
        description: oss
      minio:
        $ref: '#/definitions/config.Minio'
      mongo:
        $ref: '#/definitions/config.Mongo'
      mssql:
        $ref: '#/definitions/config.Mssql'
      mysql:
        allOf:
        - $ref: '#/definitions/config.Mysql'
        description: gorm
      oracle:
        $ref: '#/definitions/config.Oracle'
      pgsql:
        $ref: '#/definitions/config.Pgsql'
      qiniu:
        $ref: '#/definitions/config.Qiniu'
      redis:
        $ref: '#/definitions/config.Redis'
      redis-list:
        items:
          $ref: '#/definitions/config.Redis'
        type: array
      sqlite:
        $ref: '#/definitions/config.Sqlite'
      system:
        $ref: '#/definitions/config.System'
      tencent-cos:
        $ref: '#/definitions/config.TencentCOS'
      zap:
        $ref: '#/definitions/config.Zap'
    type: object
  config.SpecializedDB:
    properties:
      alias-name:
        type: string
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      disable:
        type: boolean
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      type:
        type: string
      username:
        description: 数据库账号
        type: string
    type: object
  config.Sqlite:
    properties:
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      username:
        description: 数据库账号
        type: string
    type: object
  config.System:
    properties:
      addr:
        description: 端口值
        type: integer
      db-type:
        description: 数据库类型:mysql(默认)|sqlite|sqlserver|postgresql
        type: string
      iplimit-count:
        type: integer
      iplimit-time:
        type: integer
      oss-type:
        description: Oss类型
        type: string
      router-prefix:
        type: string
      use-mongo:
        description: 使用mongo
        type: boolean
      use-multipoint:
        description: 多点登录拦截
        type: boolean
      use-redis:
        description: 使用redis
        type: boolean
      use-strict-auth:
        description: 使用树形角色分配模式
        type: boolean
    type: object
  config.TencentCOS:
    properties:
      base-url:
        type: string
      bucket:
        type: string
      path-prefix:
        type: string
      region:
        type: string
      secret-id:
        type: string
      secret-key:
        type: string
    type: object
  config.Zap:
    properties:
      director:
        description: 日志文件夹
        type: string
      encode-level:
        description: 编码级
        type: string
      format:
        description: 输出
        type: string
      level:
        description: 级别
        type: string
      log-in-console:
        description: 输出控制台
        type: boolean
      prefix:
        description: 日志前缀
        type: string
      retention-day:
        description: 日志保留天数
        type: integer
      show-line:
        description: 显示行
        type: boolean
      stacktrace-key:
        description: 栈名
        type: string
    type: object
  example.ExaAttachmentCategory:
    properties:
      ID:
        description: 主键ID
        type: integer
      children:
        items:
          $ref: '#/definitions/example.ExaAttachmentCategory'
        type: array
      createdAt:
        description: 创建时间
        type: string
      name:
        type: string
      pid:
        type: integer
      updatedAt:
        description: 更新时间
        type: string
    type: object
  example.ExaCustomer:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      customerName:
        description: 客户名
        type: string
      customerPhoneData:
        description: 客户手机号
        type: string
      sysUser:
        allOf:
        - $ref: '#/definitions/system.SysUser'
        description: 管理详情
      sysUserAuthorityID:
        description: 管理角色ID
        type: integer
      sysUserId:
        description: 管理ID
        type: integer
      updatedAt:
        description: 更新时间
        type: string
    type: object
  example.ExaFile:
    properties:
      ID:
        description: 主键ID
        type: integer
      chunkTotal:
        type: integer
      createdAt:
        description: 创建时间
        type: string
      exaFileChunk:
        items:
          $ref: '#/definitions/example.ExaFileChunk'
        type: array
      fileMd5:
        type: string
      fileName:
        type: string
      filePath:
        type: string
      isFinish:
        type: boolean
      updatedAt:
        description: 更新时间
        type: string
    type: object
  example.ExaFileChunk:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      exaFileID:
        type: integer
      fileChunkNumber:
        type: integer
      fileChunkPath:
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  example.ExaFileUploadAndDownload:
    properties:
      ID:
        description: 主键ID
        type: integer
      classId:
        description: 分类id
        type: integer
      createdAt:
        description: 创建时间
        type: string
      key:
        description: 编号
        type: string
      name:
        description: 文件名
        type: string
      tag:
        description: 文件标签
        type: string
      updatedAt:
        description: 更新时间
        type: string
      url:
        description: 文件地址
        type: string
    type: object
  github_com_flipped-aurora_gin-vue-admin_server_config.Email:
    properties:
      from:
        description: 发件人  你自己要发邮件的邮箱
        type: string
      host:
        description: 服务器地址 例如 smtp.qq.com  请前往QQ或者你要发邮件的邮箱查看其smtp协议
        type: string
      is-ssl:
        description: 是否SSL   是否开启SSL
        type: boolean
      nickname:
        description: 昵称    发件人昵称 通常为自己的邮箱
        type: string
      port:
        description: 端口     请前往QQ或者你要发邮件的邮箱查看其smtp协议 大多为 465
        type: integer
      secret:
        description: 密钥    用于登录的密钥 最好不要用邮箱密码 去邮箱smtp申请一个用于登录的密钥
        type: string
      to:
        description: 收件人:多个以英文逗号分隔 例：<EMAIL> <EMAIL> 正式开发中请把此项目作为参数使用
        type: string
    type: object
  model.Info:
    properties:
      ID:
        description: 主键ID
        type: integer
      attachments:
        description: 附件
        items:
          type: object
        type: array
      content:
        description: 内容
        type: string
      createdAt:
        description: 创建时间
        type: string
      title:
        description: 标题
        type: string
      updatedAt:
        description: 更新时间
        type: string
      userID:
        description: 作者
        type: integer
    type: object
  request.AddMenuAuthorityInfo:
    properties:
      authorityId:
        description: 角色ID
        type: integer
      menus:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
    type: object
  request.AutoCode:
    properties:
      abbreviation:
        description: Struct简称
        example: Struct简称
        type: string
      autoCreateApiToSql:
        description: 是否自动创建api
        example: false
        type: boolean
      autoCreateBtnAuth:
        description: 是否自动创建按钮权限
        example: false
        type: boolean
      autoCreateMenuToSql:
        description: 是否自动创建menu
        example: false
        type: boolean
      autoCreateResource:
        description: 是否自动创建资源标识
        example: false
        type: boolean
      autoMigrate:
        description: 是否自动迁移表结构
        example: false
        type: boolean
      businessDB:
        description: 业务数据库
        example: 业务数据库
        type: string
      description:
        description: Struct中文名称
        example: Struct中文名称
        type: string
      fields:
        items:
          $ref: '#/definitions/request.AutoCodeField'
        type: array
      generateServer:
        description: 是否生成server
        example: true
        type: boolean
      generateWeb:
        description: 是否生成web
        example: true
        type: boolean
      gvaModel:
        description: 是否使用gva默认Model
        example: false
        type: boolean
      humpPackageName:
        description: go文件名称
        example: go文件名称
        type: string
      isAdd:
        description: 是否新增
        example: false
        type: boolean
      isTree:
        description: 是否树形结构
        example: false
        type: boolean
      onlyTemplate:
        description: 是否只生成模板
        example: false
        type: boolean
      package:
        type: string
      packageName:
        description: 文件名称
        example: 文件名称
        type: string
      primaryField:
        $ref: '#/definitions/request.AutoCodeField'
      structName:
        description: Struct名称
        example: Struct名称
        type: string
      tableName:
        description: 表名
        example: 表名
        type: string
      treeJson:
        description: 展示的树json字段
        example: 展示的树json字段
        type: string
    type: object
  request.AutoCodeField:
    properties:
      checkDataSource:
        description: 是否检查数据源
        type: boolean
      clearable:
        description: 是否可清空
        type: boolean
      columnName:
        description: 数据库字段
        type: string
      comment:
        description: 数据库字段描述
        type: string
      dataSource:
        allOf:
        - $ref: '#/definitions/request.DataSource'
        description: 数据源
      dataTypeLong:
        description: 数据库字段长度
        type: string
      defaultValue:
        description: 是否必填
        type: string
      desc:
        description: 是否前端详情
        type: boolean
      dictType:
        description: 字典
        type: string
      errorText:
        description: 校验失败文字
        type: string
      excel:
        description: 是否导入/导出
        type: boolean
      fieldDesc:
        description: 中文名
        type: string
      fieldIndexType:
        description: 索引类型
        type: string
      fieldJson:
        description: FieldJson
        type: string
      fieldName:
        description: Field名
        type: string
      fieldSearchHide:
        description: 是否隐藏查询条件
        type: boolean
      fieldSearchType:
        description: 搜索条件
        type: string
      fieldType:
        description: Field数据类型
        type: string
      form:
        description: Front           bool        `json:"front"`           // 是否前端可见
        type: boolean
      primaryKey:
        description: 是否主键
        type: boolean
      require:
        description: 是否必填
        type: boolean
      sort:
        description: 是否增加排序
        type: boolean
      table:
        description: 是否前端表格列
        type: boolean
    type: object
  request.CasbinInReceive:
    properties:
      authorityId:
        description: 权限id
        type: integer
      casbinInfos:
        items:
          $ref: '#/definitions/request.CasbinInfo'
        type: array
    type: object
  request.CasbinInfo:
    properties:
      method:
        description: 方法
        type: string
      path:
        description: 路径
        type: string
    type: object
  request.ChangePasswordReq:
    properties:
      newPassword:
        description: 新密码
        type: string
      password:
        description: 密码
        type: string
    type: object
  request.DataSource:
    properties:
      association:
        description: 关联关系 1 一对一 2 一对多
        type: integer
      dbName:
        type: string
      hasDeletedAt:
        type: boolean
      label:
        type: string
      table:
        type: string
      value:
        type: string
    type: object
  request.Empty:
    type: object
  request.ExaAttachmentCategorySearch:
    properties:
      classId:
        type: integer
      keyword:
        description: 关键字
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
    type: object
  request.GetAuthorityId:
    properties:
      authorityId:
        description: 角色ID
        type: integer
    type: object
  request.GetById:
    properties:
      id:
        description: 主键ID
        type: integer
    type: object
  request.GetUserList:
    properties:
      email:
        type: string
      keyword:
        description: 关键字
        type: string
      nickName:
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      phone:
        type: string
      username:
        type: string
    type: object
  request.IdsReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  request.InitDB:
    properties:
      adminPassword:
        type: string
      dbName:
        description: 数据库名
        type: string
      dbPath:
        description: sqlite数据库文件路径
        type: string
      dbType:
        description: 数据库类型
        type: string
      host:
        description: 服务器地址
        type: string
      password:
        description: 数据库密码
        type: string
      port:
        description: 数据库连接端口
        type: string
      template:
        description: postgresql指定template
        type: string
      userName:
        description: 数据库用户名
        type: string
    required:
    - adminPassword
    - dbName
    type: object
  request.Login:
    properties:
      captcha:
        description: 验证码
        type: string
      captchaId:
        description: 验证码ID
        type: string
      password:
        description: 密码
        type: string
      username:
        description: 用户名
        type: string
    type: object
  request.PageInfo:
    properties:
      keyword:
        description: 关键字
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
    type: object
  request.Register:
    properties:
      authorityId:
        example: int 角色id
        type: string
      authorityIds:
        example: '[]uint 角色id'
        type: string
      email:
        example: 电子邮箱
        type: string
      enable:
        example: int 是否启用
        type: string
      headerImg:
        example: 头像链接
        type: string
      nickName:
        example: 昵称
        type: string
      passWord:
        example: 密码
        type: string
      phone:
        example: 电话号码
        type: string
      userName:
        example: 用户名
        type: string
    type: object
  request.SearchApiParams:
    properties:
      ID:
        description: 主键ID
        type: integer
      apiGroup:
        description: api组
        type: string
      createdAt:
        description: 创建时间
        type: string
      desc:
        description: 排序方式:升序false(默认)|降序true
        type: boolean
      description:
        description: api中文描述
        type: string
      keyword:
        description: 关键字
        type: string
      method:
        description: 方法:创建POST(默认)|查看GET|更新PUT|删除DELETE
        type: string
      orderKey:
        description: 排序
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      path:
        description: api路径
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  request.SetUserAuth:
    properties:
      authorityId:
        description: 角色ID
        type: integer
    type: object
  request.SetUserAuthorities:
    properties:
      authorityIds:
        description: 角色ID
        items:
          type: integer
        type: array
      id:
        type: integer
    type: object
  request.SysAuthorityBtnReq:
    properties:
      authorityId:
        type: integer
      menuID:
        type: integer
      selected:
        items:
          type: integer
        type: array
    type: object
  request.SysAutoCodePackageCreate:
    properties:
      desc:
        example: 描述
        type: string
      label:
        example: 展示名
        type: string
      packageName:
        example: 包名
        type: string
      template:
        example: 模版
        type: string
    type: object
  request.SysAutoHistoryRollBack:
    properties:
      deleteApi:
        description: 是否删除接口
        type: boolean
      deleteMenu:
        description: 是否删除菜单
        type: boolean
      deleteTable:
        description: 是否删除表
        type: boolean
      id:
        description: 主键ID
        type: integer
    type: object
  response.Email:
    properties:
      body:
        description: 邮件内容
        type: string
      subject:
        description: 邮件标题
        type: string
      to:
        description: 邮件发送给谁
        type: string
    type: object
  response.ExaCustomerResponse:
    properties:
      customer:
        $ref: '#/definitions/example.ExaCustomer'
    type: object
  response.ExaFileResponse:
    properties:
      file:
        $ref: '#/definitions/example.ExaFileUploadAndDownload'
    type: object
  response.FilePathResponse:
    properties:
      filePath:
        type: string
    type: object
  response.FileResponse:
    properties:
      file:
        $ref: '#/definitions/example.ExaFile'
    type: object
  response.LoginResponse:
    properties:
      expiresAt:
        type: integer
      token:
        type: string
      user:
        $ref: '#/definitions/system.SysUser'
    type: object
  response.PageResult:
    properties:
      list: {}
      page:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  response.PolicyPathResponse:
    properties:
      paths:
        items:
          $ref: '#/definitions/request.CasbinInfo'
        type: array
    type: object
  response.Response:
    properties:
      code:
        type: integer
      data: {}
      msg:
        type: string
    type: object
  response.SysAPIListResponse:
    properties:
      apis:
        items:
          $ref: '#/definitions/system.SysApi'
        type: array
    type: object
  response.SysAPIResponse:
    properties:
      api:
        $ref: '#/definitions/system.SysApi'
    type: object
  response.SysAuthorityBtnRes:
    properties:
      selected:
        items:
          type: integer
        type: array
    type: object
  response.SysAuthorityCopyResponse:
    properties:
      authority:
        $ref: '#/definitions/system.SysAuthority'
      oldAuthorityId:
        description: 旧角色ID
        type: integer
    type: object
  response.SysAuthorityResponse:
    properties:
      authority:
        $ref: '#/definitions/system.SysAuthority'
    type: object
  response.SysBaseMenuResponse:
    properties:
      menu:
        $ref: '#/definitions/system.SysBaseMenu'
    type: object
  response.SysBaseMenusResponse:
    properties:
      menus:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
    type: object
  response.SysCaptchaResponse:
    properties:
      captchaId:
        type: string
      captchaLength:
        type: integer
      openCaptcha:
        type: boolean
      picPath:
        type: string
    type: object
  response.SysConfigResponse:
    properties:
      config:
        $ref: '#/definitions/config.Server'
    type: object
  response.SysMenusResponse:
    properties:
      menus:
        items:
          $ref: '#/definitions/system.SysMenu'
        type: array
    type: object
  response.SysUserResponse:
    properties:
      user:
        $ref: '#/definitions/system.SysUser'
    type: object
  system.Condition:
    properties:
      ID:
        description: 主键ID
        type: integer
      column:
        type: string
      createdAt:
        description: 创建时间
        type: string
      from:
        type: string
      operator:
        type: string
      templateID:
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.JoinTemplate:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      joins:
        type: string
      "on":
        type: string
      table:
        type: string
      templateID:
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.Meta:
    properties:
      activeName:
        type: string
      closeTab:
        description: 自动关闭tab
        type: boolean
      defaultMenu:
        description: 是否是基础路由（开发中）
        type: boolean
      icon:
        description: 菜单图标
        type: string
      keepAlive:
        description: 是否缓存
        type: boolean
      title:
        description: 菜单名
        type: string
    type: object
  system.SysApi:
    properties:
      ID:
        description: 主键ID
        type: integer
      apiGroup:
        description: api组
        type: string
      createdAt:
        description: 创建时间
        type: string
      description:
        description: api中文描述
        type: string
      method:
        description: 方法:创建POST(默认)|查看GET|更新PUT|删除DELETE
        type: string
      path:
        description: api路径
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysAuthority:
    properties:
      authorityId:
        description: 角色ID
        type: integer
      authorityName:
        description: 角色名
        type: string
      children:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      createdAt:
        description: 创建时间
        type: string
      dataAuthorityId:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      defaultRouter:
        description: 默认菜单(默认dashboard)
        type: string
      deletedAt:
        type: string
      menus:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
      parentId:
        description: 父角色ID
        type: integer
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysBaseMenu:
    properties:
      ID:
        description: 主键ID
        type: integer
      authoritys:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      children:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
      component:
        description: 对应前端文件路径
        type: string
      createdAt:
        description: 创建时间
        type: string
      hidden:
        description: 是否在列表隐藏
        type: boolean
      menuBtn:
        items:
          $ref: '#/definitions/system.SysBaseMenuBtn'
        type: array
      meta:
        allOf:
        - $ref: '#/definitions/system.Meta'
        description: 附加属性
      name:
        description: 路由name
        type: string
      parameters:
        items:
          $ref: '#/definitions/system.SysBaseMenuParameter'
        type: array
      parentId:
        description: 父菜单ID
        type: integer
      path:
        description: 路由path
        type: string
      sort:
        description: 排序标记
        type: integer
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysBaseMenuBtn:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      desc:
        type: string
      name:
        type: string
      sysBaseMenuID:
        type: integer
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysBaseMenuParameter:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      key:
        description: 地址栏携带参数的key
        type: string
      sysBaseMenuID:
        type: integer
      type:
        description: 地址栏携带参数为params还是query
        type: string
      updatedAt:
        description: 更新时间
        type: string
      value:
        description: 地址栏携带参数的值
        type: string
    type: object
  system.SysDictionary:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      desc:
        description: 描述
        type: string
      name:
        description: 字典名（中）
        type: string
      status:
        description: 状态
        type: boolean
      sysDictionaryDetails:
        items:
          $ref: '#/definitions/system.SysDictionaryDetail'
        type: array
      type:
        description: 字典名（英）
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysDictionaryDetail:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      extend:
        description: 扩展值
        type: string
      label:
        description: 展示值
        type: string
      sort:
        description: 排序标记
        type: integer
      status:
        description: 启用状态
        type: boolean
      sysDictionaryID:
        description: 关联标记
        type: integer
      updatedAt:
        description: 更新时间
        type: string
      value:
        description: 字典值
        type: string
    type: object
  system.SysExportTemplate:
    properties:
      ID:
        description: 主键ID
        type: integer
      conditions:
        items:
          $ref: '#/definitions/system.Condition'
        type: array
      createdAt:
        description: 创建时间
        type: string
      dbName:
        description: 数据库名称
        type: string
      joinTemplate:
        items:
          $ref: '#/definitions/system.JoinTemplate'
        type: array
      limit:
        type: integer
      name:
        description: 模板名称
        type: string
      order:
        type: string
      tableName:
        description: 表名称
        type: string
      templateID:
        description: 模板标识
        type: string
      templateInfo:
        description: 模板信息
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysMenu:
    properties:
      ID:
        description: 主键ID
        type: integer
      authoritys:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      btns:
        additionalProperties:
          type: integer
        type: object
      children:
        items:
          $ref: '#/definitions/system.SysMenu'
        type: array
      component:
        description: 对应前端文件路径
        type: string
      createdAt:
        description: 创建时间
        type: string
      hidden:
        description: 是否在列表隐藏
        type: boolean
      menuBtn:
        items:
          $ref: '#/definitions/system.SysBaseMenuBtn'
        type: array
      menuId:
        type: integer
      meta:
        allOf:
        - $ref: '#/definitions/system.Meta'
        description: 附加属性
      name:
        description: 路由name
        type: string
      parameters:
        items:
          $ref: '#/definitions/system.SysBaseMenuParameter'
        type: array
      parentId:
        description: 父菜单ID
        type: integer
      path:
        description: 路由path
        type: string
      sort:
        description: 排序标记
        type: integer
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysOperationRecord:
    properties:
      ID:
        description: 主键ID
        type: integer
      agent:
        description: 代理
        type: string
      body:
        description: 请求Body
        type: string
      createdAt:
        description: 创建时间
        type: string
      error_message:
        description: 错误信息
        type: string
      ip:
        description: 请求ip
        type: string
      latency:
        description: 延迟
        type: string
      method:
        description: 请求方法
        type: string
      path:
        description: 请求路径
        type: string
      resp:
        description: 响应Body
        type: string
      status:
        description: 请求状态
        type: integer
      updatedAt:
        description: 更新时间
        type: string
      user:
        $ref: '#/definitions/system.SysUser'
      user_id:
        description: 用户id
        type: integer
    type: object
  system.SysParams:
    properties:
      ID:
        description: 主键ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      desc:
        description: 参数说明
        type: string
      key:
        description: 参数键
        type: string
      name:
        description: 参数名称
        type: string
      updatedAt:
        description: 更新时间
        type: string
      value:
        description: 参数值
        type: string
    required:
    - key
    - name
    - value
    type: object
  system.SysUser:
    properties:
      ID:
        description: 主键ID
        type: integer
      authorities:
        description: 多用户角色
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      authority:
        allOf:
        - $ref: '#/definitions/system.SysAuthority'
        description: 用户角色
      authorityId:
        description: 用户角色ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      email:
        description: 用户邮箱
        type: string
      enable:
        description: 用户是否被冻结 1正常 2冻结
        type: integer
      headerImg:
        description: 用户头像
        type: string
      nickName:
        description: 用户昵称
        type: string
      originSetting:
        allOf:
        - $ref: '#/definitions/common.JSONMap'
        description: 配置
      phone:
        description: 用户手机号
        type: string
      updatedAt:
        description: 更新时间
        type: string
      userName:
        description: 用户登录名
        type: string
      uuid:
        description: 用户UUID
        type: string
    type: object
  system.System:
    properties:
      config:
        $ref: '#/definitions/config.Server'
    type: object
info:
  contact: {}
  description: 使用gin+vue进行极速开发的全栈开发基础平台
  title: Gin-Vue-Admin Swagger API接口文档
  version: v2.7.9-beta
paths:
  /api/createApi:
    post:
      consumes:
      - application/json
      parameters:
      - description: api路径, api中文描述, api组, 方法
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysApi'
      produces:
      - application/json
      responses:
        "200":
          description: 创建基础api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建基础api
      tags:
      - SysApi
  /api/deleteApi:
    post:
      consumes:
      - application/json
      parameters:
      - description: ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysApi'
      produces:
      - application/json
      responses:
        "200":
          description: 删除api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除api
      tags:
      - SysApi
  /api/deleteApisByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 删除选中Api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除选中Api
      tags:
      - SysApi
  /api/enterSyncApi:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 确认同步API
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 确认同步API
      tags:
      - SysApi
  /api/freshCasbin:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 刷新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 刷新casbin缓存
      tags:
      - SysApi
  /api/getAllApis:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取所有的Api 不分页,返回包括api列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAPIListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取所有的Api 不分页
      tags:
      - SysApi
  /api/getApiById:
    post:
      consumes:
      - application/json
      parameters:
      - description: 根据id获取api
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 根据id获取api,返回包括api详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAPIResponse'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据id获取api
      tags:
      - SysApi
  /api/getApiGroups:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取API分组
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取API分组
      tags:
      - SysApi
  /api/getApiList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页获取API列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SearchApiParams'
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取API列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取API列表
      tags:
      - SysApi
  /api/ignoreApi:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 同步API
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 忽略API
      tags:
      - IgnoreApi
  /api/syncApi:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 同步API
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 同步API
      tags:
      - SysApi
  /api/updateApi:
    post:
      consumes:
      - application/json
      parameters:
      - description: api路径, api中文描述, api组, 方法
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysApi'
      produces:
      - application/json
      responses:
        "200":
          description: 修改基础api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 修改基础api
      tags:
      - SysApi
  /attachmentCategory/addCategory:
    post:
      consumes:
      - application/json
      parameters:
      - description: 媒体库分类数据
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/example.ExaAttachmentCategory'
      produces:
      - application/json
      responses: {}
      security:
      - AttachmentCategory: []
      summary: 添加媒体库分类
      tags:
      - AddCategory
  /attachmentCategory/deleteCategory:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分类id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除分类
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - AttachmentCategory: []
      summary: 删除分类
      tags:
      - DeleteCategory
  /attachmentCategory/getCategoryList:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: 媒体库分类列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/example.ExaAttachmentCategory'
                msg:
                  type: string
              type: object
      security:
      - AttachmentCategory: []
      summary: 媒体库分类列表
      tags:
      - GetCategoryList
  /authority/copyAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 旧角色id, 新权限id, 新权限名, 新父角色id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/response.SysAuthorityCopyResponse'
      produces:
      - application/json
      responses:
        "200":
          description: 拷贝角色,返回包括系统角色详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 拷贝角色
      tags:
      - Authority
  /authority/createAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限名, 父角色id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 创建角色,返回包括系统角色详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建角色
      tags:
      - Authority
  /authority/deleteAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 删除角色
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 删除角色
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除角色
      tags:
      - Authority
  /authority/getAuthorityList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 页码, 每页大小
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.PageInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取角色列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取角色列表
      tags:
      - Authority
  /authority/setDataAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 设置角色资源权限
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 设置角色资源权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置角色资源权限
      tags:
      - Authority
  /authority/updateAuthority:
    put:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限名, 父角色id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 更新角色信息,返回包括系统角色详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新角色信息
      tags:
      - Authority
  /authorityBtn/canRemoveAuthorityBtn:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置权限按钮
      tags:
      - AuthorityBtn
  /authorityBtn/getAuthorityBtn:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id, 角色id, 选中的按钮id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SysAuthorityBtnReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回列表成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityBtnRes'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取权限按钮
      tags:
      - AuthorityBtn
  /authorityBtn/setAuthorityBtn:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id, 角色id, 选中的按钮id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SysAuthorityBtnReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回列表成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置权限按钮
      tags:
      - AuthorityBtn
  /autoCode/addFunc:
    post:
      consumes:
      - application/json
      parameters:
      - description: 增加方法
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.AutoCode'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"创建成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 增加方法
      tags:
      - AddFunc
  /autoCode/createPackage:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建package
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SysAutoCodePackageCreate'
      produces:
      - application/json
      responses:
        "200":
          description: 创建package成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建package
      tags:
      - AutoCodePackage
  /autoCode/createTemp:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建自动代码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.AutoCode'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"创建成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 自动代码模板
      tags:
      - AutoCodeTemplate
  /autoCode/delPackage:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建package
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除package成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除package
      tags:
      - AutoCode
  /autoCode/delSysHistory:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除回滚记录
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除回滚记录
      tags:
      - AutoCode
  /autoCode/getColumn:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取当前表所有字段
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取当前表所有字段
      tags:
      - AutoCode
  /autoCode/getDB:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取当前所有数据库
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取当前所有数据库
      tags:
      - AutoCode
  /autoCode/getMeta:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 获取meta信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取meta信息
      tags:
      - AutoCode
  /autoCode/getPackage:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 创建package成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取package
      tags:
      - AutoCodePackage
  /autoCode/getSysHistory:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.PageInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 查询回滚记录,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 查询回滚记录
      tags:
      - AutoCode
  /autoCode/getTables:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取当前数据库所有表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取当前数据库所有表
      tags:
      - AutoCode
  /autoCode/getTemplates:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 创建package成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取package
      tags:
      - AutoCodePackage
  /autoCode/initAPI:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 打包插件成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 打包插件
      tags:
      - AutoCodePlugin
  /autoCode/initMenu:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 打包插件成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 打包插件
      tags:
      - AutoCodePlugin
  /autoCode/installPlugin:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: this is a test file
        in: formData
        name: plug
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 安装插件成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    type: object
                  type: array
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 安装插件
      tags:
      - AutoCodePlugin
  /autoCode/preview:
    post:
      consumes:
      - application/json
      parameters:
      - description: 预览创建代码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.AutoCode'
      produces:
      - application/json
      responses:
        "200":
          description: 预览创建后的代码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 预览创建后的代码
      tags:
      - AutoCodeTemplate
  /autoCode/pubPlug:
    post:
      consumes:
      - application/json
      parameters:
      - description: 插件名称
        in: query
        name: plugName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 打包插件成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 打包插件
      tags:
      - AutoCodePlugin
  /autoCode/rollback:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SysAutoHistoryRollBack'
      produces:
      - application/json
      responses:
        "200":
          description: 回滚自动生成代码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 回滚自动生成代码
      tags:
      - AutoCode
  /base/captcha:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 生成验证码,返回包括随机数id,base64,验证码长度,是否开启验证码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysCaptchaResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 生成验证码
      tags:
      - Base
  /base/login:
    post:
      parameters:
      - description: 用户名, 密码, 验证码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Login'
      produces:
      - application/json
      responses:
        "200":
          description: 返回包括用户信息,token,过期时间
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.LoginResponse'
                msg:
                  type: string
              type: object
      summary: 用户登录
      tags:
      - Base
  /casbin/UpdateCasbin:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限模型列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CasbinInReceive'
      produces:
      - application/json
      responses:
        "200":
          description: 更新角色api权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新角色api权限
      tags:
      - Casbin
  /casbin/getPolicyPathByAuthorityId:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限模型列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CasbinInReceive'
      produces:
      - application/json
      responses:
        "200":
          description: 获取权限列表,返回包括casbin详情列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PolicyPathResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取权限列表
      tags:
      - Casbin
  /customer/customer:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 客户ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/example.ExaCustomer'
      produces:
      - application/json
      responses:
        "200":
          description: 删除客户
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除客户
      tags:
      - ExaCustomer
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 客户名
        in: query
        name: customerName
        type: string
      - description: 客户手机号
        in: query
        name: customerPhoneData
        type: string
      - description: 管理角色ID
        in: query
        name: sysUserAuthorityID
        type: integer
      - description: 管理ID
        in: query
        name: sysUserId
        type: integer
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取单一客户信息,返回包括客户详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ExaCustomerResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取单一客户信息
      tags:
      - ExaCustomer
    post:
      consumes:
      - application/json
      parameters:
      - description: 客户用户名, 客户手机号码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/example.ExaCustomer'
      produces:
      - application/json
      responses:
        "200":
          description: 创建客户
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建客户
      tags:
      - ExaCustomer
    put:
      consumes:
      - application/json
      parameters:
      - description: 客户ID, 客户信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/example.ExaCustomer'
      produces:
      - application/json
      responses:
        "200":
          description: 更新客户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新客户信息
      tags:
      - ExaCustomer
  /customer/customerList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取权限客户列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取权限客户列表
      tags:
      - ExaCustomer
  /email/emailTest:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"发送成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 发送测试邮件
      tags:
      - System
  /email/sendEmail:
    post:
      parameters:
      - description: 发送邮件必须的参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/response.Email'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"发送成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 发送邮件
      tags:
      - System
  /fileUploadAndDownload/breakpointContinue:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: an example for breakpoint resume, 断点续传示例
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 断点续传到服务器
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 断点续传到服务器
      tags:
      - ExaFileUploadAndDownload
  /fileUploadAndDownload/deleteFile:
    post:
      parameters:
      - description: 传入文件里面id即可
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/example.ExaFileUploadAndDownload'
      produces:
      - application/json
      responses:
        "200":
          description: 删除文件
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除文件
      tags:
      - ExaFileUploadAndDownload
  /fileUploadAndDownload/findFile:
    get:
      consumes:
      - multipart/form-data
      parameters:
      - description: Find the file, 查找文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 查找文件,返回包括文件详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.FileResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 查找文件
      tags:
      - ExaFileUploadAndDownload
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 上传文件完成
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 创建文件,返回包括文件路径
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.FilePathResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建文件
      tags:
      - ExaFileUploadAndDownload
  /fileUploadAndDownload/getFileList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 页码, 每页大小, 分类id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ExaAttachmentCategorySearch'
      produces:
      - application/json
      responses:
        "200":
          description: 分页文件列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页文件列表
      tags:
      - ExaFileUploadAndDownload
  /fileUploadAndDownload/importURL:
    post:
      parameters:
      - description: 对象
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/example.ExaFileUploadAndDownload'
      produces:
      - application/json
      responses:
        "200":
          description: 导入URL
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 导入URL
      tags:
      - ExaFileUploadAndDownload
  /fileUploadAndDownload/removeChunk:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 删除缓存切片
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 删除切片
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除切片
      tags:
      - ExaFileUploadAndDownload
  /fileUploadAndDownload/upload:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 上传文件示例
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传文件示例,返回包括文件详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ExaFileResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 上传文件示例
      tags:
      - ExaFileUploadAndDownload
  /info/createInfo:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建公告
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.Info'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建公告
      tags:
      - Info
  /info/deleteInfo:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除公告
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.Info'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除公告
      tags:
      - Info
  /info/deleteInfoByIds:
    delete:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除公告
      tags:
      - Info
  /info/findInfo:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 内容
        in: query
        name: content
        type: string
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 标题
        in: query
        name: title
        type: string
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      - description: 作者
        in: query
        name: userID
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/model.Info'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询公告
      tags:
      - Info
  /info/getInfoDataSource:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: object
                msg:
                  type: string
              type: object
      summary: 获取Info的数据源
      tags:
      - Info
  /info/getInfoList:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: endCreatedAt
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: startCreatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取公告列表
      tags:
      - Info
  /info/getInfoPublic:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: endCreatedAt
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: startCreatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: object
                msg:
                  type: string
              type: object
      summary: 不需要鉴权的公告接口
      tags:
      - Info
  /info/updateInfo:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新公告
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.Info'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新公告
      tags:
      - Info
  /init/checkdb:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 初始化用户数据库
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: 初始化用户数据库
      tags:
      - CheckDB
  /init/initdb:
    post:
      parameters:
      - description: 初始化数据库参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.InitDB'
      produces:
      - application/json
      responses:
        "200":
          description: 初始化用户数据库
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
              type: object
      summary: 初始化用户数据库
      tags:
      - InitDB
  /jwt/jsonInBlacklist:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: jwt加入黑名单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: jwt加入黑名单
      tags:
      - Jwt
  /menu/addBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysBaseMenu'
      produces:
      - application/json
      responses:
        "200":
          description: 新增菜单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 新增菜单
      tags:
      - Menu
  /menu/addMenuAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.AddMenuAuthorityInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 增加menu和角色关联关系
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 增加menu和角色关联关系
      tags:
      - AuthorityMenu
  /menu/deleteBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除菜单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除菜单
      tags:
      - Menu
  /menu/getBaseMenuById:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 根据id获取菜单,返回包括系统菜单列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysBaseMenuResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据id获取菜单
      tags:
      - Menu
  /menu/getBaseMenuTree:
    post:
      parameters:
      - description: 空
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Empty'
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户动态路由,返回包括系统菜单列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysBaseMenusResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户动态路由
      tags:
      - AuthorityMenu
  /menu/getMenu:
    post:
      parameters:
      - description: 空
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Empty'
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户动态路由,返回包括系统菜单详情列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysMenusResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户动态路由
      tags:
      - AuthorityMenu
  /menu/getMenuAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetAuthorityId'
      produces:
      - application/json
      responses:
        "200":
          description: 获取指定角色menu
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取指定角色menu
      tags:
      - AuthorityMenu
  /menu/getMenuList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 页码, 每页大小
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.PageInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取基础menu列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取基础menu列表
      tags:
      - Menu
  /menu/updateBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysBaseMenu'
      produces:
      - application/json
      responses:
        "200":
          description: 更新菜单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新菜单
      tags:
      - Menu
  /sysDictionary/createSysDictionary:
    post:
      consumes:
      - application/json
      parameters:
      - description: SysDictionary模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionary'
      produces:
      - application/json
      responses:
        "200":
          description: 创建SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建SysDictionary
      tags:
      - SysDictionary
  /sysDictionary/deleteSysDictionary:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SysDictionary模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionary'
      produces:
      - application/json
      responses:
        "200":
          description: 删除SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除SysDictionary
      tags:
      - SysDictionary
  /sysDictionary/findSysDictionary:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 描述
        in: query
        name: desc
        type: string
      - description: 字典名（中）
        in: query
        name: name
        type: string
      - description: 状态
        in: query
        name: status
        type: boolean
      - description: 字典名（英）
        in: query
        name: type
        type: string
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用id查询SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询SysDictionary
      tags:
      - SysDictionary
  /sysDictionary/getSysDictionaryList:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取SysDictionary列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取SysDictionary列表
      tags:
      - SysDictionary
  /sysDictionary/updateSysDictionary:
    put:
      consumes:
      - application/json
      parameters:
      - description: SysDictionary模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionary'
      produces:
      - application/json
      responses:
        "200":
          description: 更新SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新SysDictionary
      tags:
      - SysDictionary
  /sysDictionaryDetail/createSysDictionaryDetail:
    post:
      consumes:
      - application/json
      parameters:
      - description: SysDictionaryDetail模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionaryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: 创建SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建SysDictionaryDetail
      tags:
      - SysDictionaryDetail
  /sysDictionaryDetail/deleteSysDictionaryDetail:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SysDictionaryDetail模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionaryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: 删除SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除SysDictionaryDetail
      tags:
      - SysDictionaryDetail
  /sysDictionaryDetail/findSysDictionaryDetail:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 扩展值
        in: query
        name: extend
        type: string
      - description: 展示值
        in: query
        name: label
        type: string
      - description: 排序标记
        in: query
        name: sort
        type: integer
      - description: 启用状态
        in: query
        name: status
        type: boolean
      - description: 关联标记
        in: query
        name: sysDictionaryID
        type: integer
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      - description: 字典值
        in: query
        name: value
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用id查询SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询SysDictionaryDetail
      tags:
      - SysDictionaryDetail
  /sysDictionaryDetail/getSysDictionaryDetailList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 扩展值
        in: query
        name: extend
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 展示值
        in: query
        name: label
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序标记
        in: query
        name: sort
        type: integer
      - description: 启用状态
        in: query
        name: status
        type: boolean
      - description: 关联标记
        in: query
        name: sysDictionaryID
        type: integer
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      - description: 字典值
        in: query
        name: value
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取SysDictionaryDetail列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取SysDictionaryDetail列表
      tags:
      - SysDictionaryDetail
  /sysDictionaryDetail/updateSysDictionaryDetail:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新SysDictionaryDetail
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionaryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: 更新SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新SysDictionaryDetail
      tags:
      - SysDictionaryDetail
  /sysExportTemplate/ExportTemplate:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 导出表格模板
      tags:
      - SysExportTemplate
  /sysExportTemplate/createSysExportTemplate:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysExportTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"创建成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 创建导出模板
      tags:
      - SysExportTemplate
  /sysExportTemplate/deleteSysExportTemplate:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysExportTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"删除成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 删除导出模板
      tags:
      - SysExportTemplate
  /sysExportTemplate/deleteSysExportTemplateByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"批量删除成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 批量删除导出模板
      tags:
      - SysExportTemplate
  /sysExportTemplate/exportExcel:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 导出表格
      tags:
      - SysExportTemplate
  /sysExportTemplate/findSysExportTemplate:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 数据库名称
        in: query
        name: dbName
        type: string
      - in: query
        name: limit
        type: integer
      - description: 模板名称
        in: query
        name: name
        type: string
      - in: query
        name: order
        type: string
      - description: 表名称
        in: query
        name: tableName
        type: string
      - description: 模板标识
        in: query
        name: templateID
        type: string
      - description: 模板信息
        in: query
        name: templateInfo
        type: string
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"查询成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 用id查询导出模板
      tags:
      - SysExportTemplate
  /sysExportTemplate/getSysExportTemplateList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 数据库名称
        in: query
        name: dbName
        type: string
      - in: query
        name: endCreatedAt
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - in: query
        name: limit
        type: integer
      - description: 模板名称
        in: query
        name: name
        type: string
      - in: query
        name: order
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: startCreatedAt
        type: string
      - description: 表名称
        in: query
        name: tableName
        type: string
      - description: 模板标识
        in: query
        name: templateID
        type: string
      - description: 模板信息
        in: query
        name: templateInfo
        type: string
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"获取成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 分页获取导出模板列表
      tags:
      - SysExportTemplate
  /sysExportTemplate/importExcel:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 导入表格
      tags:
      - SysImportTemplate
  /sysExportTemplate/updateSysExportTemplate:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysExportTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"更新成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更新导出模板
      tags:
      - SysExportTemplate
  /sysOperationRecord/createSysOperationRecord:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建SysOperationRecord
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysOperationRecord'
      produces:
      - application/json
      responses:
        "200":
          description: 创建SysOperationRecord
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建SysOperationRecord
      tags:
      - SysOperationRecord
  /sysOperationRecord/deleteSysOperationRecord:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SysOperationRecord模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysOperationRecord'
      produces:
      - application/json
      responses:
        "200":
          description: 删除SysOperationRecord
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除SysOperationRecord
      tags:
      - SysOperationRecord
  /sysOperationRecord/deleteSysOperationRecordByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除SysOperationRecord
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除SysOperationRecord
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除SysOperationRecord
      tags:
      - SysOperationRecord
  /sysOperationRecord/findSysOperationRecord:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 代理
        in: query
        name: agent
        type: string
      - description: 请求Body
        in: query
        name: body
        type: string
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 错误信息
        in: query
        name: error_message
        type: string
      - description: 请求ip
        in: query
        name: ip
        type: string
      - description: 延迟
        in: query
        name: latency
        type: string
      - description: 请求方法
        in: query
        name: method
        type: string
      - description: 请求路径
        in: query
        name: path
        type: string
      - description: 响应Body
        in: query
        name: resp
        type: string
      - description: 请求状态
        in: query
        name: status
        type: integer
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      - description: 用户id
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 用id查询SysOperationRecord
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询SysOperationRecord
      tags:
      - SysOperationRecord
  /sysOperationRecord/getSysOperationRecordList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 代理
        in: query
        name: agent
        type: string
      - description: 请求Body
        in: query
        name: body
        type: string
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 错误信息
        in: query
        name: error_message
        type: string
      - description: 请求ip
        in: query
        name: ip
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 延迟
        in: query
        name: latency
        type: string
      - description: 请求方法
        in: query
        name: method
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - description: 请求路径
        in: query
        name: path
        type: string
      - description: 响应Body
        in: query
        name: resp
        type: string
      - description: 请求状态
        in: query
        name: status
        type: integer
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      - description: 用户id
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取SysOperationRecord列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取SysOperationRecord列表
      tags:
      - SysOperationRecord
  /sysParams/createSysParams:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysParams'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建参数
      tags:
      - SysParams
  /sysParams/deleteSysParams:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysParams'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除参数
      tags:
      - SysParams
  /sysParams/deleteSysParamsByIds:
    delete:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除参数
      tags:
      - SysParams
  /sysParams/findSysParams:
    get:
      consumes:
      - application/json
      parameters:
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 创建时间
        in: query
        name: createdAt
        type: string
      - description: 参数说明
        in: query
        name: desc
        type: string
      - description: 参数键
        in: query
        name: key
        required: true
        type: string
      - description: 参数名称
        in: query
        name: name
        required: true
        type: string
      - description: 更新时间
        in: query
        name: updatedAt
        type: string
      - description: 参数值
        in: query
        name: value
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/system.SysParams'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询参数
      tags:
      - SysParams
  /sysParams/getSysParam:
    get:
      consumes:
      - application/json
      parameters:
      - description: key
        in: query
        name: key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/system.SysParams'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据key获取参数value
      tags:
      - SysParams
  /sysParams/getSysParamsList:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: endCreatedAt
        type: string
      - in: query
        name: key
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - in: query
        name: name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: startCreatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取参数列表
      tags:
      - SysParams
  /sysParams/updateSysParams:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysParams'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新参数
      tags:
      - SysParams
  /system/getServerInfo:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 获取服务器信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取服务器信息
      tags:
      - System
  /system/getSystemConfig:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 获取配置文件内容,返回包括系统配置
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysConfigResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取配置文件内容
      tags:
      - System
  /system/reloadSystem:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 重启系统
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 重启系统
      tags:
      - System
  /system/setSystemConfig:
    post:
      parameters:
      - description: 设置配置文件内容
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.System'
      produces:
      - application/json
      responses:
        "200":
          description: 设置配置文件内容
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置配置文件内容
      tags:
      - System
  /user/SetSelfInfo:
    put:
      consumes:
      - application/json
      parameters:
      - description: ID, 用户名, 昵称, 头像链接
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysUser'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户信息
      tags:
      - SysUser
  /user/SetSelfSetting:
    put:
      consumes:
      - application/json
      parameters:
      - description: 用户配置数据
        in: body
        name: data
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户配置
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户配置
      tags:
      - SysUser
  /user/admin_register:
    post:
      parameters:
      - description: 用户名, 昵称, 密码, 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Register'
      produces:
      - application/json
      responses:
        "200":
          description: 用户注册账号,返回包括用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysUserResponse'
                msg:
                  type: string
              type: object
      summary: 用户注册账号
      tags:
      - SysUser
  /user/changePassword:
    post:
      parameters:
      - description: 用户名, 原密码, 新密码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ChangePasswordReq'
      produces:
      - application/json
      responses:
        "200":
          description: 用户修改密码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用户修改密码
      tags:
      - SysUser
  /user/deleteUser:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 用户ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除用户
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除用户
      tags:
      - SysUser
  /user/getUserInfo:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户信息
      tags:
      - SysUser
  /user/getUserList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 页码, 每页大小
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetUserList'
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取用户列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取用户列表
      tags:
      - SysUser
  /user/resetPassword:
    post:
      parameters:
      - description: ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysUser'
      produces:
      - application/json
      responses:
        "200":
          description: 重置用户密码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 重置用户密码
      tags:
      - SysUser
  /user/setUserAuthorities:
    post:
      consumes:
      - application/json
      parameters:
      - description: 用户UUID, 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SetUserAuthorities'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户权限
      tags:
      - SysUser
  /user/setUserAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 用户UUID, 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SetUserAuth'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更改用户权限
      tags:
      - SysUser
  /user/setUserInfo:
    put:
      consumes:
      - application/json
      parameters:
      - description: ID, 用户名, 昵称, 头像链接
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysUser'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户信息
      tags:
      - SysUser
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: x-token
    type: apiKey
swagger: "2.0"
tags:
- name: Base
- description: 用户
  name: SysUser

# WebSocket功能使用指南

## 概述

项目已集成WebSocket功能，支持实时消息推送。当用户连接WebSocket后，企业微信的消息处理会通过WebSocket实时推送给前端。

## 功能特性

- **实时连接管理**: 支持多用户同时连接，按用户ID分组管理
- **自动重连**: 支持心跳检测和自动重连机制
- **消息推送**: 企业微信剪贴板消息实时推送到前端
- **连接状态查询**: 提供API查询用户连接状态

## API接口

### 1. 建立WebSocket连接

**端点**: `GET /api/websocket`

**认证**: 需要JWT token

**前置条件**: 用户必须已通过企业微信进入会话（`connected_kf=true`）

**描述**: 建立WebSocket连接，需要在请求头中携带有效的JWT token，且用户必须已连接企业微信客服

**错误响应**:
- `401`: 用户未登录
- `400`: 用户不存在
- `403`: 用户未连接企业微信客服，请先通过企业微信进入会话

### 2. 获取连接用户列表

**端点**: `GET /api/websocket/users`

**认证**: 需要JWT token

**响应**:
```json
{
  "code": 0,
  "data": {
    "users": [1, 2, 3],
    "count": 3
  },
  "msg": "获取成功"
}
```

### 3. 检查用户连接状态

**端点**: `GET /api/websocket/check`

**认证**: 需要JWT token

**响应**:
```json
{
  "code": 0,
  "data": {
    "connected": true
  },
  "msg": "检查成功"
}
```

### 4. 检查企业微信客服连接状态

**端点**: `GET /api/websocket/kf-status`

**认证**: 需要JWT token

**描述**: 检查用户的企业微信客服连接状态，用于判断是否可以建立WebSocket连接

**响应**:
```json
{
  "code": 0,
  "data": {
    "connected_kf": true,     // 是否已连接企业微信客服
    "ws_connected": false,    // 是否已建立WebSocket连接
    "can_connect_ws": true,   // 是否可以建立WebSocket连接
    "unionid": "xxx"         // 微信unionid
  },
  "msg": "检查成功"
}
```

## WebSocket消息格式

### 消息结构

```json
{
  "type": "string",      // 消息类型
  "content": "string",   // 消息内容
  "title": "string",     // 消息标题
  "userId": 123,         // 用户ID
  "timestamp": 1642345678, // 时间戳
  "extra": {}           // 额外数据
}
```

### 消息类型

1. **系统消息** (`type: "system"`)
   - 连接成功提示
   - 系统通知

2. **新剪贴板内容** (`type: "clipboard_new"`)
   - 企业微信新剪贴内容通知
   - 包含剪贴板ID、内容类型等额外信息

示例：
```json
{
  "type": "clipboard_new",
  "content": "这是剪贴的文本内容",
  "title": "这是剪贴的文本内容",
  "userId": 123,
  "timestamp": 1642345678,
  "extra": {
    "clipboardId": 456,
    "contentType": "text",
    "fileURL": "",
    "source": "wechat"
  }
}
```

## 前端集成示例

### JavaScript WebSocket客户端

```javascript
// WebSocket连接管理器
class WebSocketManager {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000; // 5秒
        this.statusCheckInterval = 3000; // 3秒检查一次状态
        this.statusTimer = null;
    }

    // 检查企业微信客服连接状态
    async checkKfStatus() {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/websocket/kf-status', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            const result = await response.json();
            
            if (result.code === 0) {
                return result.data;
            }
            return null;
        } catch (error) {
            console.error('检查客服状态失败:', error);
            return null;
        }
    }

    // 开始状态检查和连接尝试
    async start() {
        this.statusTimer = setInterval(async () => {
            const status = await this.checkKfStatus();
            
            if (status && status.can_connect_ws) {
                // 可以建立WebSocket连接
                if (!this.isConnected) {
                    this.connectWebSocket();
                }
                // 清除状态检查定时器，因为已经可以连接了
                clearInterval(this.statusTimer);
            } else {
                console.log('等待企业微信客服连接...');
            }
        }, this.statusCheckInterval);
    }

    // 建立WebSocket连接
    connectWebSocket() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            return; // 已经连接
        }

        const token = localStorage.getItem('token');
        const wsUrl = `ws://localhost:8888/api/websocket`;

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = (event) => {
            console.log('WebSocket连接成功');
            this.isConnected = true;
            this.reconnectAttempts = 0; // 重置重连次数
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket连接关闭');
            this.isConnected = false;
            this.handleReconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.isConnected = false;
        };
    }

    // 处理接收到的消息
    handleMessage(message) {
        console.log('收到WebSocket消息:', message);
        
        switch(message.type) {
            case 'system':
                console.log('系统消息:', message.content);
                break;
            case 'clipboard_new':
                console.log('新剪贴板内容:', message);
                // 处理新的剪贴板内容
                this.handleNewClipboard(message);
                break;
            default:
                console.log('未知消息类型:', message);
        }
    }

    // 处理新剪贴板内容
    handleNewClipboard(message) {
        // 更新剪贴板列表
        updateClipboardList(message.extra.clipboardId);
        
        // 显示通知
        showNotification('收到新的剪贴内容: ' + message.title);
        
        // 可以触发自定义事件
        const event = new CustomEvent('newClipboard', { detail: message });
        document.dispatchEvent(event);
    }

    // 处理重连
    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连');
            return;
        }

        this.reconnectAttempts++;
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.connectWebSocket();
        }, this.reconnectInterval);
    }

    // 停止连接
    stop() {
        if (this.statusTimer) {
            clearInterval(this.statusTimer);
            this.statusTimer = null;
        }
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.isConnected = false;
    }
}

// 使用示例
const wsManager = new WebSocketManager();

// 页面加载时开始检查状态和连接
window.addEventListener('load', () => {
    wsManager.start();
});

// 页面卸载时清理连接
window.addEventListener('beforeunload', () => {
    wsManager.stop();
});

// 示例：更新剪贴板列表
function updateClipboardList(clipboardId) {
    // 这里可以调用API获取最新的剪贴板列表
    // 或者直接将新消息添加到现有列表中
    console.log('更新剪贴板列表，新ID:', clipboardId);
}

// 示例：显示通知
function showNotification(message) {
    // 这里可以使用浏览器通知API或自定义通知组件
    if (Notification.permission === 'granted') {
        new Notification('剪贴板通知', { body: message });
    } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                new Notification('剪贴板通知', { body: message });
            }
        });
    }
}
```

### Vue.js 集成示例

```vue
<template>
  <div class="websocket-manager">
    <!-- 连接状态显示 -->
    <div class="connection-status">
      <div v-if="kfStatus.connected_kf && isConnected" class="status-connected">
        ✅ WebSocket已连接，实时推送已启用
      </div>
      <div v-else-if="kfStatus.connected_kf && !isConnected" class="status-connecting">
        🔄 正在连接WebSocket...
      </div>
      <div v-else class="status-waiting">
        ⏳ 等待企业微信客服连接，请先通过企业微信进入会话
      </div>
    </div>

    <!-- 连接信息 -->
    <div class="connection-info" v-if="showDebugInfo">
      <h4>连接状态详情</h4>
      <ul>
        <li>企业微信客服已连接: {{ kfStatus.connected_kf ? '是' : '否' }}</li>
        <li>WebSocket已连接: {{ kfStatus.ws_connected ? '是' : '否' }}</li>
        <li>可以建立WebSocket: {{ kfStatus.can_connect_ws ? '是' : '否' }}</li>
        <li>UnionID: {{ kfStatus.unionid || '未设置' }}</li>
      </ul>
    </div>
    
    <!-- 消息列表 -->
    <div class="messages" v-if="messages.length > 0">
      <h4>实时消息</h4>
      <div v-for="message in messages" :key="message.timestamp" class="message">
        <div class="message-header">
          <span class="message-type" :class="`type-${message.type}`">{{ message.type }}</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>
        <div class="message-content">{{ message.content }}</div>
        <div class="message-title" v-if="message.title">标题: {{ message.title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WebSocketManager',
  data() {
    return {
      ws: null,
      isConnected: false,
      messages: [],
      kfStatus: {
        connected_kf: false,
        ws_connected: false,
        can_connect_ws: false,
        unionid: ''
      },
      statusCheckTimer: null,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      reconnectInterval: 5000,
      statusCheckInterval: 3000,
      showDebugInfo: false // 可以通过props控制是否显示调试信息
    }
  },
  mounted() {
    this.startStatusCheck();
  },
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    // 开始状态检查
    startStatusCheck() {
      this.checkKfStatus(); // 立即检查一次
      
      this.statusCheckTimer = setInterval(() => {
        this.checkKfStatus();
      }, this.statusCheckInterval);
    },

    // 检查企业微信客服连接状态
    async checkKfStatus() {
      try {
        const response = await this.$http.get('/api/websocket/kf-status', {
          headers: {
            'Authorization': `Bearer ${this.$store.getters.token}`
          }
        });
        
        if (response.data.code === 0) {
          this.kfStatus = response.data.data;
          
          // 如果可以连接WebSocket且当前未连接，则尝试连接
          if (this.kfStatus.can_connect_ws && !this.isConnected) {
            this.connectWebSocket();
            // 停止状态检查，因为已经可以连接了
            if (this.statusCheckTimer) {
              clearInterval(this.statusCheckTimer);
              this.statusCheckTimer = null;
            }
          }
        }
      } catch (error) {
        console.error('检查客服状态失败:', error);
      }
    },

    // 建立WebSocket连接
    connectWebSocket() {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        return; // 已经连接
      }

      const token = this.$store.getters.token;
      const wsUrl = `ws://${window.location.host}/api/websocket`;
      
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        console.log('WebSocket连接成功');
        this.$message.success('实时推送已启用');
        this.$emit('websocket-connected');
      };
      
      this.ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      };
      
      this.ws.onclose = () => {
        this.isConnected = false;
        console.log('WebSocket连接关闭');
        this.$emit('websocket-disconnected');
        this.handleReconnect();
      };
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
        this.isConnected = false;
        this.$message.error('WebSocket连接失败');
      };
    },

    // 处理接收到的消息
    handleMessage(message) {
      console.log('收到WebSocket消息:', message);
      
      // 添加到消息列表
      this.messages.unshift(message);
      
      // 限制消息数量，只保留最新的50条
      if (this.messages.length > 50) {
        this.messages = this.messages.slice(0, 50);
      }
      
      switch(message.type) {
        case 'system':
          this.$message.info(message.content);
          break;
        case 'clipboard_new':
          this.handleNewClipboard(message);
          break;
        default:
          console.log('未知消息类型:', message);
      }
    },

    // 处理新剪贴板内容
    handleNewClipboard(message) {
      // 发送事件给父组件
      this.$emit('clipboard-updated', message.extra.clipboardId);
      
      // 显示通知
      this.$notify({
        title: '新剪贴内容',
        message: message.title,
        type: 'success',
        duration: 3000
      });
      
      // 如果支持浏览器通知
      if (Notification.permission === 'granted') {
        new Notification('剪贴板通知', { 
          body: `收到新的剪贴内容: ${message.title}`,
          icon: '/favicon.ico'
        });
      }
    },

    // 处理重连
    handleReconnect() {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.log('达到最大重连次数，重新开始状态检查');
        this.startStatusCheck(); // 重新开始状态检查
        return;
      }

      this.reconnectAttempts++;
      console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        if (this.kfStatus.can_connect_ws) {
          this.connectWebSocket();
        }
      }, this.reconnectInterval);
    },

    // 清理资源
    cleanup() {
      if (this.statusCheckTimer) {
        clearInterval(this.statusCheckTimer);
        this.statusCheckTimer = null;
      }
      
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
      
      this.isConnected = false;
    },

    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp * 1000).toLocaleTimeString();
    },

    // 手动重连
    manualReconnect() {
      this.cleanup();
      this.reconnectAttempts = 0;
      this.startStatusCheck();
    }
  }
}
</script>

<style scoped>
.websocket-manager {
  padding: 16px;
}

.connection-status {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 6px;
  font-weight: 500;
}

.status-connected {
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
  color: #0369a1;
}

.status-connecting {
  background-color: #fefce8;
  border: 1px solid #eab308;
  color: #a16207;
}

.status-waiting {
  background-color: #fef2f2;
  border: 1px solid #ef4444;
  color: #dc2626;
}

.connection-info {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.connection-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #475569;
}

.connection-info ul {
  margin: 0;
  padding-left: 20px;
  font-size: 12px;
  color: #64748b;
}

.messages {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  max-height: 300px;
  overflow-y: auto;
}

.messages h4 {
  margin: 0;
  padding: 12px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  color: #475569;
}

.message {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
}

.message:last-child {
  border-bottom: none;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.message-type {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-system {
  background-color: #dbeafe;
  color: #1e40af;
}

.type-clipboard_new {
  background-color: #dcfce7;
  color: #166534;
}

.message-time {
  font-size: 11px;
  color: #94a3b8;
}

.message-content {
  font-size: 13px;
  color: #334155;
  margin-bottom: 4px;
}

.message-title {
  font-size: 12px;
  color: #64748b;
  font-style: italic;
}
</style>

## 企业微信集成

当企业微信用户发送消息到客服系统时，系统会：

1. 接收并解析微信消息
2. 根据用户unionid查找对应的系统用户
3. 将消息内容保存到剪贴板
4. **如果用户有WebSocket连接，立即推送消息给前端**
5. 发送确认消息给企业微信用户

支持的消息类型：
- **文本消息**: 直接保存文本内容
- **图片消息**: 下载图片并上传到OSS，保存图片URL
- **语音消息**: 下载语音，使用阿里云语音识别转换为文字

## 注意事项

1. **认证**: WebSocket连接需要有效的JWT token
2. **断线重连**: 建议在前端实现断线重连机制
3. **消息处理**: 及时处理接收到的消息，避免消息积压
4. **性能**: 大量用户连接时注意服务器性能监控
5. **安全**: 确保只有授权用户能够建立WebSocket连接

## 配置说明

WebSocket服务在系统启动时自动初始化，无需额外配置。相关设置：

- **心跳间隔**: 54秒
- **读取超时**: 60秒
- **消息缓冲区**: 256个消息
- **跨域支持**: 已启用，允许所有域名连接

## 故障排查

### 常见问题

1. **连接失败**
   - 检查JWT token是否有效
   - 确认WebSocket服务是否正常启动
   - 检查防火墙设置

2. **消息未收到**
   - 确认用户已建立WebSocket连接
   - 检查企业微信配置是否正确
   - 查看服务器日志

3. **频繁断开连接**
   - 检查网络稳定性
   - 确认心跳机制是否正常工作
   - 检查服务器资源使用情况

### 日志查看

WebSocket相关日志会记录在系统日志中，可以通过以下方式查看：

```bash
# 查看WebSocket连接日志
grep "WebSocket" /path/to/logfile

# 查看特定用户的WebSocket活动
grep "userID.*123" /path/to/logfile
``` 
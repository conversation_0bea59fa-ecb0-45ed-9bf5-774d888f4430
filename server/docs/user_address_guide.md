# 用户地址管理功能指南

## 功能概述

用户地址管理功能为实物商品购买提供了完整的收货地址管理解决方案，支持地址的增删改查、默认地址设置等功能。

## 核心特性

### 1. 地址管理
- ✅ 创建收货地址
- ✅ 更新地址信息
- ✅ 删除地址
- ✅ 查询地址列表
- ✅ 获取单个地址详情
- ✅ 设置默认地址
- ✅ 获取默认地址

### 2. 购买流程集成
- ✅ 支持选择已保存的地址购买
- ✅ 支持直接输入新地址购买
- ✅ 自动使用默认地址（如果未指定）
- ✅ 实物商品强制要求地址信息

## API 接口

### 地址管理接口

#### 1. 创建地址
```http
POST /userAddress/create
```

**请求参数：**
```json
{
    "name": "张三",
    "phone": "13800138000",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "address": "科技园南区腾讯大厦",
    "zipcode": "518057",
    "label": "公司",
    "isDefault": true
}
```

#### 2. 更新地址
```http
PUT /userAddress/update
```

**请求参数：**
```json
{
    "id": 1,
    "name": "张三",
    "phone": "13800138000",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "address": "科技园南区腾讯大厦888号",
    "zipcode": "518057",
    "label": "公司",
    "isDefault": true
}
```

#### 3. 删除地址
```http
DELETE /userAddress/deleteById?id=1
```

**查询参数：**
- `id`: 地址ID（必填）

#### 4. 获取地址列表
```http
GET /userAddress/list?page=1&pageSize=10
```

**查询参数：**
- `page`: 页码
- `pageSize`: 每页大小
- `label`: 地址标签（可选）
- `province`: 省份（可选）
- `city`: 城市（可选）
- `isDefault`: 是否默认地址（可选）

#### 5. 获取单个地址
```http
GET /userAddress/{id}
```

#### 6. 设置默认地址
```http
POST /userAddress/setDefault
```

**请求参数：**
```json
{
    "id": 1
}
```

#### 7. 获取默认地址
```http
GET /userAddress/default
```

### 购买商品接口（已优化）

#### 购买商品
```http
POST /product/buy
```

**方式一：使用已保存的地址ID**
```json
{
    "productId": 1,
    "quantity": 1,
    "paymentMethod": "alipay",
    "addressId": 1,
    "expireMinutes": 30
}
```

**方式二：直接传入地址信息**
```json
{
    "productId": 1,
    "quantity": 1,
    "paymentMethod": "alipay",
    "shippingAddress": {
        "name": "张三",
        "phone": "13800138000",
        "province": "广东省",
        "city": "深圳市",
        "district": "南山区",
        "address": "科技园南区腾讯大厦",
        "zipcode": "518057"
    },
    "expireMinutes": 30
}
```

**方式三：使用默认地址（不传任何地址参数）**
```json
{
    "productId": 1,
    "quantity": 1,
    "paymentMethod": "alipay",
    "expireMinutes": 30
}
```

## 地址选择优先级

购买实物商品时，地址选择按以下优先级：

1. **addressId**: 如果传入地址ID，优先使用该地址
2. **shippingAddress**: 如果传入地址信息，使用传入的地址
3. **默认地址**: 如果都没有传入，使用用户的默认地址
4. **错误**: 如果以上都没有，返回错误提示

## 数据库表结构

### sys_user_addresses 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| user_id | bigint | 用户ID |
| name | varchar(50) | 收货人姓名 |
| phone | varchar(20) | 收货人电话 |
| province | varchar(50) | 省份 |
| city | varchar(50) | 城市 |
| district | varchar(50) | 区县 |
| address | varchar(200) | 详细地址 |
| zipcode | varchar(10) | 邮政编码 |
| is_default | tinyint(1) | 是否默认地址 |
| label | varchar(20) | 地址标签 |
| status | int | 状态（1-正常 2-禁用） |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| deleted_at | datetime | 删除时间 |

## 业务规则

### 1. 默认地址规则
- 每个用户只能有一个默认地址
- 设置新的默认地址时，会自动取消之前的默认地址
- 删除默认地址时，会自动将第一个地址设为默认地址

### 2. 地址验证规则
- 收货人姓名、电话、省份、城市、区县、详细地址为必填字段
- 电话号码需要符合基本格式要求
- 邮政编码为可选字段

### 3. 购买流程规则
- 虚拟商品不需要收货地址
- 实物商品必须提供收货地址
- 地址选择按照优先级自动处理

## 前端集成建议

### 1. 地址管理页面
- 地址列表展示
- 添加/编辑地址表单
- 默认地址切换
- 地址删除确认

### 2. 购买页面集成
- 地址选择器组件
- 快速添加地址功能
- 默认地址标识
- 地址编辑快捷入口

### 3. 用户体验优化
- 地址输入智能提示
- 省市区三级联动
- 常用地址快速选择
- 地址标签分类（家、公司等）

## 错误处理

### 常见错误码
- `地址不存在`: 选择的地址ID无效
- `地址已禁用`: 选择的地址状态为禁用
- `未设置默认地址`: 用户没有设置默认地址且未传入地址信息
- `实物商品需要提供收货地址`: 购买实物商品但未提供任何地址信息

### 错误处理建议
- 提供友好的错误提示
- 引导用户完善地址信息
- 自动重试机制
- 异常情况回退方案

## 扩展功能

未来可考虑的扩展功能：
- 地址导入导出
- 地址智能推荐
- 配送范围验证
- 地址标准化服务
- 批量地址管理
- 地址分享功能
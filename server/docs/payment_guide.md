# 支付系统使用指南

## 概述

本支付系统为 gin-vue-admin 项目提供了微信支付和支付宝支付的完整解决方案，包含订单创建、状态查询、异步通知处理、退款等功能。

## 功能特性

- ✅ 微信支付和支付宝支付支持
- ✅ 统一的支付接口设计
- ✅ 支付订单管理
- ✅ 异步通知处理
- ✅ 退款功能
- ✅ 支付配置管理
- ✅ 完整的数据库模型
- ✅ RESTful API设计

## 数据库表结构

### 1. 支付订单表 (payment_orders)
```sql
CREATE TABLE `payment_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(191) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户ID',
  `amount` bigint(20) NOT NULL COMMENT '支付金额(分)',
  `currency` varchar(191) DEFAULT 'CNY' COMMENT '货币类型',
  `subject` varchar(191) NOT NULL COMMENT '订单标题',
  `body` text COMMENT '订单描述',
  `payment_method` varchar(191) NOT NULL COMMENT '支付方式',
  `status` int(11) DEFAULT '1' COMMENT '支付状态',
  `third_order_no` varchar(191) DEFAULT NULL COMMENT '第三方订单号',
  `third_response` text COMMENT '第三方响应',
  `notify_url` varchar(191) DEFAULT NULL COMMENT '异步通知地址',
  `return_url` varchar(191) DEFAULT NULL COMMENT '同步返回地址',
  `pay_time` datetime(3) DEFAULT NULL COMMENT '支付时间',
  `expire_time` datetime(3) DEFAULT NULL COMMENT '过期时间',
  `refund_amount` bigint(20) DEFAULT '0' COMMENT '已退款金额(分)',
  `client_ip` varchar(191) DEFAULT NULL COMMENT '客户端IP',
  `extra` json DEFAULT NULL COMMENT '扩展字段',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_payment_orders_order_no` (`order_no`),
  KEY `idx_payment_orders_deleted_at` (`deleted_at`)
);
```

### 2. 支付配置表 (payment_configs)
```sql
CREATE TABLE `payment_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL COMMENT '配置名称',
  `payment_method` varchar(191) NOT NULL COMMENT '支付方式',
  `config` text NOT NULL COMMENT '配置JSON',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `remark` varchar(191) DEFAULT NULL COMMENT '备注',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_payment_configs_name` (`name`),
  KEY `idx_payment_configs_deleted_at` (`deleted_at`)
);
```

### 3. 退款记录表 (payment_refunds)
```sql
CREATE TABLE `payment_refunds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `refund_no` varchar(191) NOT NULL COMMENT '退款单号',
  `order_no` varchar(191) NOT NULL COMMENT '原订单号',
  `payment_order_id` bigint(20) unsigned NOT NULL COMMENT '支付订单ID',
  `refund_amount` bigint(20) NOT NULL COMMENT '退款金额(分)',
  `refund_reason` varchar(191) NOT NULL COMMENT '退款原因',
  `status` int(11) DEFAULT '1' COMMENT '退款状态',
  `third_refund_no` varchar(191) DEFAULT NULL COMMENT '第三方退款单号',
  `third_response` text COMMENT '第三方响应',
  `refund_time` datetime(3) DEFAULT NULL COMMENT '退款时间',
  `finish_time` datetime(3) DEFAULT NULL COMMENT '退款完成时间',
  `remark` varchar(191) DEFAULT NULL COMMENT '备注',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_payment_refunds_refund_no` (`refund_no`),
  KEY `idx_payment_refunds_order_no` (`order_no`),
  KEY `idx_payment_refunds_deleted_at` (`deleted_at`)
);
```

## API接口

### 1. 创建支付订单
```
POST /api/payment/create
```

请求参数：
```json
{
  "userId": 1,
  "amount": 10000,
  "subject": "商品标题",
  "body": "商品描述",
  "paymentMethod": "wechat",
  "notifyUrl": "https://example.com/notify",
  "returnUrl": "https://example.com/return",
  "expireMinutes": 30
}
```

响应：
```json
{
  "code": 0,
  "data": {
    "orderNo": "PAY20231201120000123456",
    "paymentUrl": "weixin://wxpay/bizpayurl?pr=PAY20231201120000123456",
    "expireTime": "2023-12-01T12:30:00Z"
  },
  "msg": "创建成功"
}
```

### 2. 查询支付状态
```
GET /api/payment/query/{orderNo}
```

响应：
```json
{
  "code": 0,
  "data": {
    "orderNo": "PAY20231201120000123456",
    "thirdOrderNo": "wx_PAY20231201120000123456",
    "status": 2,
    "amount": 10000,
    "payTime": "2023-12-01T12:05:00Z",
    "thirdResponse": "SUCCESS"
  },
  "msg": "查询成功"
}
```

### 3. 申请退款
```
POST /api/payment/refund
```

请求参数：
```json
{
  "orderNo": "PAY20231201120000123456",
  "refundAmount": 5000,
  "refundReason": "用户申请退款",
  "remark": "部分退款"
}
```

### 4. 获取支付订单列表
```
GET /api/payment/list?page=1&pageSize=10&orderNo=&status=
```

### 5. 获取支付配置
```
GET /api/payment/config
```

### 6. 异步通知接口
```
POST /api/payment/notify/wechat   # 微信支付通知
POST /api/payment/notify/alipay   # 支付宝通知
```

## 支付状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | 待支付 | 订单已创建，等待用户支付 |
| 2 | 已支付 | 支付成功 |
| 3 | 支付失败 | 支付失败 |
| 4 | 已取消 | 订单已取消 |
| 5 | 已退款 | 订单已全额退款 |
| 6 | 部分退款 | 订单已部分退款 |

## 配置说明

### 微信支付配置
```json
{
  "appId": "wxxxxxxxxxxx",
  "mchId": "1234567890",
  "apiKey": "your_api_key",
  "certPath": "/path/to/cert.pem",
  "keyPath": "/path/to/key.pem",
  "notifyUrl": "https://your-domain.com/api/payment/notify/wechat"
}
```

### 支付宝配置
```json
{
  "appId": "2021000000000000",
  "privateKey": "your_private_key",
  "publicKey": "alipay_public_key",
  "isProduction": false,
  "notifyUrl": "https://your-domain.com/api/payment/notify/alipay",
  "returnUrl": "https://your-domain.com/payment/return",
  "signType": "RSA2"
}
```

## 使用示例

### 1. 在业务代码中创建支付订单

```go
package main

import (
    "github.com/flipped-aurora/gin-vue-admin/server/service"
    paymentUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/payment"
)

func CreateOrder() {
    req := paymentUtils.CreatePaymentRequest{
        UserID:        1,
        Amount:        10000, // 100.00元，以分为单位
        Subject:       "商品购买",
        Body:          "购买商品详情",
        PaymentMethod: "wechat",
        ExpireMinutes: 30,
    }
    
    result, err := service.ServiceGroupApp.PaymentServiceGroup.PaymentService.CreatePayment(req)
    if err != nil {
        // 处理错误
        return
    }
    
    // 使用 result.PaymentURL 生成二维码或跳转链接
    // result.OrderNo 为订单号
}
```

### 2. 查询支付状态

```go
func CheckPaymentStatus(orderNo string) {
    result, err := service.ServiceGroupApp.PaymentServiceGroup.PaymentService.QueryPayment(orderNo)
    if err != nil {
        // 处理错误
        return
    }
    
    // 检查 result.Status 获取支付状态
    switch result.Status {
    case payment.PaymentStatusPaid:
        // 支付成功，处理业务逻辑
    case payment.PaymentStatusFailed:
        // 支付失败
    case payment.PaymentStatusPending:
        // 等待支付
    }
}
```

### 3. 处理退款

```go
func ProcessRefund(orderNo string, amount int64, reason string) {
    req := paymentUtils.RefundRequest{
        OrderNo:      orderNo,
        RefundAmount: amount,
        RefundReason: reason,
    }
    
    err := service.ServiceGroupApp.PaymentServiceGroup.PaymentService.RefundPayment(req)
    if err != nil {
        // 处理退款失败
        return
    }
    
    // 退款申请成功
}
```

## 安全注意事项

1. **配置安全**: 支付配置中的密钥信息要妥善保管，不要提交到代码仓库
2. **通知验签**: 异步通知必须验证签名，确保来源可信
3. **重复处理**: 异步通知可能重复发送，需要做幂等性处理
4. **金额校验**: 支付金额以分为单位，避免浮点数精度问题
5. **订单状态**: 只有在最终确认支付成功后才能发货或提供服务

## 扩展开发

### 添加新的支付方式

1. 在 `PaymentMethod` 枚举中添加新的支付方式
2. 实现 `PaymentInterface` 接口
3. 在 `getPaymentClient` 方法中添加对应的初始化逻辑
4. 添加相应的配置结构体

### 自定义业务逻辑

可以在支付状态变更时添加钩子函数，处理具体的业务逻辑：

```go
// 在支付成功后的处理
func OnPaymentSuccess(order *payment.PaymentOrder) {
    // 发货、发放虚拟商品等业务逻辑
}

// 在退款成功后的处理  
func OnRefundSuccess(refund *payment.PaymentRefund) {
    // 库存回滚、积分退还等业务逻辑
}
```

## 常见问题

### Q: 如何测试支付功能？
A: 使用微信和支付宝提供的沙箱环境进行测试，配置测试账号和测试密钥。

### Q: 异步通知超时怎么办？
A: 支付平台会重试异步通知，服务器应该在收到通知后及时响应。如果处理复杂，建议先响应成功，然后异步处理业务逻辑。

### Q: 支付金额精度问题？
A: 统一使用分为单位存储和计算，避免浮点数精度问题。

### Q: 如何处理订单超时？
A: 可以使用定时任务检查过期的订单，自动取消并释放资源。

## 技术支持

如有问题，请参考：
- 微信支付官方文档：https://pay.weixin.qq.com/wiki/doc/api/index.html
- 支付宝开放平台：https://open.alipay.com/
- gin-vue-admin 文档：https://www.gin-vue-admin.com/ 
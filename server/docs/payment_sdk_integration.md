# 支付SDK完整集成文档

## 概述

本文档描述了如何在gin-vue-admin项目中完整集成微信支付和支付宝支付SDK，实现真正的支付功能。

## 依赖版本

项目已经包含了必要的SDK依赖：

```go
github.com/wechatpay-apiv3/wechatpay-go v0.2.20
github.com/smartwalle/alipay/v3 v3.2.23
```

## 配置说明

### 配置文件结构

在 `config.yaml` 中的支付配置：

```yaml
payment:
    wechat:
        enabled: true
        app-id: "wx1234567890123456"           # 微信应用ID
        mch-id: "1900000000"                   # 微信商户号
        api-key: "your_apiv3_key_here"         # API v3密钥
        cert-path: "/path/to/apiclient_cert.pem"  # 商户证书路径
        key-path: "/path/to/apiclient_key.pem"    # 商户私钥路径
        notify-url: "https://yourdomain.com/api/payment/notify/wechat"
        is-sandbox: false                      # 是否沙箱环境
    alipay:
        enabled: true
        app-id: "2021000000000000"             # 支付宝应用ID
        private-key: "your_rsa_private_key"    # 应用私钥
        public-key: "alipay_rsa_public_key"    # 支付宝公钥
        is-production: false                   # 是否生产环境
        notify-url: "https://yourdomain.com/api/payment/notify/alipay"
        return-url: "https://yourdomain.com/payment/return"
        sign-type: "RSA2"                      # 签名类型
```

### 证书和密钥获取

#### 微信支付证书

1. 登录微信商户平台
2. 进入【账户中心】-> 【API安全】
3. 下载商户证书和私钥文件
4. 将证书文件放置在安全目录

#### 支付宝密钥

1. 登录支付宝开放平台
2. 进入【开发者中心】-> 【开发服务】
3. 配置应用公钥，获取支付宝公钥
4. 生成应用私钥

## 功能实现

### 微信支付功能

#### 已实现功能

1. **Native支付（扫码支付）**
   - 生成支付二维码
   - 支持订单过期时间设置
   - 完整的商品详情支持

2. **支付查询**
   - 根据商户订单号查询
   - 实时状态同步
   - 支付时间解析

3. **异步通知验证**
   - 签名验证（简化版）
   - 数据解密（需要完整HTTP请求）

#### 待完善功能

1. **退款功能**
   - 当前为简化实现
   - 需要集成 `refunddomestic` API

2. **完整通知验证**
   - 需要HTTP请求头进行完整签名验证

#### 代码示例

```go
// 创建支付订单
wechatConfig := paymentUtils.WechatConfig{
    AppID:     "wx1234567890123456",
    MchID:     "1900000000",
    APIKey:    "your_apiv3_key",
    CertPath:  "/path/to/cert.pem",
    KeyPath:   "/path/to/key.pem",
    NotifyUrl: "https://yourdomain.com/notify",
}

client, err := paymentUtils.NewWechatPayClient(wechatConfig)
if err != nil {
    log.Fatal(err)
}

// 创建支付
order := &payment.PaymentOrder{
    OrderNo: "ORDER123456",
    Amount:  10000, // 100.00元，单位：分
    Subject: "测试商品",
    Body:    "商品描述",
}

paymentURL, err := client.CreatePayment(order)
```

### 支付宝功能

#### 已实现功能

1. **统一收单交易创建**
   - 完整的API参数支持
   - 金额单位自动转换（分->元）
   - 超时时间设置

2. **交易查询**
   - 多种交易状态处理
   - 金额和时间字段解析
   - 完整响应数据序列化

3. **交易退款**
   - 同步退款结果
   - 退款状态判断
   - 退款金额验证

4. **异步通知验证**
   - 完整签名验证
   - 参数格式转换
   - 多种通知状态处理

#### 代码示例

```go
// 创建支付宝客户端
alipayConfig := paymentUtils.AlipayConfig{
    AppID:        "2021000000000000",
    PrivateKey:   "your_private_key",
    PublicKey:    "alipay_public_key",
    IsProduction: false,
    NotifyUrl:    "https://yourdomain.com/notify",
    ReturnUrl:    "https://yourdomain.com/return",
    SignType:     "RSA2",
}

client, err := paymentUtils.NewAlipayClient(alipayConfig)
if err != nil {
    log.Fatal(err)
}

// 创建支付
order := &payment.PaymentOrder{
    OrderNo: "ORDER123456",
    Amount:  10000, // 100.00元，单位：分
    Subject: "测试商品",
}

paymentURL, err := client.CreatePayment(order)
```

## API接口说明

### 支付相关接口

1. **POST /api/payment/create** - 创建支付订单
2. **GET /api/payment/query/{orderNo}** - 查询支付状态
3. **POST /api/payment/refund** - 申请退款
4. **GET /api/payment/config** - 获取支付配置
5. **GET /api/payment/list** - 获取订单列表

### 通知接口

1. **POST /api/payment/notify/wechat** - 微信支付通知
2. **POST /api/payment/notify/alipay** - 支付宝支付通知

## 安全注意事项

### 1. 证书和密钥安全

- 私钥文件权限设置为 600
- 不要将私钥提交到版本控制系统
- 生产环境使用环境变量或密钥管理服务

### 2. 签名验证

- 所有异步通知必须验证签名
- 验证通知来源的真实性
- 防止重复处理同一通知

### 3. 金额处理

- 统一使用分为单位存储
- 避免浮点数精度问题
- 金额计算使用整数运算

### 4. 配置安全

```go
// 敏感配置不在API中返回
config := map[string]interface{}{
    "wechat": map[string]interface{}{
        "enabled":    true,
        "app_id":     "wx123456",
        // 敏感信息不返回
        "api_key":   "***",
        "cert_path": "***",
        "key_path":  "***",
    },
}
```

## 部署说明

### 1. 证书文件部署

```bash
# 创建证书目录
mkdir -p /opt/app/certs

# 复制证书文件
cp apiclient_cert.pem /opt/app/certs/
cp apiclient_key.pem /opt/app/certs/

# 设置权限
chmod 600 /opt/app/certs/*
chown app:app /opt/app/certs/*
```

### 2. 配置文件更新

```yaml
payment:
    wechat:
        cert-path: "/opt/app/certs/apiclient_cert.pem"
        key-path: "/opt/app/certs/apiclient_key.pem"
```

### 3. 环境变量

```bash
export WECHAT_API_KEY="your_api_v3_key"
export ALIPAY_PRIVATE_KEY="your_rsa_private_key"
```

## 测试指南

### 1. 沙箱环境配置

#### 微信支付沙箱

```yaml
wechat:
    is-sandbox: true
    # 使用沙箱商户号和密钥
```

#### 支付宝沙箱

```yaml
alipay:
    is-production: false
    app-id: "沙箱应用ID"
    # 使用沙箱密钥
```

### 2. 测试用例

```go
func TestPaymentCreate(t *testing.T) {
    // 测试创建支付订单
    req := &CreatePaymentRequest{
        Amount:        10000,
        Subject:       "测试商品",
        PaymentMethod: payment.PaymentMethodWechat,
    }
    
    resp, err := PaymentServiceApp.CreatePayment(req)
    assert.NoError(t, err)
    assert.NotEmpty(t, resp.OrderNo)
}
```

## 故障排除

### 常见问题

1. **证书路径错误**
   - 检查文件路径是否正确
   - 验证文件权限

2. **签名验证失败**
   - 确认密钥配置正确
   - 检查时间戳是否在有效范围内

3. **通知接收失败**
   - 确认通知URL可外网访问
   - 检查防火墙设置

### 日志调试

```go
// 启用详细日志
global.GVA_LOG.Debug("支付请求", 
    zap.String("orderNo", orderNo),
    zap.Any("request", req))
```

## 扩展功能

### 1. 支付方式扩展

可以通过实现 `PaymentInterface` 接口来添加新的支付方式：

```go
type NewPaymentClient struct{}

func (n *NewPaymentClient) CreatePayment(order *payment.PaymentOrder) (string, error) {
    // 实现新支付方式的创建逻辑
}

func (n *NewPaymentClient) QueryPayment(orderNo string) (*PaymentQueryResult, error) {
    // 实现查询逻辑
}

// 实现其他接口方法...
```

### 2. 异步处理

对于高并发场景，可以使用消息队列处理支付通知：

```go
// 将支付通知放入队列
func HandlePaymentNotify(notifyData []byte) {
    // 快速验证并入队
    queue.Push("payment_notify", notifyData)
    
    // 立即返回成功响应
    return "SUCCESS"
}
```

## 更新日志

- v1.0.0: 基础支付功能实现
- v1.1.0: 配置文件化改造  
- v1.2.0: 完整SDK集成
- v1.3.0: 安全性增强

## 参考链接

- [微信支付API v3文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)
- [支付宝开放平台文档](https://opendocs.alipay.com/)
- [微信支付Go SDK](https://github.com/wechatpay-apiv3/wechatpay-go)
- [支付宝Go SDK](https://github.com/smartwalle/alipay) 
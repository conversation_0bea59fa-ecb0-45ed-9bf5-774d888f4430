## API接口列表

### 1. 获取微信授权URL
**接口地址**: `GET /api/base/wechat/auth-url`

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "authUrl": "https://open.weixin.qq.com/connect/qrconnect?appid=wx27cdbc7d0ba97938&redirect_uri=https%3A%2F%2Fmcpcn.cc%2Fapi%2Fbase%2Fwechat%2Fcallback&response_type=code&scope=snsapi_login&state=random_state_string",
    "state": "random_state_string"
  },
  "msg": "获取授权URL成功"
}
```

### 2. 处理微信授权回调
**接口地址**: `POST /api/base/wechat/callback`

**请求参数**:
```json
{
  "code": "WECHAT_AUTHORIZATION_CODE",
  "state": "random_state_string"
}
```

**成功登录响应** (用户已存在):
```json
{
  "code": 0,
  "data": {
    "action": "login",
    "token": "JWT_TOKEN",
    "user": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      // ... 其他用户信息
    },
    "expiresAt": 1638360000000
  },
  "msg": "登录成功"
}
```

**需要绑定响应** (用户不存在):
```json
{
  "code": 0,
  "data": {
    "action": "bind",
    "tempData": {
      "openId": "WECHAT_OPENID",
      "unionId": "WECHAT_UNIONID",
      "nickname": "微信昵称",
      "headImgUrl": "微信头像URL",
      "state": "random_state_string"
    }
  },
  "msg": "请绑定已有账号或注册新账号"
}
```

### 3. 绑定微信到已有账号 ⭐
**接口地址**: `POST /api/base/wechat/bind`

**请求参数**:
```json
{
  "code": "WECHAT_AUTHORIZATION_CODE",
  "state": "random_state_string",
  "username": "existing_username",
  "password": "user_password"
}
```

**成功响应**:
```json
{
  "code": 0,
  "data": {
    "token": "JWT_TOKEN",
    "user": {
      "id": 1,
      "username": "existing_username",
      // ... 其他用户信息，现在包含微信信息
      "mcpcnWechatOpenid": "WECHAT_OPENID",
      "mcpcnWechatUnionid": "WECHAT_UNIONID"
    },
    "expiresAt": 1638360000000
  },
  "msg": "绑定成功并登录"
}
```

**防重复绑定错误响应**:
```json
{
  "code": 7,
  "msg": "绑定失败: 该账号已绑定微信，无法重复绑定"
}
```

**微信账号已被绑定错误响应**:
```json
{
  "code": 7,
  "msg": "绑定失败: 该微信账号已被其他用户绑定"
}
```

### 4. 检查微信绑定状态 🆕
**接口地址**: `GET /api/user/wechat-binding-status`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "userId": 1,
    "username": "testuser",
    "isBound": true,
    "openId": "WECHAT_OPENID",
    "unionId": "WECHAT_UNIONID",
    "bindingTime": "2023-12-01T10:30:00Z"
  },
  "msg": "获取绑定状态成功"
}
```

### 5. 解绑微信账号 🆕
**接口地址**: `POST /api/user/unbind-wechat`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**成功响应**:
```json
{
  "code": 0,
  "msg": "解绑成功"
}
```

**错误响应**:
```json
{
  "code": 7,
  "msg": "解绑失败: 该账号尚未绑定微信"
}
```

## 防重复绑定机制说明

### 🔒 安全检查机制
1. **当前用户检查**: 验证当前用户是否已经绑定过微信
2. **微信账号检查**: 验证该微信是否已被其他用户绑定
3. **数据一致性检查**: 防止OpenID和UnionID绑定冲突

### 📋 绑定流程保护
```mermaid
flowchart TD
    A[用户尝试绑定] --> B[验证账号密码]
    B -->|失败| C[返回账号密码错误]
    B -->|成功| D[检查当前用户是否已绑定]
    D -->|已绑定| E[返回重复绑定错误]
    D -->|未绑定| F[检查微信是否被其他用户绑定]
    F -->|已被绑定| G[返回微信已被绑定错误]
    F -->|未被绑定| H[执行绑定操作]
    H --> I[绑定成功]
```

### ⚠️ 错误情况处理
- **用户已绑定**: "该账号已绑定微信，无法重复绑定"
- **微信已被绑定**: "该微信账号已被其他用户绑定"  
- **数据冲突**: "微信账号数据异常，请联系管理员处理"

### 🛠️ 管理功能
- **查看绑定状态**: 用户可随时查看当前微信绑定状态
- **解绑功能**: 用户可主动解除微信绑定
- **重新绑定**: 解绑后可以绑定新的微信账号

### 🔄 解绑后重新绑定流程
1. 用户调用解绑接口 `/api/user/unbind-wechat`
2. 系统清空用户的微信绑定信息
3. 用户可以重新进行微信授权绑定流程
4. 该微信号可以绑定到其他账号（如果需要）

## JavaScript使用示例

### 检查绑定状态
```javascript
async function checkWechatBinding() {
  try {
    const response = await fetch('/api/user/wechat-binding-status', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (result.code === 0) {
      console.log('绑定状态:', result.data);
      return result.data;
    } else {
      console.error('获取绑定状态失败:', result.msg);
    }
  } catch (error) {
    console.error('网络错误:', error);
  }
}
```

### 解绑微信
```javascript
async function unbindWechat() {
  try {
    const response = await fetch('/api/user/unbind-wechat', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (result.code === 0) {
      alert('解绑成功');
      // 刷新绑定状态
      await checkWechatBinding();
    } else {
      alert('解绑失败: ' + result.msg);
    }
  } catch (error) {
    console.error('网络错误:', error);
    alert('网络错误，请稍后重试');
  }
}
```

### 绑定微信（带重复绑定检查）
```javascript
async function bindWechatAccount(code, state, username, password) {
  try {
    const response = await fetch('/api/base/wechat/bind', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code,
        state,
        username,
        password
      })
    });
    
    const result = await response.json();
    if (result.code === 0) {
      // 绑定成功，保存token
      localStorage.setItem('token', result.data.token);
      alert('绑定成功并登录');
      // 跳转到主页面
      window.location.href = '/dashboard';
    } else {
      // 显示具体错误信息
      if (result.msg.includes('已绑定微信')) {
        alert('该账号已经绑定过微信，无法重复绑定');
      } else if (result.msg.includes('已被其他用户绑定')) {
        alert('该微信账号已被其他用户绑定，请使用其他微信账号');
      } else {
        alert('绑定失败: ' + result.msg);
      }
    }
  } catch (error) {
    console.error('网络错误:', error);
    alert('网络错误，请稍后重试');
  }
}
``` 
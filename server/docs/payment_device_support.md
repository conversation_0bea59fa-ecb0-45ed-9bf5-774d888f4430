# 支付系统设备类型支持文档

## 概述

支付系统现已支持根据不同设备类型选择合适的支付方式，以提供更好的用户体验。

## 支持的设备类型

### 设备类型枚举

```go
type DeviceType string

const (
    DeviceTypePC      DeviceType = "pc"      // 电脑网站
    DeviceTypeMobile  DeviceType = "mobile"  // 手机网站  
    DeviceTypeApp     DeviceType = "app"     // 手机应用
    DeviceTypeWechat  DeviceType = "wechat"  // 微信内部（微信浏览器、小程序等）
)
```

## 支付方式选择

### 支付宝支付

| 设备类型 | 支付接口 | 产品码 | 说明 |
|---------|---------|--------|------|
| **PC** | TradePagePay | FAST_INSTANT_TRADE_PAY | 电脑网站支付，跳转到支付宝收银台 |
| **Mobile** | TradeWapPay | QUICK_WAP_WAY | 手机网站支付，适配移动端浏览器 |
| **App** | TradePagePay | FAST_INSTANT_TRADE_PAY | 暂时使用PC支付，后续可扩展为App支付 |

### 微信支付

| 设备类型 | 支付接口 | 说明 | 实现状态 |
|---------|---------|------|----------|
| **PC** | Native API | 扫码支付，返回二维码URL | ✅ 已实现 |
| **Mobile** | H5 API | 手机网站支付，返回跳转URL | ✅ 完整实现 |
| **Wechat** | JSAPI API | 微信内部支付，返回支付参数 | ✅ 完整实现 |
| **App** | Native API | 暂时使用扫码支付 | 🔄 待扩展 |

## API调用示例

### 创建支付订单

#### 微信JSAPI支付示例
```json
POST /payment/create

{
    "userId": 1,
    "amount": 10000,
    "subject": "测试商品",
    "body": "商品描述",
    "paymentMethod": "wechat",
    "deviceType": "wechat",
    "openId": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
    "expireMinutes": 30
}
```

#### 微信H5支付示例
```json
POST /payment/create

{
    "userId": 1,
    "amount": 10000,
    "subject": "测试商品",
    "body": "商品描述",
    "paymentMethod": "wechat",
    "deviceType": "mobile",
    "clientIp": "*************",
    "expireMinutes": 30
}
```

#### 支付宝手机网站支付示例
```json
POST /payment/create

{
    "userId": 1,
    "amount": 10000,
    "subject": "测试商品",
    "body": "商品描述",
    "paymentMethod": "alipay",
    "deviceType": "mobile",
    "expireMinutes": 30
}
```

### 参数说明

- `deviceType`: **必填**，设备类型 (`pc` | `mobile` | `app` | `wechat`)
- `openId`: 微信JSAPI支付时**必填**，用户在商户appid下的唯一标识
- `clientIp`: H5支付时**必填**，客户端IP地址
- 其他参数保持不变

### 响应示例

#### 微信JSAPI支付响应
```json
{
    "code": 0,
    "data": {
        "orderNo": "PAY20231201123456789",
        "paymentUrl": "",
        "expireTime": "2023-12-01T13:34:56Z",
        "jsapiParams": {
            "appId": "wxd678efh567hg6787",
            "timeStamp": "1701426896",
            "nonceStr": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
            "package": "prepay_id=wx201410272009395522657a690389285100",
            "signType": "RSA",
            "paySign": "oR9d8PuhnIc+YZ8cBHFCwfgpaK9gd7vaRvqRA7WMZzqq..."
        }
    },
    "msg": "success"
}
```

#### PC端微信支付响应
```json
{
    "code": 0,
    "data": {
        "orderNo": "PAY20231201123456789",
        "paymentUrl": "weixin://wxpay/bizpayurl?pr=abc123",
        "expireTime": "2023-12-01T13:34:56Z"
    },
    "msg": "success"
}
```

#### 手机端微信支付响应
```json
{
    "code": 0,
    "data": {
        "orderNo": "PAY20231201123456789",
        "paymentUrl": "https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=...",
        "expireTime": "2023-12-01T13:34:56Z"
    },
    "msg": "success"
}
```

#### 支付宝支付响应
```json
{
    "code": 0,
    "data": {
        "orderNo": "PAY20231201123456789", 
        "paymentUrl": "https://mclient.alipay.com/h5continue.htm?...",
        "expireTime": "2023-12-01T13:34:56Z"
    },
    "msg": "success"
}
```

## 前端集成建议

### 设备类型检测

```javascript
// 检测设备类型和环境
function getDeviceType() {
    const userAgent = navigator.userAgent;
    
    // 检测是否在微信内置浏览器中
    if (/MicroMessenger/i.test(userAgent)) {
        return 'wechat';
    }
    
    // 检测是否为移动设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    
    if (isMobile) {
        return 'mobile';
    } else {
        return 'pc';
    }
}

// 创建支付订单
async function createPayment(orderData) {
    const deviceType = getDeviceType();
    
    const paymentRequest = {
        ...orderData,
        deviceType: deviceType
    };
    
    // 如果是微信内部环境，需要获取用户openid
    if (deviceType === 'wechat') {
        const openId = await getWechatOpenId();
        if (!openId) {
            throw new Error('无法获取微信用户信息，请确保在微信环境中操作');
        }
        paymentRequest.openId = openId;
    }
    
    // 如果是H5支付，需要获取客户端IP
    if (deviceType === 'mobile') {
        paymentRequest.clientIp = await getClientIP();
    }
    
    const response = await fetch('/payment/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentRequest)
    });
    
    return response.json();
}
```
# 微信OAuth轮询接口使用说明

## 概述

为了支持前端轮询获取微信授权结果，我们修改了微信OAuth的处理流程：

1. **微信回调接口** (`POST /api/base/wechat/callback`) 现在只缓存授权code，不直接处理登录
2. **新增轮询接口** (`GET /api/base/wechat/poll-code`) 供前端轮询获取授权结果

## 接口详情

### 1. 微信授权回调接口（已修改）

**接口地址**: `POST /api/base/wechat/callback`

**请求参数**:
```json
{
  "code": "WECHAT_AUTHORIZATION_CODE",
  "state": "random_state_string"
}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "action": "code_cached",
    "state": "random_state_string"
  },
  "msg": "授权code已缓存，请轮询获取结果"
}
```

### 2. 轮询获取授权结果接口（新增）

**接口地址**: `GET /api/base/wechat/poll-code?state={state}`

**请求参数**:
- `state`: 微信授权时的state参数

**响应示例**:

#### 等待授权中
```json
{
  "code": 0,
  "data": {
    "status": "waiting",
    "state": "random_state_string"
  },
  "msg": "等待授权中"
}
```

#### 授权成功 - 直接登录（与UnifiedLogin格式一致）
```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户"
    },
    "token": "JWT_TOKEN",
    "expiresAt": 1638360000000,
    "provider": "wechat",
    "isNewUser": false,
    "tempData": null
  },
  "msg": "登录成功"
}
```

#### 授权成功 - 需要绑定账号（与UnifiedLogin格式一致）
```json
{
  "code": 0,
  "data": {
    "user": null,
    "token": "",
    "expiresAt": 0,
    "provider": "wechat",
    "isNewUser": false,
    "tempData": {
      "openid": "WECHAT_OPENID",
      "unionid": "WECHAT_UNIONID",
      "nickname": "微信昵称",
      "headimgurl": "头像URL",
      "state": "random_state_string"
    }
  },
  "msg": "需要绑定账号"
}
```

## 前端使用流程

1. **获取授权URL**: 调用 `GET /api/base/wechat/auth-url` 获取微信授权链接和state
2. **跳转微信授权**: 用户在微信中完成授权
3. **微信回调**: 微信会调用回调接口，将code缓存
4. **前端轮询**: 前端使用state参数轮询 `GET /api/base/wechat/poll-code` 接口
5. **处理结果**: 根据轮询结果进行相应处理（登录成功/需要绑定账号）

## 轮询建议

- **轮询间隔**: 建议每2-3秒轮询一次
- **超时时间**: 建议设置60秒超时，超时后停止轮询
- **错误处理**: 如果接口返回错误，应停止轮询并提示用户

## 缓存机制

- **state缓存**: 有效期10分钟，用于验证请求合法性
- **code缓存**: 有效期10分钟，一次性使用（获取后立即删除）
- **自动清理**: Redis会自动清理过期的缓存数据

## 安全考虑

1. **state验证**: 确保state参数的有效性，防止CSRF攻击
2. **一次性使用**: code只能使用一次，获取后立即删除
3. **时效性**: 所有缓存都有过期时间，避免长期占用内存

## 示例代码

### JavaScript轮询示例

```javascript
async function pollWechatAuth(state) {
  const maxAttempts = 30; // 最多轮询30次（约60秒）
  const interval = 2000; // 每2秒轮询一次
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(`/api/base/wechat/poll-code?state=${state}`);
      const result = await response.json();
      
      if (result.code === 0) {
        if (result.data.user !== undefined) {
          // 授权成功，处理结果（与UnifiedLogin格式一致）
          if (result.data.user && result.data.token) {
            // 直接登录成功
            localStorage.setItem('token', result.data.token);
            window.location.href = '/dashboard';
          } else if (result.data.tempData) {
            // 需要绑定账号
            showBindAccountDialog(result.data.tempData);
          }
          return;
        } else if (result.data.status === 'waiting') {
          // 继续等待
          await new Promise(resolve => setTimeout(resolve, interval));
          continue;
        }
      } else {
        // 接口返回错误
        console.error('轮询失败:', result.msg);
        break;
      }
    } catch (error) {
      console.error('轮询请求失败:', error);
      break;
    }
  }
  
  // 超时或失败
  alert('授权超时，请重试');
}
```

# Token 验证与刷新机制详解

## 概述

本系统采用JWT + 会话管理的双重验证机制，支持Token自动刷新，避免用户频繁重新登录。

## 时间线配置

### 配置参数
```yaml
jwt:
  expires-time: 24h    # Token过期时间
  buffer-time: 30d     # 缓冲时间（刷新窗口期）
```

### 时间线说明
```
登录时间    Token过期时间    缓冲期结束时间
    |           |              |
    |<--24小时-->|<----30天---->|
    |           |              |
  创建Token   Token过期      会话失效
```

## Token状态分类

### 1. 有效状态 (TOKEN_VALID)
- **条件**: Token未过期 && 会话活跃 && 不在黑名单
- **行为**: 正常使用，自动更新活跃时间
- **前端处理**: 继续使用当前Token

### 2. 过期但可刷新 (TOKEN_EXPIRED + canRefresh=true)
- **条件**: Token已过期 && 会话在缓冲期内 && 不在黑名单
- **行为**: 需要刷新Token
- **前端处理**: 调用刷新接口获取新Token

### 3. 会话过期 (SESSION_EXPIRED)
- **条件**: 会话超出缓冲期 || 会话被停用
- **行为**: 需要重新登录
- **前端处理**: 清除本地Token，跳转登录页

### 4. 被撤销 (TOKEN_BLACKLISTED)
- **条件**: Token在黑名单中（用户注销/管理员操作）
- **行为**: 立即失效
- **前端处理**: 清除本地Token，跳转登录页

## 哪些情况需要刷新Token

### ✅ 可以刷新的情况

1. **Token自然过期**
   - 错误代码: `TOKEN_EXPIRED`
   - 条件: `canRefresh: true`
   - 原因: Token过期但会话仍在缓冲期内

2. **即将过期的Token**
   - 在中间件中自动刷新
   - 条件: 剩余时间 < 缓冲时间

### ❌ 不能刷新的情况

1. **会话已过期**
   - 错误代码: `SESSION_EXPIRED`
   - 原因: 超出缓冲期限制

2. **Token被撤销**
   - 错误代码: `TOKEN_BLACKLISTED`
   - 原因: 用户主动注销或管理员操作

3. **Token格式错误**
   - 错误代码: `TOKEN_MALFORMED`, `TOKEN_SIGNATURE_INVALID`
   - 原因: Token被篡改或格式不正确

4. **用户被禁用**
   - 错误代码: `USER_DISABLED`
   - 原因: 管理员禁用用户账户

5. **登录数量超限**
   - 错误代码: `DEVICE_LOGIN_LIMIT_EXCEEDED`, `WEB_LOGIN_LIMIT_EXCEEDED`
   - 原因: 超出设备或网页登录数量限制

## 刷新机制详解

### 自动刷新（中间件）
```go
// 在JWT中间件中自动刷新即将过期的Token
if claims.ExpiresAt.Unix()-time.Now().Unix() < claims.BufferTime {
    // 生成新Token并通过Header返回
    newToken, _ := j.CreateTokenByOldToken(token, *claims)
    c.Header("new-token", newToken)
    c.Header("new-expires-at", strconv.FormatInt(newClaims.ExpiresAt.Unix(), 10))
}
```

### 手动刷新（API接口）

#### 设备登录刷新
```http
POST /api/userSession/refreshToken
Content-Type: application/json
x-token: expired-token

{
  "deviceId": "device-123"
}
```

#### 网页登录刷新
```http
POST /api/userSession/refreshToken
Content-Type: application/json
x-token: expired-token

{}
```

## 前端处理策略

### 1. 验证Token状态
```javascript
async function checkTokenStatus() {
  const response = await fetch('/api/jwt/verify', {
    method: 'POST',
    headers: { 'x-token': getToken() }
  });
  
  const result = await response.json();
  const data = result.data;
  
  if (data.valid) {
    // Token有效，继续使用
    return { status: 'valid', token: getToken() };
  }
  
  if (data.canRefresh) {
    // 可以刷新，尝试获取新Token
    return await refreshToken();
  }
  
  if (data.needsReauth) {
    // 需要重新认证
    clearToken();
    redirectToLogin();
    return { status: 'reauth_required' };
  }
}
```

### 2. 刷新Token
```javascript
async function refreshToken() {
  try {
    const deviceId = getDeviceId(); // 设备登录时需要
    
    const response = await fetch('/api/userSession/refreshToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-token': getToken()
      },
      body: JSON.stringify(deviceId ? { deviceId } : {})
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      const newToken = result.data.token;
      setToken(newToken);
      return { status: 'refreshed', token: newToken };
    } else {
      // 刷新失败，需要重新登录
      clearToken();
      redirectToLogin();
      return { status: 'refresh_failed' };
    }
  } catch (error) {
    console.error('刷新Token失败:', error);
    clearToken();
    redirectToLogin();
    return { status: 'refresh_error' };
  }
}
```

### 3. 自动处理响应头中的新Token
```javascript
// 拦截器处理自动刷新的Token
axios.interceptors.response.use(
  response => {
    const newToken = response.headers['new-token'];
    if (newToken) {
      setToken(newToken);
      console.log('Token已自动刷新');
    }
    return response;
  },
  error => {
    if (error.response?.status === 401) {
      // Token无效，尝试刷新或重新登录
      handleTokenError();
    }
    return Promise.reject(error);
  }
);
```

### 4. 统一错误处理
```javascript
function handleTokenError(errorCode) {
  const refreshableCodes = ['TOKEN_EXPIRED'];
  const reauthCodes = [
    'TOKEN_BLACKLISTED', 'SESSION_EXPIRED', 'USER_DISABLED',
    'TOKEN_MALFORMED', 'TOKEN_SIGNATURE_INVALID'
  ];
  
  if (refreshableCodes.includes(errorCode)) {
    return refreshToken();
  }
  
  if (reauthCodes.includes(errorCode)) {
    clearToken();
    redirectToLogin();
    return;
  }
  
  // 其他错误，默认重新登录
  clearToken();
  redirectToLogin();
}
```

## 最佳实践

### 1. 应用启动时检查
```javascript
// 应用启动时检查Token状态
async function initApp() {
  const token = getToken();
  if (!token) {
    redirectToLogin();
    return;
  }
  
  const result = await checkTokenStatus();
  if (result.status === 'valid' || result.status === 'refreshed') {
    // 继续应用初始化
    startApp();
  } else {
    // 需要重新登录
    redirectToLogin();
  }
}
```

### 2. 定期检查Token状态
```javascript
// 每5分钟检查一次Token状态
setInterval(async () => {
  if (getToken()) {
    await checkTokenStatus();
  }
}, 5 * 60 * 1000);
```

### 3. 用户活跃时自动刷新
```javascript
// 用户操作时检查Token是否需要刷新
document.addEventListener('click', debounce(async () => {
  const token = getToken();
  if (token) {
    const claims = parseJWT(token);
    const now = Date.now() / 1000;
    const timeLeft = claims.exp - now;
    
    // 如果剩余时间少于1小时，尝试刷新
    if (timeLeft < 3600) {
      await checkTokenStatus();
    }
  }
}, 60000)); // 1分钟内最多检查一次
```

## 总结

Token刷新的核心原则：
1. **Token过期但会话有效** → 可以刷新
2. **会话过期或被撤销** → 必须重新登录
3. **自动刷新优先** → 减少用户感知
4. **优雅降级** → 刷新失败时引导重新登录

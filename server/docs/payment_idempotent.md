# 支付系统防重复处理机制

## 概述

支付宝和微信支付会在未收到成功响应时重复发送回调通知，因此需要在系统中实现防重复处理机制，确保业务逻辑不会被重复执行。

## 多层防重复机制

### 1. 支付服务层防重复

位置：`service/payment/payment_service.go` - `ProcessNotify` 方法

```go
// 防重复处理
if order.Status == payment.PaymentStatusPaid {
    return nil
}
```

**机制说明：**
- 检查支付订单状态，如果已是"已支付"状态，直接返回成功
- 避免重复更新订单状态和触发后续业务逻辑

### 2. 产品服务层防重复

位置：`service/product/purchase_service.go` - `ProcessPaymentSuccess` 方法

#### 2.1 订单级防重复

```go
// 防重复处理：检查是否已经处理过
alreadyProcessed := true
for _, item := range orderItems {
    if item.Status != product.OrderItemStatusProcessed {
        alreadyProcessed = false
        break
    }
}

if alreadyProcessed {
    global.GVA_LOG.Info("订单已处理过，跳过重复处理", zap.String("orderNo", orderNo))
    return nil
}
```

#### 2.2 订单项级防重复（乐观锁）

```go
// 更新订单项状态为已处理（使用乐观锁防止重复更新）
result := tx.Model(&product.OrderItem{}).
    Where("id = ? AND status = ?", item.ID, product.OrderItemStatusPending).
    Update("status", product.OrderItemStatusProcessed)

// 如果受影响行数为0，说明状态已被其他进程更新，跳过后续处理
if result.RowsAffected == 0 {
    global.GVA_LOG.Warn("订单项状态已被更新，可能重复处理", 
        zap.String("orderNo", orderNo), 
        zap.Uint("itemId", item.ID))
    continue
}
```

### 3. 会员激活防重复

位置：`service/product/purchase_service.go` - `activateMembership` 方法

#### 3.1 订单级会员激活检查

```go
// 防重复激活：检查是否已经为此订单激活过会员
var existingActivation product.UserMembership
err := tx.Where("user_id = ? AND order_no = ? AND product_id = ?", 
    userID, orderNo, item.ProductID).First(&existingActivation).Error

if err == nil {
    // 已经为此订单激活过会员，跳过
    global.GVA_LOG.Info("订单已激活过会员，跳过重复激活", 
        zap.String("orderNo", orderNo),
        zap.Uint("userId", userID),
        zap.Uint("productId", item.ProductID))
    return nil
}
```

#### 3.2 积分发放防重复

```go
// 检查是否已经为此订单发放过积分
var pointsRecord system.SysUserPoints
err := tx.Where("user_id = ? AND order_id = ? AND type = 'order'", 
    userID, paymentOrder.ID).First(&pointsRecord).Error

if err == gorm.ErrRecordNotFound {
    // 未发放过积分，进行发放
    // ... 积分发放逻辑
} else {
    // 已经发放过积分，跳过
    global.GVA_LOG.Info("积分已发放过，跳过", 
        zap.String("orderNo", orderNo),
        zap.Uint("userId", userID))
}
```

## 处理流程

### 正常流程

1. 支付回调到达 → 支付服务层检查订单状态
2. 更新支付订单状态为"已支付"
3. 触发产品服务的支付成功处理
4. 检查订单项状态，处理未处理的订单项
5. 激活会员（如果是会员商品）
6. 发放积分、更新销量等

### 重复回调处理

1. **第一次回调：** 正常处理流程
2. **第二次及后续回调：**
   - 支付服务层：订单状态已是"已支付"，直接返回成功
   - 如果到达产品服务层：检查订单项状态，发现已处理，跳过
   - 如果到达会员激活：检查激活记录，发现已激活，跳过
   - 如果到达积分发放：检查积分记录，发现已发放，跳过

## 并发处理

### 数据库事务

所有关键操作都在数据库事务中执行，确保数据一致性：

```go
err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
    // 所有业务逻辑在事务中执行
    return nil
})
```

### 乐观锁机制

使用条件更新实现乐观锁：

```go
// 只有当状态为"待处理"时才更新为"已处理"
result := tx.Model(&product.OrderItem{}).
    Where("id = ? AND status = ?", item.ID, product.OrderItemStatusPending).
    Update("status", product.OrderItemStatusProcessed)

// 检查受影响行数，如果为0说明状态已被其他进程更新
if result.RowsAffected == 0 {
    // 跳过后续处理
    continue
}
```

## 监控和日志

### 关键日志点

1. **重复处理检测：**
   ```
   "订单已处理过，跳过重复处理"
   "订单项已处理，跳过"
   "订单已激活过会员，跳过重复激活"
   "积分已发放过，跳过"
   ```

2. **并发处理检测：**
   ```
   "订单项状态已被更新，可能重复处理"
   ```

3. **正常处理：**
   ```
   "支付成功处理完成"
   "延长现有会员时间"
   "创建新会员记录"
   "发放会员积分"
   ```

### 监控指标

建议监控以下指标：
- 重复回调次数
- 并发处理冲突次数
- 处理失败率
- 处理延时

## 最佳实践

1. **幂等性设计：** 确保相同参数的操作可以安全地重复执行
2. **状态检查：** 在每个关键步骤前检查当前状态
3. **事务隔离：** 使用数据库事务确保操作原子性
4. **详细日志：** 记录足够的日志信息用于问题排查
5. **异步处理：** 支付回调处理采用异步方式，避免阻塞回调响应

## 故障处理

### 常见问题

1. **回调超时：** 支付平台会重复发送，系统需要正确处理
2. **并发处理：** 多个回调同时到达，使用乐观锁处理
3. **部分失败：** 使用事务确保数据一致性
4. **数据不一致：** 通过状态检查和重试机制解决

### 恢复机制

1. **手动补偿：** 提供管理接口用于手动处理异常订单
2. **定时任务：** 定期检查和修复数据不一致问题
3. **告警机制：** 关键错误及时告警通知 
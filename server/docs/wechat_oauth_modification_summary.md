# 微信OAuth轮询功能修改总结

## 修改概述

根据需求，我们修改了微信OAuth回调功能，实现了以下功能：
1. 微信回调时将code缓存到Redis，以state为key
2. 新增轮询接口供前端查询授权结果
3. 轮询接口返回格式与UnifiedLogin保持一致

## 修改的文件

### 1. `server/api/v1/system/wechat_oauth.go`

#### 修改内容：
- **导入新包**：添加了 `time` 和 `service` 包的导入
- **添加服务引用**：定义了 `wechatOAuthService` 和 `jwtService` 变量
- **修改 `WechatCallback` 方法**：
  - 现在只缓存code到Redis，不直接处理登录
  - 返回简单的成功响应，告知前端开始轮询
- **新增 `PollWechatCode` 方法**：
  - 接收state参数，查询缓存的code
  - 如果找到code，处理微信授权并返回结果
  - 返回格式与UnifiedLogin完全一致
  - 支持设备登录和网站登录

#### 新增接口：
```go
// PollWechatCode 轮询获取微信授权code
// @Router /wechat/poll-code [get]
func (w *WechatOAuthApi) PollWechatCode(c *gin.Context)
```

### 2. `server/service/system/wechat_oauth.go`

#### 新增方法：
- **`CacheCodeByState`**：将微信授权code缓存到Redis
- **`GetCodeByState`**：根据state从Redis获取code
- **`validateStateWithoutDelete`**：验证state但不删除
- **`HandleCallbackForPolling`**：专门用于轮询的回调处理

#### 缓存机制：
- **Key格式**：`wechat_code:{state}`
- **过期时间**：10分钟
- **一次性使用**：获取后立即删除

### 3. `server/router/system/wechat_oauth.go`

#### 新增路由：
```go
wechatOAuthPublicRouter.GET("poll-code", wechatOAuthApi.PollWechatCode)
```

### 4. 文档文件

- **`server/docs/wechat_oauth_polling_api.md`**：详细的API使用文档
- **`server/docs/wechat_oauth_modification_summary.md`**：本修改总结文档

## API接口变化

### 原有接口修改

#### `POST /api/base/wechat/callback`
**修改前**：直接处理登录逻辑，返回登录结果
**修改后**：只缓存code，返回轮询提示

```json
// 新的响应格式
{
  "code": 0,
  "data": {
    "action": "code_cached",
    "state": "random_state_string"
  },
  "msg": "授权code已缓存，请轮询获取结果"
}
```

### 新增接口

#### `GET /api/base/wechat/poll-code?state={state}`

**等待状态响应**：
```json
{
  "code": 0,
  "data": {
    "status": "waiting",
    "state": "random_state_string"
  },
  "msg": "等待授权中"
}
```

**登录成功响应**（与UnifiedLogin格式一致）：
```json
{
  "code": 0,
  "data": {
    "user": { /* 用户信息 */ },
    "token": "JWT_TOKEN",
    "expiresAt": 1638360000000,
    "provider": "wechat",
    "isNewUser": false,
    "tempData": null
  },
  "msg": "登录成功"
}
```

**需要绑定账号响应**（与UnifiedLogin格式一致）：
```json
{
  "code": 0,
  "data": {
    "user": null,
    "token": "",
    "expiresAt": 0,
    "provider": "wechat",
    "isNewUser": false,
    "tempData": { /* 微信用户信息 */ }
  },
  "msg": "需要绑定账号"
}
```

## 技术实现细节

### 1. 缓存机制
- 使用Redis存储code，key为 `wechat_code:{state}`
- 设置10分钟过期时间
- 获取后立即删除，确保一次性使用

### 2. 状态验证
- 保持原有的state验证机制
- 新增不删除state的验证方法，用于轮询场景

### 3. 会话管理
- 集成现有的会话管理系统
- 支持设备登录和网站登录
- 自动处理token生成和cookie设置

### 4. 安全考虑
- 保持原有的CSRF防护
- Code只能使用一次
- 所有缓存都有过期时间
- 验证state的有效性

## 使用流程

1. **前端获取授权URL**：调用 `GET /api/base/wechat/auth-url`
2. **用户微信授权**：跳转到微信授权页面
3. **微信回调**：微信调用回调接口，code被缓存
4. **前端轮询**：使用state参数轮询 `GET /api/base/wechat/poll-code`
5. **处理结果**：根据返回结果进行登录或绑定操作

## 兼容性

- **向后兼容**：原有的绑定和注册接口保持不变
- **格式统一**：轮询接口返回格式与UnifiedLogin完全一致
- **现有功能**：不影响现有的微信OAuth功能

## 建议的前端实现

```javascript
// 轮询示例
async function pollWechatAuth(state) {
  const maxAttempts = 30; // 60秒超时
  const interval = 2000;  // 2秒间隔
  
  for (let i = 0; i < maxAttempts; i++) {
    const response = await fetch(`/api/base/wechat/poll-code?state=${state}`);
    const result = await response.json();
    
    if (result.code === 0 && result.data.user !== undefined) {
      // 授权完成，处理结果
      if (result.data.user && result.data.token) {
        // 登录成功
        localStorage.setItem('token', result.data.token);
        return { success: true, action: 'login', data: result.data };
      } else if (result.data.tempData) {
        // 需要绑定
        return { success: true, action: 'bind', data: result.data };
      }
    }
    
    // 继续等待
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  // 超时
  return { success: false, error: 'timeout' };
}
```

## 测试建议

1. **功能测试**：验证轮询接口的各种响应状态
2. **性能测试**：测试高并发轮询的性能
3. **安全测试**：验证state和code的安全性
4. **兼容性测试**：确保不影响现有功能
5. **超时测试**：验证缓存过期和清理机制

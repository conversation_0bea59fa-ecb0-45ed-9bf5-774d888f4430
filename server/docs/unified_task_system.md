# 统一任务系统设计文档

## 概述

本系统实现了统一的任务记录机制，所有积分变动都会同时记录用户任务完成记录和积分流水，确保数据的完整性和可追溯性。

## 设计原则

### 1. 双表记录原则
- **SysUserTask**: 记录任务完成的业务信息
- **SysUserPoints**: 记录积分变动的流水信息
- 通过 `UserTaskID` 字段建立关联关系

### 2. 统一接口原则
所有积分变动场景都使用统一的任务服务接口：
- `AddPointsWithTaskRecord()` - 普通积分变动
- `AddFreePointsWithTaskRecord()` - 免费积分变动
- `CompleteTask()` - 用户主动完成任务

## 应用场景

### 1. 用户主动完成任务
**场景**: 用户点击完成任务按钮
**接口**: `POST /task/completeTask`
**流程**:
1. 验证任务完成条件
2. 记录任务完成记录
3. 更新用户积分
4. 记录积分流水

### 2. 购买会员获得积分
**场景**: 用户购买会员时系统自动发放积分
**任务ID**: `config.TASK_MEMBERSHIP_PURCHASE`
**流程**:
```go
taskService.AddPointsWithTaskRecord(
    userID,
    totalPoints,
    "购买会员赠送积分",
    "order",
    config.TASK_MEMBERSHIP_PURCHASE,
)
```

### 3. 会员月度积分发放
**场景**: 定时任务为会员发放月度积分
**任务ID**: `config.TASK_MEMBERSHIP_MONTHLY`
**流程**:
```go
taskService.AddPointsWithTaskRecord(
    userID,
    monthlyPoints,
    "会员月度积分",
    "membership",
    config.TASK_MEMBERSHIP_MONTHLY,
)
```

### 4. 每日免费积分刷新
**场景**: 定时任务刷新用户免费积分到50
**任务ID**: `config.TASK_DAILY_FREE_POINTS`
**流程**:
```go
taskService.AddFreePointsWithTaskRecord(
    userID,
    needRefresh,
    "每日免费积分刷新",
    config.TASK_DAILY_FREE_POINTS,
)
```

### 5. 系统初始化免费积分
**场景**: 为新用户或现有用户初始化免费积分
**任务ID**: `config.TASK_INIT_FREE_POINTS`
**流程**:
```go
taskService.AddFreePointsWithTaskRecord(
    userID,
    50,
    "系统初始化免费积分",
    config.TASK_INIT_FREE_POINTS,
)
```

## 数据结构

### SysUserTask (用户任务完成记录)
```go
type SysUserTask struct {
    global.GVA_MODEL
    UserID      uint      // 用户ID
    TaskID      uint      // 任务ID
    CompletedAt time.Time // 完成时间
    Reward      int       // 获得的积分
    Status      string    // 状态
    Remark      string    // 备注
}
```

### SysUserPoints (积分流水记录)
```go
type SysUserPoints struct {
    global.GVA_MODEL
    UserID        uint   // 用户ID
    Change        int    // 变动积分
    Reason        string // 变动原因
    Type          string // 类型：task/order/membership等
    TaskID        uint   // 关联任务ID
    UserTaskID    uint   // 关联用户任务记录ID
    // ... 其他关联字段
}
```

## 任务配置

### 系统任务ID常量
```go
const (
    TASK_DAILY_SIGNIN        = 1  // 每日签到
    TASK_INVITE_FRIEND       = 2  // 邀请好友
    TASK_SHARE_CASE          = 3  // 分享案例
    TASK_FEEDBACK            = 4  // 反馈建议
    TASK_MEMBERSHIP_MONTHLY  = 5  // 会员月度积分
    TASK_WATCH_AD            = 6  // 观看广告
    TASK_MONTHLY_SURVEY      = 7  // 月度问卷
    TASK_DAILY_FREE_POINTS   = 8  // 每日免费积分刷新
    TASK_MEMBERSHIP_PURCHASE = 9  // 会员购买奖励
    TASK_INIT_FREE_POINTS    = 10 // 初始化免费积分
)
```

## 数据查询示例

### 1. 查询用户任务完成详情
```sql
SELECT 
    ut.id,
    ut.completed_at,
    t.title,
    ut.reward,
    sp.id as points_record_id,
    sp.type as points_type
FROM sys_user_tasks ut
JOIN sys_tasks t ON ut.task_id = t.id
JOIN sys_user_points sp ON sp.user_task_id = ut.id
WHERE ut.user_id = 1
ORDER BY ut.completed_at DESC;
```

### 2. 统计用户各类型任务获得的积分
```sql
SELECT 
    t.task_type,
    COUNT(ut.id) as completion_count,
    SUM(ut.reward) as total_points
FROM sys_user_tasks ut
JOIN sys_tasks t ON ut.task_id = t.id
WHERE ut.user_id = 1 AND ut.status = 'completed'
GROUP BY t.task_type;
```

### 3. 检查数据一致性
```sql
SELECT 
    ut.id as task_record_id,
    ut.reward as task_reward,
    sp.change as points_change,
    CASE 
        WHEN ut.reward = sp.change THEN 'OK'
        ELSE 'MISMATCH'
    END as status
FROM sys_user_tasks ut
JOIN sys_user_points sp ON sp.user_task_id = ut.id
WHERE ut.user_id = 1;
```

## 优势

### 1. 数据完整性
- 所有积分变动都有对应的任务记录
- 通过UserTaskID建立强关联关系
- 支持数据一致性检查

### 2. 业务可追溯
- 每笔积分都能追溯到具体的任务和完成时间
- 支持详细的积分来源分析
- 便于问题排查和数据审计

### 3. 统一管理
- 所有积分变动使用统一的服务接口
- 集中的任务配置管理
- 一致的数据记录格式

### 4. 扩展性好
- 新增积分场景只需添加对应的系统任务
- 支持不同类型的积分变动
- 便于后续功能扩展

## 注意事项

1. **任务ID管理**: 系统任务ID需要与数据库中的记录保持一致
2. **事务安全**: 所有积分操作都在数据库事务中执行
3. **性能考虑**: 大量任务记录可能影响查询性能，建议定期归档
4. **数据清理**: 可以定期清理过期的任务完成记录

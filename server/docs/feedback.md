# 反馈系统 API 接口文档（已移除标题字段）

## 概述

反馈系统提供用户反馈意见和建议的功能，包括创建、更新、删除、审核反馈以及获取不同类型的反馈列表。

## 基础信息

- **Base URL**: `/api/v1/feedback`
- **认证方式**: <PERSON><PERSON> (JWT)
- **Content-Type**: `application/json`

## 接口列表

### 1. 创建反馈

**接口地址**: `POST /feedback/create`

**接口描述**: 用户创建新的反馈

**请求参数**:

```json
{
  "content": "反馈内容详情",
  "type": "suggestion",
  "tags": "标签1,标签2"
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| content | string | 是 | 反馈内容，最少10字符 |
| type | string | 是 | 反馈类型：suggestion(建议)、bug(问题反馈)、feature(功能需求) |
| tags | string | 否 | 标签，多个标签用逗号分隔 |

**响应示例**:

```json
{
  "code": 0,
  "data": {
    "ID": 1,
    "CreatedAt": "2024-01-01T10:00:00Z",
    "UpdatedAt": "2024-01-01T10:00:00Z",
    "user_id": 123,
    "content": "反馈内容详情",
    "type": "suggestion",
    "status": "pending",
    "is_starred": false,
    "is_public": false,
    "admin_reply": "",
    "admin_user_id": 0,
    "view_count": 0,
    "like_count": 0,
    "tags": "标签1,标签2",
    "priority": 0,
    "remark": ""
  },
  "msg": "创建成功"
}
```

### 2. 更新反馈

**接口地址**: `PUT /feedback/update`

**接口描述**: 用户更新自己的反馈（仅限待审核状态）

**请求参数**:

```json
{
  "id": 1,
  "content": "更新后的反馈内容",
  "type": "bug",
  "tags": "新标签1,新标签2"
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | uint | 是 | 反馈ID |
| content | string | 是 | 反馈内容，最少10字符 |
| type | string | 是 | 反馈类型：suggestion、bug、feature |
| tags | string | 否 | 标签，多个标签用逗号分隔 |

**响应示例**:

```json
{
  "code": 0,
  "data": {},
  "msg": "更新成功"
}
```

### 3. 删除反馈

**接口地址**: `DELETE /feedback/delete/{id}`

**接口描述**: 用户删除自己的反馈

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 反馈ID |

**响应示例**:

```json
{
  "code": 0,
  "data": {},
  "msg": "删除成功"
}
```

### 4. 审核反馈

**接口地址**: `POST /feedback/review`

**接口描述**: 管理员审核反馈

**请求参数**:

```json
{
  "id": 1,
  "status": "approved",
  "is_starred": true,
  "is_public": true,
  "admin_reply": "感谢您的反馈，我们会认真考虑",
  "priority": 5,
  "remark": "重要反馈"
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | uint | 是 | 反馈ID |
| status | string | 是 | 审核状态：approved(通过)、rejected(拒绝) |
| is_starred | bool | 否 | 是否标星 |
| is_public | bool | 否 | 是否公开显示 |
| admin_reply | string | 否 | 管理员回复 |
| priority | int | 否 | 优先级，数字越大优先级越高 |
| remark | string | 否 | 备注 |

**响应示例**:

```json
{
  "code": 0,
  "data": {},
  "msg": "审核成功"
}
```

### 5. 获取用户反馈列表

**接口地址**: `GET /feedback/getUserList`

**接口描述**: 获取当前用户自己的反馈列表

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| content | string | 否 | 内容搜索 |
| type | string | 否 | 反馈类型筛选 |
| status | string | 否 | 状态筛选 |
| startCreatedAt | string | 否 | 创建时间开始 |
| endCreatedAt | string | 否 | 创建时间结束 |

**响应示例**:

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "ID": 1,
        "CreatedAt": "2024-01-01T10:00:00Z",
        "UpdatedAt": "2024-01-01T10:00:00Z",
        "user_id": 123,
        "content": "反馈内容",
        "type": "suggestion",
        "status": "pending",
        "is_starred": false,
        "is_public": false,
        "admin_reply": "",
        "view_count": 5,
        "like_count": 2,
        "tags": "标签1,标签2",
        "priority": 0,
        "user_name": "用户名",
        "user_avatar": "头像URL",
        "admin_name": ""
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 6. 获取公开反馈列表

**接口地址**: `GET /feedback/getPublicList`

**接口描述**: 获取公开的反馈列表（已审核通过且设置为公开的反馈）

**查询参数**: 同用户反馈列表

**响应示例**: 同用户反馈列表

### 7. 获取管理员反馈列表

**接口地址**: `GET /feedback/getAdminList`

**接口描述**: 管理员获取所有反馈列表

**查询参数**: 同用户反馈列表

**响应示例**: 同用户反馈列表

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 7 | 参数错误 |
| 1001 | 未登录 |
| 1002 | 权限不足 |
| 1003 | 反馈不存在 |
| 1004 | 只能操作自己的反馈 |
| 1005 | 反馈已审核，无法修改 |

## 数据模型

### Feedback 反馈模型（已移除标题字段）

```json
{
  "ID": 1,
  "CreatedAt": "2024-01-01T10:00:00Z",
  "UpdatedAt": "2024-01-01T10:00:00Z",
  "user_id": 123,
  "content": "反馈内容",
  "type": "suggestion",
  "status": "pending",
  "is_starred": false,
  "is_public": false,
  "admin_reply": "管理员回复",
  "admin_user_id": 456,
  "view_count": 10,
  "like_count": 5,
  "tags": "标签1,标签2",
  "priority": 3,
  "remark": "备注信息",
  "user_name": "用户名",
  "user_avatar": "头像URL",
  "admin_name": "管理员名称"
}
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| ID | uint | 反馈ID |
| CreatedAt | string | 创建时间 |
| UpdatedAt | string | 更新时间 |
| user_id | uint | 用户ID |
| content | string | 反馈内容 |
| type | string | 反馈类型：suggestion/bug/feature |
| status | string | 审核状态：pending/approved/rejected |
| is_starred | bool | 是否标星 |
| is_public | bool | 是否公开显示 |
| admin_reply | string | 管理员回复 |
| admin_user_id | uint | 审核管理员ID |
| view_count | int | 查看次数 |
| like_count | int | 点赞次数 |
| tags | string | 标签，多个标签用逗号分隔 |
| priority | int | 优先级，数字越大优先级越高 |
| remark | string | 备注 |
| user_name | string | 用户名（关联查询） |
| user_avatar | string | 用户头像（关联查询） |
| admin_name | string | 管理员名称（关联查询） |

## 使用示例

### 创建反馈示例

```bash
curl -X POST "http://localhost:8888/api/v1/feedback/create" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "希望能够增加暗色主题功能，这样在夜间使用时对眼睛更友好。",
    "type": "suggestion",
    "tags": "UI,主题,用户体验"
  }'
```

### 审核反馈示例

```bash
curl -X POST "http://localhost:8888/api/v1/feedback/review" \
  -H "Authorization: Bearer admin-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "status": "approved",
    "is_starred": true,
    "is_public": true,
    "admin_reply": "感谢您的建议，我们已将此功能加入开发计划。",
    "priority": 8
  }'
```

## 更新说明

- **已移除字段**: `title` (反馈标题)
- **影响范围**:
   - 数据模型 `Feedback` 结构体
   - 请求结构体 `CreateFeedbackRequest` 和 `UpdateFeedbackRequest`
   - API文档中的所有相关示例和说明
- **注意事项**: 如果数据库中已存在 `title` 字段，需要执行数据库迁移来删除该字段

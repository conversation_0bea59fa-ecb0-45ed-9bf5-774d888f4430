# 腾讯云短信服务集成指南

## 概述

本项目已集成腾讯云短信服务，支持发送短信验证码功能。

## 配置步骤

### 1. 腾讯云控制台配置

1. 登录腾讯云控制台
2. 进入短信服务 SMS 控制台
3. 创建短信应用，获取 App ID
4. 创建短信签名，获取签名名称
5. 创建短信模板，获取模板 ID
6. 获取腾讯云访问密钥 SecretId 和 SecretKey

### 2. 项目配置

在 `config.yaml` 文件中配置腾讯云短信服务：

```yaml
# 腾讯云短信配置
tencent-sms:
    secret-id: your-secret-id        # 腾讯云访问密钥ID
    secret-key: your-secret-key      # 腾讯云访问密钥Secret
    region: ap-beijing               # 短信服务区域
    app-id: your-app-id              # 短信应用ID
    sign-name: your-sign-name        # 短信签名
    template-id: your-template-id    # 短信模板ID
    endpoint: sms.tencentcloudapi.com # 请求端点
```

### 3. 短信模板设置

短信模板需要在腾讯云控制台中创建，模板示例：

```
【{1}】您的验证码是{2}，请于{3}分钟内输入。如非本人操作，请忽略本短信。
```

其中：
- `{1}` 为签名
- `{2}` 为验证码
- `{3}` 为有效期（分钟）

### 4. 验证码发送

系统会自动调用腾讯云短信服务发送验证码。验证码有效期为5分钟。

## 接口使用

### 发送短信验证码接口

**请求URL：** `/base/sendSMSCode`

**请求方式：** POST

**请求参数：**
```json
{
    "phone": "13800138000"
}
```

**返回示例：**
```json
{
    "code": 0,
    "data": null,
    "msg": "验证码发送成功"
}
```

## 支持的地区

腾讯云短信服务支持以下地区：
- ap-beijing（北京）
- ap-shanghai（上海）
- ap-guangzhou（广州）
- ap-chengdu（成都）
- ap-nanjing（南京）
- ap-singapore（新加坡）
- ap-mumbai（孟买）
- ap-hongkong（香港）
- ap-tokyo（东京）
- ap-seoul（首尔）
- ap-bangkok（曼谷）
- ap-jakarta（雅加达）
- na-toronto（多伦多）
- sa-saopaulo（圣保罗）
- na-siliconvalley（硅谷）
- na-ashburn（弗吉尼亚）
- eu-frankfurt（法兰克福）

## 注意事项

1. 请确保腾讯云账户余额充足
2. 短信签名和模板需要经过腾讯云审核
3. 验证码有效期为5分钟
4. 相同手机号码60秒内只能发送一次验证码
5. 请妥善保管腾讯云访问密钥，避免泄露

## 错误处理

系统会自动处理以下错误：
- 网络连接错误
- 腾讯云API错误
- 参数格式错误
- 签名或模板未审核通过

错误信息会记录在日志中，便于排查问题。

## 扩展支持

如需支持其他短信服务商，可以：
1. 实现 `SMSService` 接口
2. 在 `GetSMSService()` 函数中根据配置选择不同的实现
3. 添加相应的配置结构体

示例：

```go
type AliyunSMS struct{}

func (a *AliyunSMS) SendSMS(phoneNumber, code string) error {
    // 阿里云短信发送实现
    return nil
}
``` 
# 环境配置指南

本项目支持多环境配置，可以轻松区分本地环境、开发环境和生产环境。

## 配置文件说明

- `config.yaml` - 默认配置文件（本地环境）
- `config.dev.yaml` - 开发环境配置
- `config.prod.yaml` - 生产环境配置

## 启动方式

### 方式一：使用启动脚本（推荐）

```bash
# 启动主服务
./start.sh main dev      # 开发环境
./start.sh main prod     # 生产环境
./start.sh main          # 本地环境（默认）

# 启动代理服务
./start.sh proxy dev     # 开发环境
./start.sh proxy prod    # 生产环境
./start.sh proxy         # 本地环境（默认）
```

### 方式二：直接使用 go run

```bash
# 启动主服务
go run main.go --env=dev      # 开发环境
go run main.go --env=prod     # 生产环境
go run main.go                # 本地环境（默认）

# 启动代理服务
go run main.go proxy --env=dev      # 开发环境
go run main.go proxy --env=prod     # 生产环境
go run main.go proxy                # 本地环境（默认）
```

### 方式三：编译后运行

```bash
# 编译
go build -o server main.go

# 启动主服务
./server --env=dev      # 开发环境
./server --env=prod     # 生产环境
./server                # 本地环境（默认）

# 启动代理服务
./server proxy --env=dev      # 开发环境
./server proxy --env=prod     # 生产环境
./server proxy                # 本地环境（默认）
```

## 环境配置差异

### 本地环境 (config.yaml) - 默认
- 主服务端口: 8889
- 代理服务端口: 8026
- 数据库: localhost:3306/rapido_local
- Redis: localhost:6379/0

### 开发环境 (config.dev.yaml)
- 主服务端口: 8890
- 代理服务端口: 8027
- 数据库: localhost:3306/rapido_dev
- Redis: localhost:6379/1

### 生产环境 (config.prod.yaml)
- 主服务端口: 8888
- 代理服务端口: 8025
- 数据库: 阿里云RDS/rapido_prod
- Redis: 阿里云Redis/7

## 配置文件优先级

1. 命令行参数 `--env` 指定的环境配置
2. 环境变量 `GVA_CONFIG` 指定的配置文件
3. Gin 模式对应的配置文件：
   - `gin.DebugMode` -> `config.yaml`
   - `gin.ReleaseMode` -> `config.release.yaml`
   - `gin.TestMode` -> `config.test.yaml`

## 注意事项

1. 确保对应环境的配置文件存在
2. 本地和开发环境需要确保 MySQL 和 Redis 服务已启动
3. 生产环境需要确保网络连接和权限配置正确
4. 不同环境使用不同的数据库和端口，避免冲突和数据混乱
5. 开发环境和本地环境都使用本地数据库，但使用不同的数据库名和Redis库

package server

import (
	"context"
	"fmt"
	"net"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/grpc/interceptor"
	"github.com/flipped-aurora/gin-vue-admin/server/grpc/services"
)

// GrpcServer gRPC服务器结构
type GrpcServer struct {
	server   *grpc.Server
	listener net.Listener
	addr     string
}

// NewGrpcServer 创建新的gRPC服务器
func NewGrpcServer(addr string) *GrpcServer {
	return &GrpcServer{
		addr: addr,
	}
}

// Start 启动gRPC服务器
func (s *GrpcServer) Start() error {
	// 创建监听器
	listener, err := net.Listen("tcp", s.addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %v", s.addr, err)
	}
	s.listener = listener

	// 配置gRPC服务器选项
	opts := []grpc.ServerOption{
		// 配置keepalive
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle:     30 * time.Second,
			MaxConnectionAge:      30 * time.Minute,
			MaxConnectionAgeGrace: 5 * time.Second,
			Time:                  10 * time.Second,
			Timeout:               3 * time.Second,
		}),
		grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
			MinTime:             30 * time.Second,
			PermitWithoutStream: true,
		}),
		// 添加拦截器
		grpc.ChainUnaryInterceptor(
			interceptor.LoggingInterceptor(),
			interceptor.RecoveryInterceptor(),
			interceptor.AuthInterceptor(),
		),
		grpc.ChainStreamInterceptor(
			interceptor.StreamLoggingInterceptor(),
			interceptor.StreamRecoveryInterceptor(),
		),
		// 默认使用不安全连接，生产环境请配置TLS
		grpc.Creds(insecure.NewCredentials()),
	}

	// 创建gRPC服务器
	s.server = grpc.NewServer(opts...)

	// 注册服务
	s.registerServices()

	// 如果启用了反射，注册反射服务
	if global.GVA_CONFIG.System.GrpcReflection {
		reflection.Register(s.server)
		global.GVA_LOG.Info("gRPC reflection enabled")
	}

	global.GVA_LOG.Info("gRPC server starting", zap.String("address", s.addr))

	// 启动服务器
	return s.server.Serve(listener)
}

// Stop 停止gRPC服务器
func (s *GrpcServer) Stop() {
	if s.server != nil {
		global.GVA_LOG.Info("Stopping gRPC server...")
		s.server.GracefulStop()
	}
}

// Shutdown 强制关闭gRPC服务器
func (s *GrpcServer) Shutdown(ctx context.Context) error {
	if s.server != nil {
		global.GVA_LOG.Info("Shutting down gRPC server...")
		s.server.Stop()
	}
	return nil
}

// registerServices 注册所有gRPC服务
func (s *GrpcServer) registerServices() {
	// 注册用户服务
	services.RegisterUserService(s.server)

	// 注册设备服务
	services.RegisterDeviceService(s.server)

	global.GVA_LOG.Info("gRPC services registered")
}

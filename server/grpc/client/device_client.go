package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	devicepb "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/device"
)

// TestDeviceClient 测试设备gRPC客户端
func TestDeviceClient() {
	// 连接到gRPC服务器
	conn, err := grpc.Dial("localhost:10000", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("连接gRPC服务器失败: %v", err)
	}
	defer conn.Close()

	// 创建设备服务客户端
	client := devicepb.NewDeviceServiceClient(conn)

	// 测试获取设备信息
	deviceID := "9b55171a-843b-5649-b1ba-d372ea57fbbd" // 使用数据库中实际存在的设备ID

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	resp, err := client.GetDeviceInfo(ctx, &devicepb.GetDeviceByIDRequest{
		DeviceId: deviceID,
	})

	if err != nil {
		log.Printf("调用GetDeviceInfo失败: %v", err)
		return
	}

	// 打印结果
	fmt.Printf("=== 设备信息查询结果 ===\n")
	fmt.Printf("响应码: %d\n", resp.Base.Code)
	fmt.Printf("响应消息: %s\n", resp.Base.Message)
	fmt.Printf("是否成功: %t\n", resp.Base.Success)

	if resp.Base.Success && resp.Device != nil {
		device := resp.Device
		fmt.Printf("\n=== 设备详细信息 ===\n")
		fmt.Printf("设备ID: %s\n", device.DeviceId)
		fmt.Printf("设备名称: %s\n", device.DeviceName)
		fmt.Printf("主机名: %s\n", device.Hostname)
		fmt.Printf("操作系统: %s %s\n", device.OsName, device.OsVersion)
		fmt.Printf("系统架构: %s\n", device.OsArch)
		fmt.Printf("应用版本: %s\n", device.AppVersion)
		fmt.Printf("IP地址: %s\n", device.IpAddress)
		fmt.Printf("MAC地址: %s\n", device.MacAddress)
		fmt.Printf("关联用户ID: %d\n", device.UserId)
		fmt.Printf("状态: %d\n", device.Status)
		fmt.Printf("是否活跃: %t\n", device.IsActive)
		fmt.Printf("上报次数: %d\n", device.ReportCount)
		fmt.Printf("目录: %s\n", device.UserHomeDir)
		fmt.Printf("MCP访问地址: %s\n", device.GetMcpAccessAddress())
		fmt.Printf("智能体ID: %s\n", device.GetAgentId())

		// 在线状态信息
		if resp.OnlineStatus != nil {
			status := resp.OnlineStatus
			fmt.Printf("\n=== 设备在线状态 ===\n")
			fmt.Printf("是否在线: %t\n", status.IsOnline)
			if status.IsOnline {
				fmt.Printf("会话类型: %s\n", status.SessionType)
				fmt.Printf("登录IP: %s\n", status.IpAddress)
				fmt.Printf("设备名称: %s\n", status.DeviceName)
				fmt.Printf("操作系统: %s\n", status.OsInfo)
				fmt.Printf("应用版本: %s\n", status.AppVersion)
				if status.LastActiveAt != nil {
					lastActive := time.Unix(status.LastActiveAt.Seconds, int64(status.LastActiveAt.Nanos))
					fmt.Printf("最后活跃时间: %s\n", lastActive.Format("2006-01-02 15:04:05"))
				}
			}
		}
	}

	fmt.Printf("\n=== 测试完成 ===\n")
}

// TestGetMyDevices 测试获取我的设备列表
func TestGetMyDevices() {
	// 连接到gRPC服务器
	conn, err := grpc.Dial("localhost:10000", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("连接gRPC服务器失败: %v", err)
	}
	defer conn.Close()

	// 创建设备服务客户端
	client := devicepb.NewDeviceServiceClient(conn)

	// 测试获取我的设备列表
	userID := uint32(36) // 替换为实际的用户ID

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := client.GetMyDevices(ctx, &devicepb.GetMyDevicesRequest{
		UserId: userID,
	})

	if err != nil {
		log.Printf("调用GetMyDevices失败: %v", err)
		return
	}

	// 打印结果
	fmt.Printf("=== 我的设备列表查询结果 ===\n")
	fmt.Printf("响应码: %d\n", resp.Base.Code)
	fmt.Printf("响应消息: %s\n", resp.Base.Message)
	fmt.Printf("是否成功: %t\n", resp.Base.Success)

	if resp.Base.Success {
		fmt.Printf("总数量: %d\n", resp.Total)
		fmt.Printf("设备数量: %d\n", len(resp.Devices))

		fmt.Printf("\n=== 设备列表 ===\n")
		for i, device := range resp.Devices {
			fmt.Printf("--- 设备 %d ---\n", i+1)
			fmt.Printf("设备ID: %s\n", device.DeviceId)
			fmt.Printf("设备名称: %s\n", device.DeviceName)
			fmt.Printf("主机名: %s\n", device.Hostname)
			fmt.Printf("操作系统: %s %s\n", device.OsName, device.OsVersion)
			fmt.Printf("应用版本: %s\n", device.AppVersion)
			fmt.Printf("IP地址: %s\n", device.IpAddress)
			fmt.Printf("是否活跃: %t\n", device.IsActive)
			fmt.Printf("上报次数: %d\n", device.ReportCount)
			fmt.Printf("MCP访问地址: %s\n", device.GetMcpAccessAddress())
			fmt.Printf("智能体ID: %s\n", device.GetAgentId())

			if device.LastReportAt != nil {
				lastReport := time.Unix(device.LastReportAt.Seconds, int64(device.LastReportAt.Nanos))
				fmt.Printf("最后上报时间: %s\n", lastReport.Format("2006-01-02 15:04:05"))
			}
			fmt.Printf("\n")
		}
	}

	fmt.Printf("=== 我的设备列表测试完成 ===\n")
}

// main 函数用于独立运行测试
func main() {
	// 测试获取设备信息
	TestDeviceClient()

	fmt.Printf("%s", "\n"+strings.Repeat("=", 50)+"\n\n")

	// 测试获取我的设备列表
	TestGetMyDevices()
}

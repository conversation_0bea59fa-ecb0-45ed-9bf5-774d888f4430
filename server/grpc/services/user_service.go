package services

import (
	"context"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	commonPb "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/common"
	userPb "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/user"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
)

// UserServiceImpl 用户服务实现
type UserServiceImpl struct {
	userPb.UnimplementedUserServiceServer
	userService system.UserService
}

// NewUserService 创建用户服务实例
func NewUserService() *UserServiceImpl {
	return &UserServiceImpl{
		userService: service.ServiceGroupApp.SystemServiceGroup.UserService,
	}
}

// RegisterUserService 注册用户服务
func RegisterUserService(server *grpc.Server) {
	userPb.RegisterUserServiceServer(server, NewUserService())
}

// GetUser 获取用户信息
func (s *UserServiceImpl) GetUser(ctx context.Context, req *userPb.GetUserRequest) (*userPb.GetUserResponse, error) {
	// 获取当前用户ID（从认证拦截器传递过来的）
	userID, ok := ctx.Value("user_id").(uint)
	if !ok {
		return nil, status.Errorf(codes.Unauthenticated, "User not authenticated")
	}

	// 检查是否是查询自己的信息或者有权限查询其他用户信息
	if req.UserId != uint32(userID) {
		// 这里可以添加权限检查逻辑
		// 目前简化为只能查询自己的信息
		return nil, status.Errorf(codes.PermissionDenied, "Permission denied")
	}

	// 调用服务层获取用户信息
	user, err := s.userService.FindUserById(int(req.UserId))
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "User not found: %v", err)
	}

	// 转换为protobuf格式
	userPbData := &userPb.User{
		Id:          uint32(user.ID),
		Username:    user.Username,
		NickName:    user.NickName,
		Email:       user.Email,
		Phone:       user.Phone,
		Avatar:      user.HeaderImg,
		AuthorityId: uint32(user.AuthorityId),
		CreatedAt: &commonPb.Timestamp{
			Seconds: time.Time(user.CreatedAt).Unix(),
			Nanos:   int32(time.Time(user.CreatedAt).Nanosecond()),
		},
		UpdatedAt: &commonPb.Timestamp{
			Seconds: time.Time(user.UpdatedAt).Unix(),
			Nanos:   int32(time.Time(user.UpdatedAt).Nanosecond()),
		},
	}

	return &userPb.GetUserResponse{
		Base: &commonPb.BaseResponse{
			Code:    200,
			Message: "Success",
			Success: true,
		},
		User: userPbData,
	}, nil
}

// GetUserPoints 获取用户积分
func (s *UserServiceImpl) GetUserPoints(ctx context.Context, req *userPb.GetUserPointsRequest) (*userPb.GetUserPointsResponse, error) {
	// 获取当前用户ID（从认证拦截器传递过来的）
	userID, ok := ctx.Value("user_id").(uint)
	if !ok {
		return nil, status.Errorf(codes.Unauthenticated, "User not authenticated")
	}

	// 检查是否是查询自己的积分
	if req.UserId != uint32(userID) {
		return nil, status.Errorf(codes.PermissionDenied, "Permission denied: can only query own points")
	}

	// 调用服务层获取用户信息
	user, err := s.userService.FindUserById(int(req.UserId))
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "User not found: %v", err)
	}

	// 计算总积分
	totalPoints := user.Points + user.FreePoints

	return &userPb.GetUserPointsResponse{
		Base: &commonPb.BaseResponse{
			Code:    200,
			Message: "Success",
			Success: true,
		},
		Points:      int32(user.Points),     // 付费积分
		FreePoints:  int32(user.FreePoints), // 免费积分
		TotalPoints: int32(totalPoints),     // 总积分
		VipLevel:    int32(user.VipLevel),   // VIP等级
	}, nil
}

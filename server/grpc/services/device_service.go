package services

import (
	"context"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
	deviceReq "github.com/flipped-aurora/gin-vue-admin/server/model/device/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	commonpb "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/common"
	devicepb "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/device"
	deviceService "github.com/flipped-aurora/gin-vue-admin/server/service/device"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
)

// DeviceGRPCService 设备gRPC服务
type DeviceGRPCService struct {
	devicepb.UnimplementedDeviceServiceServer
}

// NewDeviceGRPCService 创建设备gRPC服务实例
func NewDeviceGRPCService() *DeviceGRPCService {
	return &DeviceGRPCService{}
}

// RegisterDeviceService 注册设备服务
func RegisterDeviceService(server *grpc.Server) {
	devicepb.RegisterDeviceServiceServer(server, NewDeviceGRPCService())
}

// GetDeviceInfo 根据设备ID获取设备信息和在线状态（不需要认证）
func (s *DeviceGRPCService) GetDeviceInfo(ctx context.Context, req *devicepb.GetDeviceByIDRequest) (*devicepb.GetDeviceInfoResponse, error) {
	// 输入参数验证
	if req.DeviceId == "" {
		return &devicepb.GetDeviceInfoResponse{
			Base: &commonpb.BaseResponse{
				Code:    400,
				Message: "设备ID不能为空",
				Success: false,
			},
		}, nil
	}

	global.GVA_LOG.Info("收到设备信息查询请求",
		zap.String("device_id", req.DeviceId))

	// 查询设备信息
	var deviceRecord device.Device
	err := global.GVA_DB.Where("device_id = ?", req.DeviceId).First(&deviceRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &devicepb.GetDeviceInfoResponse{
				Base: &commonpb.BaseResponse{
					Code:    404,
					Message: "设备不存在",
					Success: false,
				},
			}, nil
		}
		global.GVA_LOG.Error("查询设备信息失败", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "查询设备信息失败: %v", err)
	}

	// 添加调试日志
	global.GVA_LOG.Info("查询到设备信息",
		zap.String("device_id", deviceRecord.DeviceID),
		zap.String("mcp_access_address", deviceRecord.MCPAccessAddress))

	// 转换设备信息
	devicePB := convertDeviceToPB(&deviceRecord)

	// 查询设备在线状态
	onlineStatus := s.getDeviceOnlineStatus(req.DeviceId)

	return &devicepb.GetDeviceInfoResponse{
		Base: &commonpb.BaseResponse{
			Code:    200,
			Message: "查询成功",
			Success: true,
		},
		Device:       devicePB,
		OnlineStatus: onlineStatus,
	}, nil
}

// getDeviceOnlineStatus 获取设备在线状态
func (s *DeviceGRPCService) getDeviceOnlineStatus(deviceID string) *devicepb.DeviceOnlineStatus {
	// 查询设备的活跃会话
	var session system.SysUserSession
	err := global.GVA_DB.Where("device_id = ? AND is_active = ? AND buffer_at > ?",
		deviceID, true, time.Now()).
		Order("last_active_at DESC").First(&session).Error

	onlineStatus := &devicepb.DeviceOnlineStatus{
		IsOnline: false,
	}

	if err == nil {
		// 设备在线
		onlineStatus.IsOnline = true
		onlineStatus.LastActiveAt = convertTimeToTimestamp(session.LastActiveAt)
		onlineStatus.SessionType = session.SessionType
		onlineStatus.IpAddress = session.IPAddress
		onlineStatus.UserAgent = session.UserAgent
		onlineStatus.DeviceName = session.DeviceName
		onlineStatus.OsInfo = session.OSInfo
		onlineStatus.AppVersion = session.AppVersion
	} else if err != gorm.ErrRecordNotFound {
		// 查询出错，记录日志但不影响结果
		global.GVA_LOG.Error("查询设备在线状态失败",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	return onlineStatus
}

// convertDeviceToPB 将设备模型转换为protobuf消息
func convertDeviceToPB(device *device.Device) *devicepb.Device {
	userID := uint32(0)
	if device.UserID != nil {
		userID = uint32(*device.UserID)
	}

	return &devicepb.Device{
		Id:               uint32(device.ID),
		DeviceId:         device.DeviceID,
		DeviceName:       device.DeviceName,
		HardwareHash:     device.HardwareHash,
		CpuInfo:          device.CPUInfo,
		MemoryInfo:       device.MemoryInfo,
		DiskInfo:         device.DiskInfo,
		NetworkInfo:      device.NetworkInfo,
		GpuInfo:          device.GPUInfo,
		OsName:           device.OSName,
		OsVersion:        device.OSVersion,
		OsArch:           device.OSArch,
		Hostname:         device.Hostname,
		Username:         device.Username,
		UserHomeDir:      device.UserHomeDir,
		WorkDir:          device.WorkDir,
		AppVersion:       device.AppVersion,
		AppBuildNo:       device.AppBuildNo,
		IpAddress:        device.IPAddress,
		MacAddress:       device.MACAddress,
		UserId:           userID,
		FirstSeenAt:      convertTimeToTimestamp(device.FirstSeenAt),
		LastSeenAt:       convertTimeToTimestamp(device.LastSeenAt),
		LastReportAt:     convertTimeToTimestamp(device.LastReportAt),
		CreatedAt:        convertTimeToTimestamp(time.Time(device.CreatedAt)),
		UpdatedAt:        convertTimeToTimestamp(time.Time(device.UpdatedAt)),
		Status:           int32(device.Status),
		IsActive:         device.IsActive,
		ReportCount:      device.ReportCount,
		Remark:           device.Remark,
		IsDefault:        device.IsDefault,
		McpAccessAddress: device.MCPAccessAddress,
		AgentId:          device.AgentId,
	}
}

// convertTimeToTimestamp 将time.Time转换为protobuf Timestamp
func convertTimeToTimestamp(t time.Time) *commonpb.Timestamp {
	// 处理零值时间，避免负数时间戳
	if t.IsZero() {
		return &commonpb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		}
	}
	return &commonpb.Timestamp{
		Seconds: t.Unix(),
		Nanos:   int32(t.Nanosecond()),
	}
}

// GetMyDevices 获取用户的设备列表
func (s *DeviceGRPCService) GetMyDevices(ctx context.Context, req *devicepb.GetMyDevicesRequest) (*devicepb.GetMyDevicesResponse, error) {
	// 输入参数验证
	if req.UserId == 0 {
		return &devicepb.GetMyDevicesResponse{
			Base: &commonpb.BaseResponse{
				Code:    400,
				Message: "用户ID不能为空",
				Success: false,
			},
		}, nil
	}

	global.GVA_LOG.Info("收到获取用户设备列表请求",
		zap.Uint32("user_id", req.UserId))

	// 构造设备搜索请求 - 不使用分页，获取所有设备
	userID := uint(req.UserId)
	searchReq := &deviceReq.DeviceSearchRequest{
		PageInfo: request.PageInfo{
			Page:     1,
			PageSize: 1000, // 设置一个较大的值来获取所有设备
		},
	}
	searchReq.UserID = &userID

	// 调用设备服务获取设备列表
	deviceSvc := &deviceService.DeviceService{}
	result, err := deviceSvc.GetDeviceList(searchReq)
	if err != nil {
		global.GVA_LOG.Error("获取用户设备列表失败",
			zap.Uint32("user_id", req.UserId),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "获取设备列表失败: %v", err)
	}

	// 转换设备列表为protobuf格式
	var devicesPB []*devicepb.Device
	for _, dev := range result.List {
		devicesPB = append(devicesPB, convertDeviceToPB(&dev))
	}

	return &devicepb.GetMyDevicesResponse{
		Base: &commonpb.BaseResponse{
			Code:    200,
			Message: "获取成功",
			Success: true,
		},
		Devices: devicesPB,
		Total:   result.Total,
	}, nil
}

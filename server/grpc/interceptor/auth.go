package interceptor

import (
	"context"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

// AuthInterceptor 认证拦截器
func AuthInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 跳过不需要认证的方法
		if isAuthSkipMethod(info.FullMethod) {
			return handler(ctx, req)
		}

		// 从metadata中获取token，支持项目标准的 x-token 头部
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Errorf(codes.Unauthenticated, "Missing metadata")
		}

		// 首先尝试从 x-token 头部获取（项目标准方式）
		token := md.Get("x-token")[0]

		if token == "" {
			return nil, status.Errorf(codes.Unauthenticated, "Empty token")
		}

		// 检查token是否在黑名单中
		jwtService := service.ServiceGroupApp.SystemServiceGroup.JwtService
		if jwtService.IsBlacklist(token) {
			return nil, status.Errorf(codes.Unauthenticated, "Token is blacklisted")
		}

		// 验证会话是否有效（集成会话管理系统）
		userSessionService := service.ServiceGroupApp.SystemServiceGroup.UserSessionService
		_, err := userSessionService.ValidateSession(token)
		if err != nil {
			// 会话无效，将token加入黑名单
			return nil, status.Errorf(codes.Unauthenticated, "Session invalid: %v", err)
		}

		// 验证JWT token
		j := utils.NewJWT()
		claims, err := j.ParseToken(token)
		if err != nil {
			return nil, status.Errorf(codes.Unauthenticated, "Invalid token: %v", err)
		}

		// 更新会话活跃时间
		_ = userSessionService.UpdateSessionActivity(token)

		// 将用户信息添加到context中
		ctx = context.WithValue(ctx, "user_id", claims.BaseClaims.ID)
		ctx = context.WithValue(ctx, "username", claims.BaseClaims.Username)
		ctx = context.WithValue(ctx, "nickname", claims.BaseClaims.NickName)
		ctx = context.WithValue(ctx, "authority_id", claims.BaseClaims.AuthorityId)
		ctx = context.WithValue(ctx, "uuid", claims.BaseClaims.UUID.String())

		return handler(ctx, req)
	}
}

// isAuthSkipMethod 检查是否需要跳过认证的方法
func isAuthSkipMethod(method string) bool {
	skipMethods := []string{
		"/grpc.reflection.v1alpha.ServerReflection/ServerReflectionInfo", // gRPC反射接口
		"/device.DeviceService/GetDeviceInfo",                            // 设备信息查询接口不需要认证
		"/device.DeviceService/GetMyDevices",                             // 我的设备接口不需要认证
	}

	for _, skipMethod := range skipMethods {
		if method == skipMethod {
			return true
		}
	}
	return false
}

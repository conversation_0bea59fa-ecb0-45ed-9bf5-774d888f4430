package product

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"time"

	"gorm.io/gorm"
)

// UserAddress 用户地址表
type UserAddress struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	UserID    uint   `gorm:"not null;index:idx_user_address" json:"userId"`         // 用户ID
	Name      string `gorm:"type:varchar(50);not null" json:"name"`                 // 收货人姓名
	Phone     string `gorm:"type:varchar(20);not null" json:"phone"`                // 收货人电话
	Province  string `gorm:"type:varchar(50);not null" json:"province"`             // 省份
	City      string `gorm:"type:varchar(50);not null" json:"city"`                 // 城市
	District  string `gorm:"type:varchar(50);not null" json:"district"`             // 区县
	Address   string `gorm:"type:varchar(200);not null" json:"address"`             // 详细地址
	Zipcode   string `gorm:"type:varchar(10)" json:"zipcode"`                       // 邮政编码
	IsDefault bool   `gorm:"default:false;index:idx_user_default" json:"isDefault"` // 是否默认地址
	Label     string `gorm:"type:varchar(20)" json:"label"`                         // 地址标签（家、公司等）
	Status    int    `gorm:"default:1" json:"status"`                               // 状态 1-正常 2-禁用

	// 关联用户
	User system.SysUser `gorm:"foreignKey:UserID" json:"-"`
}

// TableName 指定表名
func (UserAddress) TableName() string {
	return "user_addresses"
}

// GetFullAddress 获取完整地址
func (u *UserAddress) GetFullAddress() string {
	return u.Province + u.City + u.District + u.Address
}

// AddressStatus 地址状态枚举
type AddressStatus int

const (
	AddressStatusNormal   AddressStatus = 1 // 正常
	AddressStatusDisabled AddressStatus = 2 // 禁用
)

package product

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// UserMembership 用户会员关系表
type UserMembership struct {
	global.GVA_MODEL
	UserID         uint           `json:"userId" gorm:"index;not null;comment:用户ID"`
	MembershipType MembershipType `json:"membershipType" gorm:"not null;comment:会员类型"`
	ProductID      uint           `json:"productId" gorm:"index;comment:商品ID"`
	OrderNo        string         `json:"orderNo" gorm:"index;comment:订单号"`

	// 时间相关
	StartTime time.Time `json:"startTime" gorm:"not null;comment:开始时间"`
	EndTime   time.Time `json:"endTime" gorm:"not null;comment:结束时间"`
	Duration  int       `json:"duration" gorm:"comment:时长(天)"`

	// 积分相关
	MonthlyPoints int        `json:"monthlyPoints" gorm:"comment:每月赠送积分"`
	TotalPoints   int        `json:"totalPoints" gorm:"comment:总共获得积分"`
	LastPointTime *time.Time `json:"lastPointTime" gorm:"comment:最后一次积分发放时间"`

	// 状态
	Status    MembershipStatus `json:"status" gorm:"default:1;comment:会员状态"`
	AutoRenew bool             `json:"autoRenew" gorm:"default:false;comment:是否自动续费"`

	// 扩展字段
	Features string `json:"features" gorm:"type:text;comment:会员特权(JSON数组)"`
	Extra    string `json:"extra" gorm:"type:text;comment:扩展信息(JSON)"`
}

// MembershipStatus 会员状态
type MembershipStatus int

const (
	MembershipStatusActive    MembershipStatus = 1 // 激活
	MembershipStatusExpired   MembershipStatus = 2 // 已过期
	MembershipStatusSuspended MembershipStatus = 3 // 暂停
	MembershipStatusCancelled MembershipStatus = 4 // 已取消
)

func (UserMembership) TableName() string {
	return "user_memberships"
}

// IsActive 检查会员是否有效
func (um *UserMembership) IsActive() bool {
	now := time.Now()
	return um.Status == MembershipStatusActive && um.StartTime.Before(now) && um.EndTime.After(now)
}

// GetVipLevel 根据会员类型返回VIP等级
func (um *UserMembership) GetVipLevel() int {
	if !um.IsActive() {
		return 0
	}

	switch um.MembershipType {
	case MembershipTypePro:
		return 1
	case MembershipTypeUltra:
		return 2
	default:
		return 0
	}
}

// RemainingDays 计算剩余天数
func (um *UserMembership) RemainingDays() int {
	if !um.IsActive() {
		return 0
	}

	now := time.Now()
	if um.EndTime.Before(now) {
		return 0
	}

	duration := um.EndTime.Sub(now)
	return int(duration.Hours() / 24)
}

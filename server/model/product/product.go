package product

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Product 商品表
type Product struct {
	global.GVA_MODEL
	Name          string        `json:"name" gorm:"not null;comment:商品名称"`
	Description   string        `json:"description" gorm:"type:text;comment:商品描述"`
	Type          ProductType   `json:"type" gorm:"not null;comment:商品类型"`
	Category      string        `json:"category" gorm:"comment:商品分类"`
	Price         int64         `json:"price" gorm:"not null;comment:商品价格(分)"`
	OriginalPrice int64         `json:"originalPrice" gorm:"comment:原价(分)"`
	Stock         int           `json:"stock" gorm:"default:-1;comment:库存(-1表示无限)"`
	Status        ProductStatus `json:"status" gorm:"default:1;comment:商品状态"`
	SortOrder     int           `json:"sortOrder" gorm:"default:0;comment:排序"`
	CoverImage    string        `json:"coverImage" gorm:"comment:封面图片"`
	Images        string        `json:"images" gorm:"type:text;comment:商品图片(JSON数组)"`
	Tags          string        `json:"tags" gorm:"comment:商品标签"`

	// 会员商品专用字段
	MembershipType MembershipType `json:"membershipType" gorm:"comment:会员类型"`
	Duration       int            `json:"duration" gorm:"comment:会员时长(天)"`
	DurationUnit   DurationUnit   `json:"durationUnit" gorm:"comment:时长单位"`
	MonthlyPoints  int            `json:"monthlyPoints" gorm:"comment:每月赠送积分"`
	Features       string         `json:"features" gorm:"type:text;comment:会员特权(JSON数组)"`

	// 实物商品专用字段
	Weight      float64 `json:"weight" gorm:"comment:商品重量(kg)"`
	Dimensions  string  `json:"dimensions" gorm:"comment:商品尺寸"`
	ShippingFee int64   `json:"shippingFee" gorm:"comment:运费(分)"`

	// 统计字段
	SalesCount int `json:"salesCount" gorm:"default:0;comment:销量"`
	ViewCount  int `json:"viewCount" gorm:"default:0;comment:浏览量"`

	// 时间字段
	LaunchTime  *time.Time `json:"launchTime" gorm:"comment:上架时间"`
	OfflineTime *time.Time `json:"offlineTime" gorm:"comment:下架时间"`
}

// ProductType 商品类型
type ProductType string

const (
	ProductTypeMembership ProductType = "membership" // 会员商品
	ProductTypePhysical   ProductType = "physical"   // 实物商品
)

// ProductStatus 商品状态
type ProductStatus int

const (
	ProductStatusDraft   ProductStatus = 0 // 草稿
	ProductStatusOnline  ProductStatus = 1 // 上架
	ProductStatusOffline ProductStatus = 2 // 下架
	ProductStatusDeleted ProductStatus = 3 // 已删除
)

// MembershipType 会员类型
type MembershipType string

const (
	MembershipTypePro   MembershipType = "pro"   // Pro会员
	MembershipTypeUltra MembershipType = "ultra" //
)

// DurationUnit 时长单位
type DurationUnit string

const (
	DurationUnitMonth   DurationUnit = "month"   // 月
	DurationUnitQuarter DurationUnit = "quarter" // 季度
	DurationUnitYear    DurationUnit = "year"    // 年
)

func (Product) TableName() string {
	return "products"
}

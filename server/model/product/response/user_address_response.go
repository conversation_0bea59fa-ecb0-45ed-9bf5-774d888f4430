package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/product"
	"time"
)

// UserAddressResponse 地址响应结构
type UserAddressResponse struct {
	ID          uint      `json:"id"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
	UserID      uint      `json:"userId"`
	Name        string    `json:"name"`
	Phone       string    `json:"phone"`
	Province    string    `json:"province"`
	City        string    `json:"city"`
	District    string    `json:"district"`
	Address     string    `json:"address"`
	Zipcode     string    `json:"zipcode"`
	IsDefault   bool      `json:"isDefault"`
	Label       string    `json:"label"`
	Status      int       `json:"status"`
	FullAddress string    `json:"fullAddress"` // 完整地址
}

// UserAddressListResponse 地址列表响应
type UserAddressListResponse struct {
	List     []UserAddressResponse `json:"list"`
	Total    int64                 `json:"total"`
	Page     int                   `json:"page"`
	PageSize int                   `json:"pageSize"`
}

// ConvertToUserAddressResponse 转换为地址响应结构
func ConvertToUserAddressResponse(address product.UserAddress) UserAddressResponse {
	return UserAddressResponse{
		ID:          address.ID,
		CreatedAt:   address.CreatedAt,
		UpdatedAt:   address.UpdatedAt,
		UserID:      address.UserID,
		Name:        address.Name,
		Phone:       address.Phone,
		Province:    address.Province,
		City:        address.City,
		District:    address.District,
		Address:     address.Address,
		Zipcode:     address.Zipcode,
		IsDefault:   address.IsDefault,
		Label:       address.Label,
		Status:      address.Status,
		FullAddress: address.GetFullAddress(),
	}
}

// ConvertToUserAddressResponseList 批量转换地址响应结构
func ConvertToUserAddressResponseList(addresses []product.UserAddress) []UserAddressResponse {
	var result []UserAddressResponse
	for _, address := range addresses {
		result = append(result, ConvertToUserAddressResponse(address))
	}
	return result
}

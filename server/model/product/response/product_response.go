package response

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/product"
)

// ProductResponse 商品响应
type ProductResponse struct {
	product.Product
	ImagesArray   []string `json:"imagesArray"`
	TagsArray     []string `json:"tagsArray"`
	FeaturesArray []string `json:"featuresArray"`
	IsInStock     bool     `json:"isInStock"`
	DiscountRate  float64  `json:"discountRate"`
}

// ProductListResponse 商品列表响应
type ProductListResponse struct {
	List  []ProductResponse `json:"list"`
	Total int64             `json:"total"`
}

// BuyProductResponse 购买商品响应
type BuyProductResponse struct {
	OrderNo    string `json:"orderNo"`
	PaymentURL string `json:"paymentUrl"`
	ExpireTime string `json:"expireTime"`
	Amount     int64  `json:"amount"`
}

// UserMembershipResponse 用户会员响应
type UserMembershipResponse struct {
	product.UserMembership
	FeaturesArray []string `json:"featuresArray"`
	IsActive      bool     `json:"isActive"`
	RemainingDays int      `json:"remainingDays"`
	VipLevel      int      `json:"vipLevel"`
	VipExpireAt   string   `json:"vipExpireAt"`
}

// UserMembershipListResponse 用户会员列表响应
type UserMembershipListResponse struct {
	List  []UserMembershipResponse `json:"list"`
	Total int64                    `json:"total"`
}

// UserMembershipStatusResponse 用户会员状态响应
type UserMembershipStatusResponse struct {
	HasActiveMembership bool                    `json:"hasActiveMembership"`
	CurrentMembership   *UserMembershipResponse `json:"currentMembership"`
	VipLevel            int                     `json:"vipLevel"`
	TotalPoints         int                     `json:"totalPoints"`
	MonthlyPoints       int                     `json:"monthlyPoints"`
	NextPointTime       *time.Time              `json:"nextPointTime"`
}

package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// CreateUserAddressRequest 创建地址请求
type CreateUserAddressRequest struct {
	Name      string `json:"name" binding:"required" example:"张三"`           // 收货人姓名
	Phone     string `json:"phone" binding:"required" example:"13800138000"` // 收货人电话
	Province  string `json:"province" binding:"required" example:"广东省"`      // 省份
	City      string `json:"city" binding:"required" example:"深圳市"`          // 城市
	District  string `json:"district" binding:"required" example:"南山区"`      // 区县
	Address   string `json:"address" binding:"required" example:"科技园南区"`     // 详细地址
	Zipcode   string `json:"zipcode" example:"518057"`                       // 邮政编码
	Label     string `json:"label" example:"家"`                              // 地址标签
	IsDefault bool   `json:"isDefault"`                                      // 是否设置为默认地址
}

// UpdateUserAddressRequest 更新地址请求
type UpdateUserAddressRequest struct {
	ID uint `json:"id" binding:"required"` // 地址ID
	CreateUserAddressRequest
}

// UserAddressSearch 地址搜索请求
type UserAddressSearch struct {
	request.PageInfo
	UserID    *uint   `json:"userId" form:"userId"`       // 用户ID（管理员使用）
	Label     *string `json:"label" form:"label"`         // 地址标签
	Province  *string `json:"province" form:"province"`   // 省份
	City      *string `json:"city" form:"city"`           // 城市
	IsDefault *bool   `json:"isDefault" form:"isDefault"` // 是否默认地址
	Status    *int    `json:"status" form:"status"`       // 状态
}

// SetDefaultAddressRequest 设置默认地址请求
type SetDefaultAddressRequest struct {
	ID uint `json:"id" binding:"required"` // 地址ID
}

package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/product"
)

// ProductSearch 商品搜索请求
type ProductSearch struct {
	request.PageInfo
	Name           *string                 `json:"name" form:"name"`
	Type           *product.ProductType    `json:"type" form:"type"`
	Category       *string                 `json:"category" form:"category"`
	Status         *product.ProductStatus  `json:"status" form:"status"`
	MembershipType *product.MembershipType `json:"membershipType" form:"membershipType"`
	MinPrice       *int64                  `json:"minPrice" form:"minPrice"`
	MaxPrice       *int64                  `json:"maxPrice" form:"maxPrice"`
	Tags           *string                 `json:"tags" form:"tags"`
}

// CreateProductRequest 创建商品请求
type CreateProductRequest struct {
	Name          string              `json:"name" binding:"required"`
	Description   string              `json:"description"`
	Type          product.ProductType `json:"type" binding:"required"`
	Category      string              `json:"category"`
	Price         int64               `json:"price" binding:"required,min=1"`
	OriginalPrice int64               `json:"originalPrice"`
	Stock         int                 `json:"stock"`
	CoverImage    string              `json:"coverImage"`
	Images        []string            `json:"images"`
	Tags          []string            `json:"tags"`

	// 会员商品相关
	MembershipType product.MembershipType `json:"membershipType"`
	Duration       int                    `json:"duration"`
	DurationUnit   product.DurationUnit   `json:"durationUnit"`
	MonthlyPoints  int                    `json:"monthlyPoints"`
	Features       []string               `json:"features"`

	// 实物商品相关
	Weight      float64 `json:"weight"`
	Dimensions  string  `json:"dimensions"`
	ShippingFee int64   `json:"shippingFee"`
}

// UpdateProductRequest 更新商品请求
type UpdateProductRequest struct {
	ID uint `json:"id" binding:"required"`
	CreateProductRequest
}

// BuyProductRequest 购买商品请求
type BuyProductRequest struct {
	ProductID     uint   `json:"productId" binding:"required"`
	Quantity      int    `json:"quantity" binding:"omitempty,min=1"`
	PaymentMethod string `json:"paymentMethod" binding:"required"`
	DeviceType    string `json:"deviceType" binding:"required"` // 设备类型

	// 支付相关
	OpenID   string `json:"openId"`   // 微信用户openid（当paymentMethod=wechat且deviceType=wechat时必需，用于JSAPI支付）
	ClientIP string `json:"clientIp"` // 客户端IP（由后端自动设置）

	// 地址相关 - 两种方式二选一
	AddressID       *uint                   `json:"addressId"`       // 使用已保存的地址ID
	ShippingAddress *ShippingAddressRequest `json:"shippingAddress"` // 或直接传入地址信息

	AutoRenew     bool `json:"autoRenew"`
	ExpireMinutes int  `json:"expireMinutes" binding:"omitempty,min=1,max=1440"`
}

// ShippingAddressRequest 收货地址请求
type ShippingAddressRequest struct {
	Name     string `json:"name" binding:"required"`
	Phone    string `json:"phone" binding:"required"`
	Province string `json:"province" binding:"required"`
	City     string `json:"city" binding:"required"`
	District string `json:"district" binding:"required"`
	Address  string `json:"address" binding:"required"`
	Zipcode  string `json:"zipcode"`
}

// UserMembershipSearch 用户会员搜索请求
type UserMembershipSearch struct {
	request.PageInfo
	UserID         *uint                     `json:"userId" form:"userId"`
	MembershipType *product.MembershipType   `json:"membershipType" form:"membershipType"`
	Status         *product.MembershipStatus `json:"status" form:"status"`
}

package product

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// OrderItem 订单商品关系表
type OrderItem struct {
	global.GVA_MODEL
	OrderNo     string      `json:"orderNo" gorm:"index;not null;comment:支付订单号"`
	ProductID   uint        `json:"productId" gorm:"index;not null;comment:商品ID"`
	ProductName string      `json:"productName" gorm:"not null;comment:商品名称"`
	ProductType ProductType `json:"productType" gorm:"not null;comment:商品类型"`

	// 价格信息
	UnitPrice  int64 `json:"unitPrice" gorm:"not null;comment:单价(分)"`
	Quantity   int   `json:"quantity" gorm:"default:1;comment:数量"`
	TotalPrice int64 `json:"totalPrice" gorm:"not null;comment:总价(分)"`

	// 会员商品相关
	MembershipType MembershipType `json:"membershipType" gorm:"comment:会员类型"`
	Duration       int            `json:"duration" gorm:"comment:会员时长(天)"`
	DurationUnit   DurationUnit   `json:"durationUnit" gorm:"comment:时长单位"`
	MonthlyPoints  int            `json:"monthlyPoints" gorm:"comment:每月赠送积分"`

	// 实物商品相关
	ShippingFee     int64          `json:"shippingFee" gorm:"comment:运费(分)"`
	ShippingAddress string         `json:"shippingAddress" gorm:"type:text;comment:收货地址"`
	TrackingNumber  string         `json:"trackingNumber" gorm:"comment:快递单号"`
	ShippingStatus  ShippingStatus `json:"shippingStatus" gorm:"default:1;comment:物流状态"`

	// 状态和时间
	Status      OrderItemStatus `json:"status" gorm:"default:1;comment:订单项状态"`
	DeliveredAt *time.Time      `json:"deliveredAt" gorm:"comment:交付时间"`
}

// OrderItemStatus 订单项状态
type OrderItemStatus int

const (
	OrderItemStatusPending   OrderItemStatus = 1 // 待处理
	OrderItemStatusProcessed OrderItemStatus = 2 // 已处理
	OrderItemStatusDelivered OrderItemStatus = 3 // 已交付
	OrderItemStatusCancelled OrderItemStatus = 4 // 已取消
)

// ShippingStatus 物流状态
type ShippingStatus int

const (
	ShippingStatusPending   ShippingStatus = 1 // 待发货
	ShippingStatusShipped   ShippingStatus = 2 // 已发货
	ShippingStatusDelivered ShippingStatus = 3 // 已送达
	ShippingStatusReturned  ShippingStatus = 4 // 已退货
)

func (OrderItem) TableName() string {
	return "order_items"
}

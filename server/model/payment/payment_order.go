package payment

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/gorm"
)

// PaymentOrder 支付订单表
type PaymentOrder struct {
	global.GVA_MODEL
	OrderNo       string                 `json:"orderNo" gorm:"uniqueIndex;comment:订单号"`
	UserID        uint                   `json:"userId" gorm:"comment:用户ID"`
	Amount        int64                  `json:"amount" gorm:"comment:支付金额(分)"`
	Currency      string                 `json:"currency" gorm:"default:CNY;comment:货币类型"`
	Subject       string                 `json:"subject" gorm:"comment:订单标题"`
	Body          string                 `json:"body" gorm:"comment:订单描述"`
	PaymentMethod PaymentMethod          `json:"paymentMethod" gorm:"comment:支付方式"`
	Status        PaymentStatus          `json:"status" gorm:"default:1;comment:支付状态"`
	ThirdOrderNo  string                 `json:"thirdOrderNo" gorm:"comment:第三方订单号"`
	ThirdResponse string                 `json:"thirdResponse" gorm:"type:text;comment:第三方响应"`
	NotifyUrl     string                 `json:"notifyUrl" gorm:"comment:异步通知地址"`
	ReturnUrl     string                 `json:"returnUrl" gorm:"comment:同步返回地址"`
	PayTime       *time.Time             `json:"payTime" gorm:"comment:支付时间"`
	ExpireTime    *time.Time             `json:"expireTime" gorm:"comment:过期时间"`
	RefundAmount  int64                  `json:"refundAmount" gorm:"default:0;comment:已退款金额(分)"`
	ClientIP      string                 `json:"clientIp" gorm:"comment:客户端IP"`
	Extra         map[string]interface{} `json:"extra" gorm:"serializer:json;comment:扩展字段"`
}

// PaymentMethod 支付方式枚举
type PaymentMethod string

const (
	PaymentMethodWechat PaymentMethod = "wechat" // 微信支付
	PaymentMethodAlipay PaymentMethod = "alipay" // 支付宝支付
)

// PaymentStatus 支付状态枚举
type PaymentStatus int

const (
	PaymentStatusPending         PaymentStatus = 1 // 待支付
	PaymentStatusPaid            PaymentStatus = 2 // 已支付
	PaymentStatusFailed          PaymentStatus = 3 // 支付失败
	PaymentStatusCancelled       PaymentStatus = 4 // 已取消
	PaymentStatusRefunded        PaymentStatus = 5 // 已退款
	PaymentStatusPartialRefunded PaymentStatus = 6 // 部分退款
)

func (PaymentOrder) TableName() string {
	return "payment_orders"
}

// BeforeCreate 创建前的钩子
func (p *PaymentOrder) BeforeCreate(tx *gorm.DB) (err error) {
	if p.OrderNo == "" {
		p.OrderNo = generateOrderNo()
	}
	return
}

// generateOrderNo 生成订单号
func generateOrderNo() string {
	return "PAY" + time.Now().Format("20060102150405") + generateRandomString(6)
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

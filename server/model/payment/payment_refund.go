package payment

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// PaymentRefund 退款记录表
type PaymentRefund struct {
	global.GVA_MODEL
	RefundNo       string       `json:"refundNo" gorm:"uniqueIndex;comment:退款单号"`
	OrderNo        string       `json:"orderNo" gorm:"index;comment:原订单号"`
	PaymentOrderID uint         `json:"paymentOrderId" gorm:"comment:支付订单ID"`
	RefundAmount   int64        `json:"refundAmount" gorm:"comment:退款金额(分)"`
	RefundReason   string       `json:"refundReason" gorm:"comment:退款原因"`
	Status         RefundStatus `json:"status" gorm:"default:1;comment:退款状态"`
	ThirdRefundNo  string       `json:"thirdRefundNo" gorm:"comment:第三方退款单号"`
	ThirdResponse  string       `json:"thirdResponse" gorm:"type:text;comment:第三方响应"`
	RefundTime     *time.Time   `json:"refundTime" gorm:"comment:退款时间"`
	FinishTime     *time.Time   `json:"finishTime" gorm:"comment:退款完成时间"`
	Remark         string       `json:"remark" gorm:"comment:备注"`

	// 关联关系
	PaymentOrder PaymentOrder `json:"paymentOrder" gorm:"foreignKey:PaymentOrderID"`
}

// RefundStatus 退款状态枚举
type RefundStatus int

const (
	RefundStatusPending   RefundStatus = 1 // 退款中
	RefundStatusSuccess   RefundStatus = 2 // 退款成功
	RefundStatusFailed    RefundStatus = 3 // 退款失败
	RefundStatusCancelled RefundStatus = 4 // 已取消
)

func (PaymentRefund) TableName() string {
	return "payment_refunds"
}

// generateRefundNo 生成退款单号
func generateRefundNo() string {
	return "REF" + time.Now().Format("20060102150405") + generateRandomString(6)
}

package wecom

//响应参数

type WechatWorkAccessTokenResp struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	ErrCode     int    `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
}

// SendKfTextMsgReq 发送文本消息请求体
// 参考：https://developer.work.weixin.qq.com/document/25217
type SendKfTextMsgReq struct {
	Touser   string `json:"touser"`
	OpenKfid string `json:"open_kfid"`
	MsgType  string `json:"msgtype"`
	Text     struct {
		Content string `json:"content"`
	} `json:"text"`
}

// SendKfTextMsgResp 发送文本消息响应体
// 参考：https://developer.work.weixin.qq.com/document/25217
type SendKfTextMsgResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	MsgID   string `json:"msgid"`
}

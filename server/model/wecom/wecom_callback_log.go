package wecom

import (
	"gorm.io/gorm"
)

// WecomEventCallbackLog 企业微信事件回调日志表
// 可根据业务需求扩展字段
// gorm.Model包含ID、CreatedAt、UpdatedAt、DeletedAt
type WecomEventCallbackLog struct {
	gorm.Model
	EventType    string `json:"event_type" gorm:"column:event_type;type:varchar(64);index"`
	RawContent   string `json:"raw_content" gorm:"type:text"`
	HandleResult string `json:"handle_result" gorm:"type:text"`
	Remark       string `json:"remark" gorm:"type:varchar(255)"`
}

// TableName 表名
func (WecomEventCallbackLog) TableName() string {
	return "wecom_event_callback_logs"
}

package mcprouter

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// ServerLog is the model for the server_logs table
type ServerLog struct {
	ID                 int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	JSONRPCVersion     string    `json:"jsonrpc_version" gorm:"column:jsonrpc_version"`
	ProtocolVersion    string    `json:"protocol_version" gorm:"column:protocol_version"`
	ConnectionTime     time.Time `json:"connection_time" gorm:"column:connection_time;type:datetime"`
	ClientName         string    `json:"client_name" gorm:"column:client_name"`
	ClientVersion      string    `json:"client_version" gorm:"column:client_version"`
	RequestMethod      string    `json:"request_method" gorm:"column:request_method"`
	RequestParams      string    `json:"request_params" gorm:"column:request_params"`
	RequestID          string    `json:"request_id" gorm:"column:request_id"`
	RequestTime        time.Time `json:"request_time" gorm:"column:request_time;type:datetime"`
	RequestFrom        string    `json:"request_from" gorm:"column:request_from"`
	SessionID          string    `json:"session_id" gorm:"column:session_id"`
	ServerUUID         string    `json:"server_uuid" gorm:"column:server_uuid"`
	ServerKey          string    `json:"server_key" gorm:"column:server_key"`
	ToolName           string    `json:"tool_name" gorm:"column:tool_name"`
	ServerConfigName   string    `json:"server_config_name" gorm:"column:server_config_name"`
	ServerShareProcess bool      `json:"server_share_process" gorm:"column:server_share_process"`
	ServerType         string    `json:"server_type" gorm:"column:server_type"`
	ServerURL          string    `json:"server_url" gorm:"column:server_url"`
	ServerCommand      string    `json:"server_command" gorm:"column:server_command"`
	ServerCommandHash  string    `json:"server_command_hash" gorm:"column:server_command_hash"`
	ServerName         string    `json:"server_name" gorm:"column:server_name"`
	ServerVersion      string    `json:"server_version" gorm:"column:server_version"`
	ResponseTime       time.Time `json:"response_time" gorm:"column:response_time;type:datetime"`
	ResponseResult     string    `json:"response_result" gorm:"column:response_result"`
	ResponseError      string    `json:"response_error" gorm:"column:response_error"`
	CostTime           int64     `json:"cost_time" gorm:"column:cost_time"`
	UserID             string    `json:"user_id" gorm:"column:user_id"`
	ChatSessionID      string    `json:"chat_session_id" gorm:"column:chat_session_id"`
	Points             int       `json:"points" gorm:"column:points"`
	IP                 string    `json:"ip" gorm:"column:ip;type:varchar(45);comment:客户端IP地址"`
}

// TableName returns the table name for the server_log model
func (s *ServerLog) TableName() string {
	return "server_logs"
}

// CreateServerLog creates a new server log
func CreateServerLog(sl *ServerLog) error {
	return global.GVA_DB.Create(sl).Error
}

// GetServerLogs gets server logs with pagination
func GetServerLogs(serverID int64, page, limit int) ([]*ServerLog, error) {
	var logs []*ServerLog
	offset := (page - 1) * limit
	err := global.GVA_DB.
		Where("server_id = ?", serverID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs).Error
	return logs, err
}

package mcprouter

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type Token struct {
	ID        int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	Token     string    `json:"token" gorm:"uniqueIndex"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
	UserID    string    `json:"user_id"`
}

func (t *Token) TableName() string {
	return "tokens"
}

type TokenStatus string

const (
	TokenStatusActive   TokenStatus = "active"
	TokenStatusInactive TokenStatus = "inactive"
	TokenStatusExpired  TokenStatus = "expired"
)

func FindTokenByToken(token string) (*Token, error) {
	t := &Token{}
	err := global.GVA_DB.
		Where("token = ?", token).
		Where("status = ?", TokenStatusActive).
		First(t).Error
	return t, err
}

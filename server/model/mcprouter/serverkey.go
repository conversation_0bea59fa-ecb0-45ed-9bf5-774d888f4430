package mcprouter

import (
	"fmt"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Serverkey is the model for the server_keys table
type Serverkey struct {
	ID                int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	ServerKey         string    `json:"server_key" gorm:"uniqueIndex"`
	ServerUUID        string    `json:"server_uuid"`
	ServerName        string    `json:"server_name"`
	ServerCommand     string    `json:"server_command"`
	ServerParams      string    `json:"server_params"`
	KeyParamName      string    `json:"keyParamName" gorm:"column:key_param_name;type:varchar(100);default:'key'"`
	Status            string    `json:"status"`
	CreatedAt         time.Time `json:"created_at"`
	UserUUID          string    `json:"user_uuid"`
	EnvJson           string    `json:"env_json" gorm:"type:text;comment:存储多env键值对的JSON对象"`
	SseUrl            string    `json:"sse_url" gorm:"column:sse_url;type:varchar(255);comment:中转SSE URL"`
	StreamableHttpUrl string    `json:"streamable_http_url" gorm:"column:streamable_http_url;type:varchar(255);comment:支持HTTP JSON-RPC的第三方接口"`
}

// TableName returns the table name for the serverkey model
func (s *Serverkey) TableName() string {
	return "server_keys"
}

type ServerKeyStatus string

const (
	ServerKeyStatusCreated ServerKeyStatus = "created"
	ServerKeyStatusDeleted ServerKeyStatus = "deleted"
)

// FindServerkeyByServerKey finds a serverkey by its server key
func FindServerkeyByServerKey(serverKey string) (*Serverkey, error) {
	serverkey := &Serverkey{}

	dbConn := global.GVA_DB
	if dbConn == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	err := dbConn.
		Where("server_key = ?", serverKey).
		Where("status = ?", ServerKeyStatusCreated).
		Order("created_at DESC").
		First(&serverkey).Error

	if err != nil {
		return nil, err
	}

	return serverkey, nil
}

// GetServerKeyByKey 根据key获取服务器密钥
func GetServerKeyByKey(key string) (*Serverkey, error) {
	var serverKey Serverkey

	// 处理key参数，只取第一部分
	cleanKey := strings.Split(key, "/")[0]

	err := global.GVA_DB.Where("server_key = ? AND status = ?", cleanKey, "created").
		Order("created_at DESC").
		First(&serverKey).Error

	if err != nil {
		return nil, err
	}

	return &serverKey, nil
}

// FindServerkeyByServerUUID 根据服务器UUID查找serverkey
func FindServerkeyByServerUUID(uuid string) (*Serverkey, error) {
	var serverkey Serverkey
	err := global.GVA_DB.Where("server_uuid = ?", uuid).First(&serverkey).Error
	if err != nil {
		return nil, err
	}
	return &serverkey, nil
}

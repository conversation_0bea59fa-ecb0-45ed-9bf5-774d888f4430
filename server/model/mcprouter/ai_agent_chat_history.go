package mcprouter

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type AgentChatHistory struct {
	ID             int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	MacAddress     string    `json:"macAddress" gorm:"column:mac_address"`
	AgentID        string    `json:"agentId" gorm:"column:agent_id"`
	SessionID      string    `json:"sessionId" gorm:"column:session_id"`
	ChatType       int8      `json:"chatType" gorm:"column:chat_type"`
	Content        string    `json:"content" gorm:"column:content"`
	AudioID        string    `json:"audioId" gorm:"column:audio_id"`
	RequestParams  string    `json:"requestParams" gorm:"column:request_params"`
	ResponseResult string    `json:"responseResult" gorm:"column:response_result"`
	McpName        string    `json:"mcpName" gorm:"column:mcp_name"`
	ToolName       string    `json:"toolName" gorm:"column:tool_name"`
	IsError        *bool     `json:"isError" gorm:"column:is_error"`
	UserID         int64     `json:"userId" gorm:"column:user_id"`
	LlmTokens      *int      `json:"llmTokens" gorm:"column:llm_tokens"`
	CreatedAt      time.Time `json:"createdAt" gorm:"column:created_at"`
	UpdatedAt      time.Time `json:"updatedAt" gorm:"column:updated_at"`
	Points         int       `json:"points" gorm:"column:points"`
}

func (s *AgentChatHistory) TableName() string {
	return "ai_agent_chat_history"
}

func CreateAgentChatHistory(sl *AgentChatHistory) error {
	return global.GVA_DB.Create(sl).Error
}

func GetAgentChatHistory(serverID int64, page, limit int) ([]*AgentChatHistory, error) {
	var logs []*AgentChatHistory
	offset := (page - 1) * limit
	err := global.GVA_DB.
		Where("server_id = ?", serverID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs).Error
	return logs, err
}

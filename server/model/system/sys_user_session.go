package system

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// SysUserSession 用户登录会话表
type SysUserSession struct {
	global.GVA_MODEL
	UserID       uint      `json:"userId" gorm:"index;not null;comment:用户ID"`               // 用户ID
	DeviceID     *string   `json:"deviceId" gorm:"index;comment:设备ID,网站登录时为空"`              // 设备ID，网站登录时为空
	SessionType  string    `json:"sessionType" gorm:"not null;comment:会话类型 device|web"`     // 会话类型：device(设备登录) 或 web(网站登录)
	Token        string    `json:"token" gorm:"size:512;unique;not null;comment:JWT Token"` // JWT Token
	IPAddress    string    `json:"ipAddress" gorm:"comment:登录IP地址"`                         // 登录IP地址
	UserAgent    string    `json:"userAgent" gorm:"type:text;comment:用户代理"`                 // 用户代理
	LoginAt      time.Time `json:"loginAt" gorm:"not null;comment:登录时间"`                    // 登录时间
	LastActiveAt time.Time `json:"lastActiveAt" gorm:"not null;comment:最后活跃时间"`             // 最后活跃时间
	ExpiresAt    time.Time `json:"expiresAt" gorm:"not null;comment:过期时间"`                  // 过期时间
	BufferAt     time.Time `json:"bufferAt" gorm:"not null;comment:缓冲时间截止时间"`               // 缓冲时间截止时间
	IsActive     bool      `json:"isActive" gorm:"default:true;comment:是否活跃"`               // 是否活跃
	DeviceName   string    `json:"deviceName" gorm:"comment:设备名称"`                          // 设备名称（用于显示）
	OSInfo       string    `json:"osInfo" gorm:"comment:操作系统信息"`                            // 操作系统信息
	AppVersion   string    `json:"appVersion" gorm:"comment:应用版本"`                          // 应用版本
	RefreshCount int       `json:"refreshCount" gorm:"default:0;comment:Token刷新次数"`         // Token刷新次数
}

// TableName 设置表名
func (SysUserSession) TableName() string {
	return "sys_user_sessions"
}

// SessionType 会话类型常量
const (
	SessionTypeDevice = "device" // 设备登录
	SessionTypeWeb    = "web"    // 网站登录
)

// 设备登录限制配置
const (
	MaxDeviceLogins = 3 // 最大设备登录数量
	MaxWebLogins    = 1 // 最大网站登录数量
)

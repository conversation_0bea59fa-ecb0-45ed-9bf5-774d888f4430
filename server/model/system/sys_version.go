// 自动生成模板SysVersion
package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// 系统版本 结构体  SysVersion
type SysVersion struct {
	global.GVA_MODEL
	OsType        *string `json:"osType" form:"osType" gorm:"index;column:os_type;comment:操作系统标识;" binding:"required"`     //操作系统标识
	Version       *string `json:"version" form:"version" gorm:"column:version;comment:版本号;" binding:"required"`            //版本
	UpdateContent *string `json:"updateContent" form:"updateContent" gorm:"column:update_content;comment:更新内容;type:text;"` //更新内容
}

// TableName 系统版本 SysVersion自定义表名 sys_version
func (SysVersion) TableName() string {
	return "sys_version"
}

package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// UserGeneralSettings 用户通用设置 结构体
type UserGeneralSettings struct {
	global.GVA_MODEL
	UserID uint `json:"userId" gorm:"uniqueIndex;not null;comment:用户ID"` // 用户ID

	// 基础设置
	AutoLaunch     bool   `json:"autoLaunch" gorm:"default:false;comment:登录时自动运行"`      // 登录时自动运行
	ShowMenuIcon   bool   `json:"showMenuIcon" gorm:"default:true;comment:显示菜单栏图标"`     // 显示菜单栏图标
	LaunchShortcut string `json:"launchShortcut" gorm:"default:'⌘ 点两次';comment:启动快捷键"`  // 启动快捷键
	Language       string `json:"language" gorm:"default:'zh-CN';comment:界面语言"`         // 界面语言
	DisplayScreen  string `json:"displayScreen" gorm:"default:'鼠标所在屏幕';comment:窗口出现屏幕"` // 窗口出现屏幕,"鼠标所在屏幕"、"主屏幕"、"指定屏幕"
	ThemeMode      string `json:"themeMode" gorm:"default:'system';comment:外观模式"`       // 外观模式 "light"、"dark"、"system"
	WindowStyle    string `json:"windowStyle" gorm:"default:'full';comment:窗口样式"`       // 窗口样式 完整、简约、右边栏、左边栏四种样式,"full"、"simple"、"right-sidebar"、"left-sidebar"

	// 系统权限状态（只读）
	AccessibilityEnabled   bool `json:"accessibilityEnabled" gorm:"default:false;comment:辅助功能权限"`    // 辅助功能权限
	ScreenRecordingEnabled bool `json:"screenRecordingEnabled" gorm:"default:false;comment:屏幕录制权限"`  // 屏幕录制权限
	FullDiskAccessEnabled  bool `json:"fullDiskAccessEnabled" gorm:"default:false;comment:完全磁盘访问权限"` // 完全磁盘访问权限
}

// TableName 用户通用设置表名
func (UserGeneralSettings) TableName() string {
	return "user_general_settings"
}

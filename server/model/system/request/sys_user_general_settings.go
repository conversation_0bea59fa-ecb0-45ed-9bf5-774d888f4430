package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// UserGeneralSettingsSearch 用户通用设置搜索结构体
type UserGeneralSettingsSearch struct {
	UserID   uint   `json:"userId" form:"userId"`     // 用户ID
	Language string `json:"language" form:"language"` // 界面语言
	request.PageInfo
}

// UpdateUserGeneralSettingsReq 更新用户通用设置请求结构体
type UpdateUserGeneralSettingsReq struct {
	AutoLaunch     *bool   `json:"autoLaunch"`     // 登录时自动运行
	ShowMenuIcon   *bool   `json:"showMenuIcon"`   // 显示菜单栏图标
	LaunchShortcut *string `json:"launchShortcut"` // 启动快捷键
	Language       *string `json:"language"`       // 界面语言
	DisplayScreen  *string `json:"displayScreen"`  // 窗口出现屏幕
	ThemeMode      *string `json:"themeMode"`      // 外观模式
	WindowStyle    *string `json:"windowStyle"`    // 窗口样式
}

// UpdatePermissionsReq 更新权限状态请求结构体
type UpdatePermissionsReq struct {
	AccessibilityEnabled   *bool `json:"accessibilityEnabled"`   // 辅助功能权限
	ScreenRecordingEnabled *bool `json:"screenRecordingEnabled"` // 屏幕录制权限
	FullDiskAccessEnabled  *bool `json:"fullDiskAccessEnabled"`  // 完全磁盘访问权限
}

package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// AuthProviderBindRequest 绑定认证提供商请求
type AuthProviderBindRequest struct {
	Provider   string `json:"provider" binding:"required"`   // 认证提供商类型
	ProviderId string `json:"providerId" binding:"required"` // 提供商用户ID
	OpenId     string `json:"openId"`                        // 第三方开放ID
	UnionId    string `json:"unionId"`                       // 第三方联合ID
	Email      string `json:"email"`                         // 关联邮箱
	Phone      string `json:"phone"`                         // 关联手机号
	Username   string `json:"username"`                      // 用户名
	Code       string `json:"code"`                          // 验证码（用于验证邮箱/手机号）
	Metadata   string `json:"metadata"`                      // 额外元数据
}

// AuthProviderUnbindRequest 解绑认证提供商请求
type AuthProviderUnbindRequest struct {
	Provider   string `json:"provider" binding:"required"`   // 认证提供商类型
	ProviderId string `json:"providerId" binding:"required"` // 提供商用户ID
}

// UnifiedLoginRequest 统一登录请求
type UnifiedLoginRequest struct {
	Provider   string `json:"provider" binding:"required"` // 认证提供商类型
	ProviderId string `json:"providerId"`                  // 提供商用户ID
	Username   string `json:"username"`                    // 用户名
	Password   string `json:"password"`                    // 密码
	Email      string `json:"email"`                       // 邮箱
	Phone      string `json:"phone"`                       // 手机号
	Code       string `json:"code"`                        // 验证码/授权码
	OpenId     string `json:"openId"`                      // 第三方开放ID
	UnionId    string `json:"unionId"`                     // 第三方联合ID
	Captcha    string `json:"captcha"`                     // 图形验证码
	CaptchaId  string `json:"captchaId"`                   // 图形验证码ID
	Validate   string `json:"validate"`                    // 易盾验证参数
	Metadata   string `json:"metadata"`                    // 额外元数据
}

// AccountMergeRequest 账号合并请求
type AccountMergeRequest struct {
	SourceUserIds []uint `json:"sourceUserIds" binding:"required"` // 源用户ID列表（要合并的账号）
	TargetUserId  uint   `json:"targetUserId" binding:"required"`  // 目标用户ID（要保留的账号）
	Password      string `json:"password" binding:"required"`      // 确认密码
	ConfirmMerge  bool   `json:"confirmMerge" binding:"required"`  // 确认合并标志
}

// GetUserProvidersRequest 获取用户认证提供商列表请求
type GetUserProvidersRequest struct {
	request.PageInfo
	UserId   uint   `json:"userId" form:"userId"`     // 用户ID
	Provider string `json:"provider" form:"provider"` // 认证提供商类型
	Verified *bool  `json:"verified" form:"verified"` // 是否已验证
}

// VerifyProviderRequest 验证认证提供商请求
type VerifyProviderRequest struct {
	Provider   string `json:"provider" binding:"required"`   // 认证提供商类型
	ProviderId string `json:"providerId" binding:"required"` // 提供商用户ID
	Code       string `json:"code" binding:"required"`       // 验证码
}

// CheckProviderExistRequest 检查认证提供商是否存在请求
type CheckProviderExistRequest struct {
	Provider   string `json:"provider" binding:"required"`   // 认证提供商类型
	ProviderId string `json:"providerId" binding:"required"` // 提供商用户ID
	Email      string `json:"email"`                         // 邮箱（可选）
	Phone      string `json:"phone"`                         // 手机号（可选）
	Username   string `json:"username"`                      // 用户名（可选）
}

// AccountBindConfirmRequest 账号绑定确认请求
type AccountBindConfirmRequest struct {
	TempToken    string `json:"tempToken" binding:"required"` // 临时令牌
	Action       string `json:"action" binding:"required"`    // 操作类型：bind, create_new
	TargetUserId uint   `json:"targetUserId"`                 // 目标用户ID（绑定时需要）
	Password     string `json:"password"`                     // 密码（用于身份验证）
	VerifyCode   string `json:"verifyCode"`                   // 验证码（备选验证方式）
}

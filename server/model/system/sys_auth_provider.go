package system

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// SysAuthProvider 认证提供商模型 - 用于关联用户与不同的登录方式
type SysAuthProvider struct {
	global.GVA_MODEL
	UserId     uint       `json:"userId" gorm:"not null;index;comment:用户ID"`           // 关联的用户ID
	Provider   string     `json:"provider" gorm:"not null;size:50;comment:认证提供商类型"`    // 认证提供商类型：password, wechat, github, google, phone, email等
	ProviderId string     `json:"providerId" gorm:"not null;size:255;comment:提供商用户ID"` // 提供商的用户唯一标识
	OpenId     string     `json:"openId" gorm:"size:255;comment:第三方开放ID"`              // 第三方平台的OpenID
	UnionId    string     `json:"unionId" gorm:"size:255;comment:第三方联合ID"`             // 第三方平台的UnionID（微信等）
	Email      string     `json:"email" gorm:"size:255;comment:关联邮箱"`                  // 关联的邮箱
	Phone      string     `json:"phone" gorm:"size:20;comment:关联手机号"`                  // 关联的手机号
	Username   string     `json:"username" gorm:"size:100;comment:用户名"`                // 用户名（用于用户名密码登录）
	Verified   bool       `json:"verified" gorm:"default:false;comment:是否已验证"`         // 是否已验证（邮箱、手机号等）
	Metadata   string     `json:"metadata" gorm:"type:text;comment:额外元数据"`             // 存储额外的元数据（JSON格式）
	LastUsedAt *time.Time `json:"lastUsedAt" gorm:"comment:最后使用时间"`                    // 最后使用时间
	User       SysUser    `json:"user" gorm:"foreignKey:UserId;references:ID"`         // 关联用户
}

func (SysAuthProvider) TableName() string {
	return "sys_auth_providers"
}

// AuthProviderType 认证提供商类型常量
const (
	ProviderTypeUsername = "username" // 用户名密码
	ProviderTypeEmail    = "email"    // 邮箱
	ProviderTypePhone    = "phone"    // 手机号验证码
	ProviderTypeWechat   = "wechat"   // 微信
	ProviderTypeGithub   = "github"   // GitHub
	ProviderTypeGoogle   = "google"   // Google
	ProviderTypeQQ       = "qq"       // QQ
	ProviderTypeWeibo    = "weibo"    // 微博
	ProviderTypeAuthing  = "authing"  // Authing
)

// IsThirdParty 判断是否为第三方登录
func (p *SysAuthProvider) IsThirdParty() bool {
	thirdPartyProviders := []string{
		ProviderTypeWechat,
		ProviderTypeGithub,
		ProviderTypeGoogle,
		ProviderTypeQQ,
		ProviderTypeWeibo,
		ProviderTypeAuthing,
	}

	for _, provider := range thirdPartyProviders {
		if p.Provider == provider {
			return true
		}
	}
	return false
}

// NeedsVerification 判断是否需要验证
func (p *SysAuthProvider) NeedsVerification() bool {
	return (p.Provider == ProviderTypeEmail || p.Provider == ProviderTypePhone) && !p.Verified
}

package response

// JWT验证结果代码常量
const (
	// Token有效
	TokenValid = "TOKEN_VALID"

	// Token为空
	TokenEmpty = "TOKEN_EMPTY"

	// Token在黑名单中（被主动注销）
	TokenBlacklisted = "TOKEN_BLACKLISTED"

	// Token已过期
	TokenExpired = "TOKEN_EXPIRED"

	// Token尚未激活
	TokenNotValidYet = "TOKEN_NOT_VALID_YET"

	// Token格式错误
	TokenMalformed = "TOKEN_MALFORMED"

	// Token签名无效
	TokenSignatureInvalid = "TOKEN_SIGNATURE_INVALID"

	// Token无效（其他原因）
	TokenInvalid = "TOKEN_INVALID"

	// 会话已过期
	SessionExpired = "SESSION_EXPIRED"

	// 会话无效
	SessionInvalid = "SESSION_INVALID"

	// 用户被禁用
	UserDisabled = "USER_DISABLED"

	// 用户不存在
	UserNotFound = "USER_NOT_FOUND"

	// 设备登录超限
	DeviceLoginLimitExceeded = "DEVICE_LOGIN_LIMIT_EXCEEDED"

	// 网页登录超限
	WebLoginLimitExceeded = "WEB_LOGIN_LIMIT_EXCEEDED"
)

// JwtVerifyResponse JWT验证响应结构
type JwtVerifyResponse struct {
	Valid       bool        `json:"valid"`                 // 是否有效
	Code        string      `json:"code"`                  // 错误代码
	Message     string      `json:"message"`               // 错误消息
	Claims      interface{} `json:"claims,omitempty"`      // Token声明（仅在有效时返回）
	ExpiresAt   int64       `json:"expiresAt,omitempty"`   // 过期时间戳（仅在有效时返回）
	Session     interface{} `json:"session,omitempty"`     // 会话信息（仅在有效时返回）
	CanRefresh  bool        `json:"canRefresh"`            // 是否可以刷新Token
	NeedsReauth bool        `json:"needsReauth"`           // 是否需要重新认证
	BufferAt    int64       `json:"bufferAt,omitempty"`    // 缓冲期截止时间（仅在有效时返回）
	RefreshHint string      `json:"refreshHint,omitempty"` // 刷新提示信息
}

// GetCodeMessage 根据错误代码获取用户友好的错误消息
func GetCodeMessage(code string) string {
	messages := map[string]string{
		TokenValid:               "Token有效",
		TokenEmpty:               "Token为空",
		TokenBlacklisted:         "Token已被撤销或注销",
		TokenExpired:             "Token已过期，请重新登录",
		TokenNotValidYet:         "Token尚未激活",
		TokenMalformed:           "Token格式错误",
		TokenSignatureInvalid:    "Token签名无效",
		TokenInvalid:             "Token无效",
		SessionExpired:           "会话已过期，请重新登录",
		SessionInvalid:           "会话无效",
		UserDisabled:             "用户已被禁用",
		UserNotFound:             "用户不存在",
		DeviceLoginLimitExceeded: "设备登录数量超出限制",
		WebLoginLimitExceeded:    "网页登录数量超出限制",
	}

	if message, exists := messages[code]; exists {
		return message
	}
	return "未知错误"
}

// GetCodeDescription 根据错误代码获取详细描述（用于开发调试）
func GetCodeDescription(code string) string {
	descriptions := map[string]string{
		TokenValid:               "Token通过所有验证，可以正常使用",
		TokenEmpty:               "请求中未提供Token",
		TokenBlacklisted:         "Token已被主动撤销，可能是用户注销或管理员操作",
		TokenExpired:             "Token已超过有效期，需要重新获取",
		TokenNotValidYet:         "Token的生效时间尚未到达",
		TokenMalformed:           "Token格式不符合JWT标准",
		TokenSignatureInvalid:    "Token签名验证失败，可能被篡改",
		TokenInvalid:             "Token无法解析或验证失败",
		SessionExpired:           "用户会话已过期，超出了缓冲时间",
		SessionInvalid:           "用户会话状态异常或不存在",
		UserDisabled:             "用户账户已被管理员禁用",
		UserNotFound:             "Token中的用户信息在系统中不存在",
		DeviceLoginLimitExceeded: "用户的设备登录数量已达到上限",
		WebLoginLimitExceeded:    "用户的网页登录数量已达到上限",
	}

	if description, exists := descriptions[code]; exists {
		return description
	}
	return "未知的错误代码"
}

// IsRefreshable 判断错误是否可以通过刷新Token解决
func IsRefreshable(code string) bool {
	refreshableCodes := map[string]bool{
		TokenExpired: true, // Token过期但在缓冲期内，可以刷新
	}
	return refreshableCodes[code]
}

// IsRetryable 判断错误是否可以通过重试解决
func IsRetryable(code string) bool {
	retryableCodes := map[string]bool{
		TokenExpired:   true,  // 可以通过刷新Token解决
		SessionExpired: false, // 会话过期需要重新登录，不能简单重试
	}
	return retryableCodes[code]
}

// RequiresReauth 判断错误是否需要重新认证
func RequiresReauth(code string) bool {
	reauthCodes := map[string]bool{
		TokenBlacklisted:         true,
		TokenExpired:             true,
		TokenMalformed:           true,
		TokenSignatureInvalid:    true,
		TokenInvalid:             true,
		SessionExpired:           true,
		SessionInvalid:           true,
		UserDisabled:             true,
		UserNotFound:             true,
		DeviceLoginLimitExceeded: true,
		WebLoginLimitExceeded:    true,
	}
	return reauthCodes[code]
}

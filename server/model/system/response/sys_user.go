package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

type SysUserResponse struct {
	User system.SysUser `json:"user"`
}

type LoginResponse struct {
	User      system.SysUser `json:"user"`
	Token     string         `json:"token"`
	ExpiresAt int64          `json:"expiresAt"`
}

// UserPointsResponse 用户积分响应
type UserPointsResponse struct {
	Points      int `json:"points"`      // 付费积分
	FreePoints  int `json:"freePoints"`  // 免费积分
	TotalPoints int `json:"totalPoints"` // 总积分
	VipLevel    int `json:"vipLevel"`    // VIP等级
}

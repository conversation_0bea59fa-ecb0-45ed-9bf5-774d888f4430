package response

import "github.com/flipped-aurora/gin-vue-admin/server/model/system"

// UserGeneralSettingsResponse 用户通用设置响应结构体
type UserGeneralSettingsResponse struct {
	Settings    system.UserGeneralSettings `json:"settings"`    // 设置信息
	Permissions PermissionsInfo            `json:"permissions"` // 权限信息
}

// PermissionsInfo 权限信息结构体
type PermissionsInfo struct {
	AccessibilityEnabled   bool `json:"accessibilityEnabled"`   // 辅助功能权限
	ScreenRecordingEnabled bool `json:"screenRecordingEnabled"` // 屏幕录制权限
	FullDiskAccessEnabled  bool `json:"fullDiskAccessEnabled"`  // 完全磁盘访问权限
}

// OptionItem 选项项结构体
type OptionItem struct {
	Code string `json:"code"` // 选项代码
	Name string `json:"name"` // 选项名称
}

// GeneralSettingsOptionsResponse 通用设置选项响应结构体
type GeneralSettingsOptionsResponse struct {
	Languages      []OptionItem `json:"languages"`      // 语言选项
	ThemeModes     []OptionItem `json:"themeModes"`     // 主题模式选项
	WindowStyles   []OptionItem `json:"windowStyles"`   // 窗口样式选项
	DisplayScreens []OptionItem `json:"displayScreens"` // 显示屏幕选项
}

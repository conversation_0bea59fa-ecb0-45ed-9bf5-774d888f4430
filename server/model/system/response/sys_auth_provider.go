package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// AuthProviderResponse 认证提供商响应
type AuthProviderResponse struct {
	AuthProvider system.SysAuthProvider `json:"authProvider"`
}

// AuthProviderListResponse 认证提供商列表响应
type AuthProviderListResponse struct {
	List     []system.SysAuthProvider `json:"list"`
	Total    int64                    `json:"total"`
	Page     int                      `json:"page"`
	PageSize int                      `json:"pageSize"`
}

// UnifiedLoginResponse 统一登录响应
type UnifiedLoginResponse struct {
	User           system.SysUser  `json:"user"`
	Token          string          `json:"token"`
	ExpiresAt      int64           `json:"expiresAt"`
	Provider       string          `json:"provider"`       // 使用的认证提供商
	IsNewUser      bool            `json:"isNewUser"`      // 是否为新用户
	NeedsBind      bool            `json:"needsBind"`      // 是否需要绑定已有账号
	TempToken      string          `json:"tempToken"`      // 临时令牌（用于绑定流程）
	CandidateUsers []CandidateUser `json:"candidateUsers"` // 候选用户账号
	BindingOptions []BindingOption `json:"bindingOptions"` // 绑定选项
	TempData       *WechatTempData `json:"tempData"`       // 临时数据，用于绑定流程
}

// WechatTempData 微信临时数据
type WechatTempData struct {
	OpenID     string `json:"openid"`
	UnionID    string `json:"unionid"`
	Nickname   string `json:"nickname"`
	HeadImgURL string `json:"headimgurl"`
	State      string `json:"state"`
}

// CandidateUser 候选用户信息
type CandidateUser struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	NickName string `json:"nickName"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	Avatar   string `json:"avatar"`
	MatchBy  string `json:"matchBy"` // 匹配方式：email, phone, username
}

// BindingOption 绑定选项
type BindingOption struct {
	Type        string `json:"type"`        // auto, manual, create_new
	Title       string `json:"title"`       // 选项标题
	Description string `json:"description"` // 选项描述
	Recommended bool   `json:"recommended"` // 是否推荐
}

// AccountBindConfirmResponse 账号绑定确认响应
type AccountBindConfirmResponse struct {
	Success   bool           `json:"success"`
	User      system.SysUser `json:"user"`
	Token     string         `json:"token"`
	ExpiresAt int64          `json:"expiresAt"`
	Provider  string         `json:"provider"`
	Message   string         `json:"message"`
}

// ProviderExistResponse 检查认证提供商是否存在响应
type ProviderExistResponse struct {
	Exists bool            `json:"exists"`
	User   *system.SysUser `json:"user,omitempty"` // 如果存在，返回关联的用户信息
}

// AccountMergeResponse 账号合并响应
type AccountMergeResponse struct {
	Success      bool   `json:"success"`
	TargetUserId uint   `json:"targetUserId"` // 保留的账号ID
	MergedCount  int    `json:"mergedCount"`  // 合并的认证提供商数量
	Message      string `json:"message"`
}

// UserProvidersResponse 用户认证提供商列表响应
type UserProvidersResponse struct {
	Providers []UserProviderInfo `json:"providers"`
	Total     int64              `json:"total"`
}

// UserProviderInfo 用户认证提供商信息
type UserProviderInfo struct {
	Provider   string `json:"provider"`   // 认证提供商类型
	ProviderId string `json:"providerId"` // 提供商用户ID（脱敏后）
	Verified   bool   `json:"verified"`   // 是否已验证
	LastUsedAt string `json:"lastUsedAt"` // 最后使用时间
	IsDefault  bool   `json:"isDefault"`  // 是否为默认登录方式
	CanUnbind  bool   `json:"canUnbind"`  // 是否可以解绑
}

// BindProviderResponse 绑定认证提供商响应
type BindProviderResponse struct {
	Success  bool   `json:"success"`
	Provider string `json:"provider"`
	Message  string `json:"message"`
}

// UnbindProviderResponse 解绑认证提供商响应
type UnbindProviderResponse struct {
	Success  bool   `json:"success"`
	Provider string `json:"provider"`
	Message  string `json:"message"`
}

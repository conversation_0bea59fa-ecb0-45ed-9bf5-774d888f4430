package response

import "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"

// TokenVerifyResponse token验证响应
type TokenVerifyResponse struct {
	Valid     bool                  `json:"valid"`     // token是否有效
	Claims    *request.CustomClaims `json:"claims"`    // token中的声明信息
	ExpiresAt int64                 `json:"expiresAt"` // 过期时间戳
	Message   string                `json:"message"`   // 状态消息
}

// TokenRefreshResponse token刷新响应
type TokenRefreshResponse struct {
	Token     string `json:"token"`     // 新的token
	ExpiresAt int64  `json:"expiresAt"` // 新token的过期时间戳
	Message   string `json:"message"`   // 状态消息
}

package basetypes

type Language string

const (
	LangAuto Language = "auto"
	LangZh   Language = "zh"
	LangEn   Language = "en"
	LangYue  Language = "yue"
	LangWyw  Language = "wyw"
	LangJp   Language = "jp"
	LangKor  Language = "kor"
	LangFra  Language = "fra"
	LangSpa  Language = "spa"
	LangTh   Language = "th"
	LangAra  Language = "ara"
	LangRu   Language = "ru"
	LangPt   Language = "pt"
	LangDe   Language = "de"
	LangIt   Language = "it"
	LangEl   Language = "el"
	LangNl   Language = "nl"
	LangPl   Language = "pl"
	LangBul  Language = "bul"
	LangEst  Language = "est"
	LangDan  Language = "dan"
	LangFin  Language = "fin"
	LangCs   Language = "cs"
	LangRom  Language = "rom"
	LangSlo  Language = "slo"
	LangSwe  Language = "swe"
	LangHu   Language = "hu"
	LangCht  Language = "cht"
	LangVie  Language = "vie"
)

var LanguageNameMap = map[Language]string{
	LangAuto: "自动检测",
	LangZh:   "中文",
	LangEn:   "英语",
	LangYue:  "粤语",
	LangWyw:  "文言文",
	LangJp:   "日语",
	LangKor:  "韩语",
	LangFra:  "法语",
	LangSpa:  "西班牙语",
	LangTh:   "泰语",
	LangAra:  "阿拉伯语",
	LangRu:   "俄语",
	LangPt:   "葡萄牙语",
	LangDe:   "德语",
	LangIt:   "意大利语",
	LangEl:   "希腊语",
	LangNl:   "荷兰语",
	LangPl:   "波兰语",
	LangBul:  "保加利亚语",
	LangEst:  "爱沙尼亚语",
	LangDan:  "丹麦语",
	LangFin:  "芬兰语",
	LangCs:   "捷克语",
	LangRom:  "罗马尼亚语",
	LangSlo:  "斯洛文尼亚语",
	LangSwe:  "瑞典语",
	LangHu:   "匈牙利语",
	LangCht:  "繁体中文",
	LangVie:  "越南语",
}

var LanguageEnameMap = map[Language]string{
	LangAuto: "Auto Detect",
	LangZh:   "Chinese",
	LangEn:   "English",
	LangYue:  "Cantonese",
	LangWyw:  "Classical Chinese",
	LangJp:   "Japanese",
	LangKor:  "Korean",
	LangFra:  "French",
	LangSpa:  "Spanish",
	LangTh:   "Thai",
	LangAra:  "Arabic",
	LangRu:   "Russian",
	LangPt:   "Portuguese",
	LangDe:   "German",
	LangIt:   "Italian",
	LangEl:   "Greek",
	LangNl:   "Dutch",
	LangPl:   "Polish",
	LangBul:  "Bulgarian",
	LangEst:  "Estonian",
	LangDan:  "Danish",
	LangFin:  "Finnish",
	LangCs:   "Czech",
	LangRom:  "Romanian",
	LangSlo:  "Slovenian",
	LangSwe:  "Swedish",
	LangHu:   "Hungarian",
	LangCht:  "Traditional Chinese",
	LangVie:  "Vietnamese",
}

var LanguageCodeMap = map[string]Language{
	"auto": LangAuto,
	"zh":   LangZh,
	"en":   LangEn,
	"yue":  LangYue,
	"wyw":  LangWyw,
	"jp":   LangJp,
	"kor":  LangKor,
	"fra":  LangFra,
	"spa":  LangSpa,
	"th":   LangTh,
	"ara":  LangAra,
	"ru":   LangRu,
	"pt":   LangPt,
	"de":   LangDe,
	"it":   LangIt,
	"el":   LangEl,
	"nl":   LangNl,
	"pl":   LangPl,
	"bul":  LangBul,
	"est":  LangEst,
	"dan":  LangDan,
	"fin":  LangFin,
	"cs":   LangCs,
	"rom":  LangRom,
	"slo":  LangSlo,
	"swe":  LangSwe,
	"hu":   LangHu,
	"cht":  LangCht,
	"vie":  LangVie,
}

var AllLanguageCodes = []Language{
	LangAuto, LangZh, LangEn, LangYue, LangWyw, LangJp, LangKor, LangFra, LangSpa, LangTh, LangAra, LangRu, LangPt, LangDe, LangIt, LangEl, LangNl, LangPl, LangBul, LangEst, LangDan, LangFin, LangCs, LangRom, LangSlo, LangSwe, LangHu, LangCht, LangVie,
}

package response

import "github.com/flipped-aurora/gin-vue-admin/server/model/llm/request"

// LLMResponse 定义通用大语言模型响应参数
// 兼容 OpenAI Chat API 返回格式

type LLMResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   *Usage   `json:"usage,omitempty"`
}

type Choice struct {
	Index        int             `json:"index"`
	Message      request.Message `json:"message"`
	Delta        *Delta          `json:"delta,omitempty"`
	FinishReason string          `json:"finish_reason"`
}

type Delta struct {
	Role         string                `json:"role,omitempty"`
	Content      string                `json:"content,omitempty"`
	ToolCalls    []request.ToolCall    `json:"tool_calls,omitempty"`
	FunctionCall *request.FunctionCall `json:"function_call,omitempty"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

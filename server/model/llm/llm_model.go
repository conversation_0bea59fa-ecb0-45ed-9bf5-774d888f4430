package llm

import "github.com/flipped-aurora/gin-vue-admin/server/global"

// LLMModel 定义大语言模型的数据库结构体
// 用于存储模型名称、类型、API密钥、BaseURL等信息
// 可扩展支持多种厂商和模型

type LLMModel struct {
	global.GVA_MODEL
	Name             string `gorm:"unique;not null" json:"name"`          // 模型名称（如 gpt-3.5-turbo）
	Provider         string `json:"provider"`                             // 厂商（如 openai, azure, baidu 等）
	ApiKey           string `json:"api_key"`                              // API 密钥
	BaseURL          string `json:"base_url"`                             // API 基础地址
	IsEnabled        bool   `json:"is_enabled"`                           // 是否启用
	Remark           string `json:"remark"`                               // 备注
	CreditCost       int    `json:"credit_cost" gorm:"default:0"`         // 普通用户每次调用最低消耗积分
	TokenPerPoint    int    `json:"token_per_point" gorm:"default:0"`     // 普通用户每多少token消耗1积分
	VipCreditCost    int    `json:"vip_credit_cost" gorm:"default:0"`     // VIP用户每次调用最低消耗积分
	VipTokenPerPoint int    `json:"vip_token_per_point" gorm:"default:0"` // VIP用户每多少token消耗1积分
}

func (LLMModel) TableName() string {
	return "llm_models"
}

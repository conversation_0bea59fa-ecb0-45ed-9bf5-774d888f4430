package llm

import "github.com/flipped-aurora/gin-vue-admin/server/global"

// LLMUsageLog 用于记录每次大语言模型调用的消耗情况
// 包括模型、用户、输入输出、token 消耗等信息

type LLMUsageLog struct {
	global.GVA_MODEL
	ModelID          uint   `json:"model_id"`                      // 关联的 LLMModel ID
	UserID           uint   `json:"user_id"`                       // 调用用户 ID（如有用户体系）
	Prompt           string `json:"prompt" gorm:"type:longtext"`   // 用户输入内容（可选）
	Response         string `json:"response" gorm:"type:longtext"` // 模型返回内容（可选）
	PromptTokens     int    `json:"prompt_tokens"`                 // 输入 token 数
	CompletionTokens int    `json:"completion_tokens"`             // 输出 token 数
	TotalTokens      int    `json:"total_tokens"`                  // 总 token 数
}

func (LLMUsageLog) TableName() string {
	return "llm_usage_logs"
}

package request

// LLMRequest 定义通用大语言模型请求参数
// 支持多种模型调用，参数参考 OpenAI 官方规范
// https://platform.openai.com/docs/api-reference/chat/create

type LLMRequest struct {
	Model            string      `json:"model" binding:"required"`
	Messages         []Message   `json:"messages" binding:"required"`
	MaxTokens        int         `json:"max_tokens,omitempty"`
	Temperature      float32     `json:"temperature,omitempty"`
	TopP             float32     `json:"top_p,omitempty"`
	Stream           bool        `json:"stream,omitempty"`
	Stop             []string    `json:"stop,omitempty"`
	PresencePenalty  float32     `json:"presence_penalty,omitempty"`
	FrequencyPenalty float32     `json:"frequency_penalty,omitempty"`
	User             string      `json:"user,omitempty"`
	Tools            []ToolDef   `json:"tools,omitempty"`
	ToolChoice       interface{} `json:"tool_choice,omitempty"`
}

type Message struct {
	Role         string        `json:"role" binding:"required"`
	Content      string        `json:"content,omitempty"`
	Name         string        `json:"name,omitempty"`
	FunctionCall *FunctionCall `json:"function_call,omitempty"`
	ToolCalls    []ToolCall    `json:"tool_calls,omitempty"`
	ToolCallID   string        `json:"tool_call_id,omitempty"`
}

type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
	ID        string `json:"id,omitempty"`
}

type ToolDef struct {
	Type        string                 `json:"type"`
	Name        string                 `json:"name"`
	Description string                 `json:"description,omitempty"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
	Strict      bool                   `json:"strict,omitempty"`
}

type ToolCall struct {
	ID       string        `json:"id"`
	Type     string        `json:"type"`
	Function *FunctionCall `json:"function"`
	Index    *int          `json:"index,omitempty"`
}

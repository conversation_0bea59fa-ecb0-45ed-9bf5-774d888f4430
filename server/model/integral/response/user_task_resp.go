package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// UserTaskItem 任务项
type UserTaskItem struct {
	ID          uint            `json:"id"`          // 任务记录ID
	CompletedAt global.DateTime `json:"completedAt"` // 任务完成时间
	Points      int             `json:"points"`      // 获得体力值
	Title       string          `json:"title"`       // 备注
}

// UserTaskListResponse 任务列表响应
type UserTaskListResponse struct {
	List     []UserTaskItem `json:"list"`     // 任务列表
	Total    int64          `json:"total"`    // 总数
	Page     int            `json:"page"`     // 当前页
	PageSize int            `json:"pageSize"` // 每页大小
}

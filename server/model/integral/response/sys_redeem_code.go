package response

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
)

// RedeemCodeResponse 兑换码响应
type RedeemCodeResponse struct {
	ID            uint                `json:"id"`            // ID
	Code          string              `json:"code"`          // 兑换码
	Type          string              `json:"type"`          // 兑换类型
	Title         string              `json:"title"`         // 兑换码标题
	Description   string              `json:"description"`   // 描述
	RewardData    integral.RewardData `json:"rewardData"`    // 奖励数据
	MaxUses       int                 `json:"maxUses"`       // 最大使用次数
	UsedCount     int                 `json:"usedCount"`     // 已使用次数
	ExpiresAt     *time.Time          `json:"expiresAt"`     // 过期时间
	IsActive      bool                `json:"isActive"`      // 是否激活
	CreatedBy     uint                `json:"createdBy"`     // 创建者ID
	CreatedByName string              `json:"createdByName"` // 创建者名称
	BatchID       string              `json:"batchId"`       // 批次ID
	Remark        string              `json:"remark"`        // 备注
	Status        string              `json:"status"`        // 状态
	CreatedAt     global.DateTime     `json:"createdAt"`     // 创建时间
	UpdatedAt     global.DateTime     `json:"updatedAt"`     // 更新时间
}

// RedeemCodeListResponse 兑换码列表响应
type RedeemCodeListResponse struct {
	List     []RedeemCodeResponse `json:"list"`     // 列表
	Total    int64                `json:"total"`    // 总数
	Page     int                  `json:"page"`     // 页码
	PageSize int                  `json:"pageSize"` // 页大小
}

// BatchCreateRedeemCodeResponse 批量创建兑换码响应
type BatchCreateRedeemCodeResponse struct {
	BatchID     string              `json:"batchId"`     // 批次ID
	Count       int                 `json:"count"`       // 生成数量
	Codes       []string            `json:"codes"`       // 生成的兑换码列表
	Type        string              `json:"type"`        // 兑换类型
	Title       string              `json:"title"`       // 兑换码标题
	RewardData  integral.RewardData `json:"rewardData"`  // 奖励数据
	Description string              `json:"description"` // 描述
	ExpiresAt   *time.Time          `json:"expiresAt"`   // 过期时间
}

// UseRedeemCodeResponse 使用兑换码响应
type UseRedeemCodeResponse struct {
	Type        string              `json:"type"`        // 兑换类型
	Title       string              `json:"title"`       // 兑换码标题
	RewardData  integral.RewardData `json:"rewardData"`  // 获得的奖励数据
	Code        string              `json:"code"`        // 兑换码
	Description string              `json:"description"` // 描述
	Message     string              `json:"message"`     // 提示信息
}

// RedeemCodeUsageResponse 兑换码使用记录响应
type RedeemCodeUsageResponse struct {
	ID         uint                `json:"id"`         // ID
	Code       string              `json:"code"`       // 兑换码
	Type       string              `json:"type"`       // 兑换类型
	Title      string              `json:"title"`      // 兑换码标题
	UserID     uint                `json:"userId"`     // 用户ID
	Username   string              `json:"username"`   // 用户名
	RewardData integral.RewardData `json:"rewardData"` // 获得奖励数据
	UsedAt     global.DateTime     `json:"usedAt"`     // 使用时间
	IPAddress  string              `json:"ipAddress"`  // 使用者IP
}

// RedeemCodeUsageListResponse 兑换码使用记录列表响应
type RedeemCodeUsageListResponse struct {
	List     []RedeemCodeUsageResponse `json:"list"`     // 列表
	Total    int64                     `json:"total"`    // 总数
	Page     int                       `json:"page"`     // 页码
	PageSize int                       `json:"pageSize"` // 页大小
}

// RedeemCodeStatsResponse 兑换码统计响应
type RedeemCodeStatsResponse struct {
	TotalCodes   int64                   `json:"totalCodes"`   // 总兑换码数
	ActiveCodes  int64                   `json:"activeCodes"`  // 激活的兑换码数
	UsedCodes    int64                   `json:"usedCodes"`    // 已使用的兑换码数
	ExpiredCodes int64                   `json:"expiredCodes"` // 已过期的兑换码数
	TotalUsages  int64                   `json:"totalUsages"`  // 总使用次数
	TodayUsages  int64                   `json:"todayUsages"`  // 今日使用次数
	WeekUsages   int64                   `json:"weekUsages"`   // 本周使用次数
	MonthUsages  int64                   `json:"monthUsages"`  // 本月使用次数
	TypeStats    map[string]TypeStatItem `json:"typeStats"`    // 按类型统计
}

// TypeStatItem 类型统计项
type TypeStatItem struct {
	Type        string `json:"type"`        // 类型
	TotalCodes  int64  `json:"totalCodes"`  // 总数
	UsedCodes   int64  `json:"usedCodes"`   // 已使用数
	TotalUsages int64  `json:"totalUsages"` // 使用次数
}

// ConvertToResponse 将模型转换为响应格式
func ConvertToRedeemCodeResponse(code integral.SysRedeemCode, createdByName string) RedeemCodeResponse {
	rewardData, _ := code.GetRewardData()
	if rewardData == nil {
		rewardData = &integral.RewardData{}
	}

	return RedeemCodeResponse{
		ID:            code.ID,
		Code:          code.Code,
		Type:          code.Type,
		Title:         code.Title,
		Description:   code.Description,
		RewardData:    *rewardData,
		MaxUses:       code.MaxUses,
		UsedCount:     code.UsedCount,
		ExpiresAt:     code.ExpiresAt,
		IsActive:      code.IsActive,
		CreatedBy:     code.CreatedBy,
		CreatedByName: createdByName,
		BatchID:       code.BatchID,
		Remark:        code.Remark,
		Status:        code.GetStatus(),
		CreatedAt:     global.DateTime(code.CreatedAt),
		UpdatedAt:     global.DateTime(code.UpdatedAt),
	}
}

package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
)

// FeedbackResponse 反馈响应
type FeedbackResponse struct {
	integral.Feedback
}

// FeedbackListResponse 反馈列表响应
type FeedbackListResponse struct {
	List     []integral.Feedback `json:"list"`     // 反馈列表
	Total    int64               `json:"total"`    // 总数
	Page     int                 `json:"page"`     // 当前页
	PageSize int                 `json:"pageSize"` // 每页大小
}

// FeedbackStatsResponse 反馈统计响应
type FeedbackStatsResponse struct {
	TotalCount    int64 `json:"totalCount"`    // 总反馈数
	PendingCount  int64 `json:"pendingCount"`  // 待审核数
	ApprovedCount int64 `json:"approvedCount"` // 已通过数
	RejectedCount int64 `json:"rejectedCount"` // 已拒绝数
	StarredCount  int64 `json:"starredCount"`  // 标星数
	PublicCount   int64 `json:"publicCount"`   // 公开数
}

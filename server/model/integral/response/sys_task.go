package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
)

// TaskListItem 任务列表项
type TaskListItem struct {
	integral.SysTask
	TotalCompletedTimes int              `json:"totalCompletedTimes"` // 累计完成次数
	TotalRewardPoints   int              `json:"totalRewardPoints"`   // 累计获得积分
	LastCompletedAt     *global.DateTime `json:"lastCompletedAt"`     // 最后完成时间
}

// TaskListResponse 任务列表响应
type TaskListResponse struct {
	List []TaskListItem `json:"list"`
}

// CompleteTaskResponse 完成任务响应
type CompleteTaskResponse struct {
	TaskID       uint   `json:"taskId"`       // 任务ID
	TaskTitle    string `json:"taskTitle"`    // 任务标题
	RewardPoints int    `json:"rewardPoints"` // 获得的积分
	Message      string `json:"message"`      // 提示信息
}

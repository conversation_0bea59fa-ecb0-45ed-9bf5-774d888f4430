package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// UserPointsRecordResponse 积分记录响应
type UserPointsRecordResponse struct {
	ID     uint            `json:"id"`     // 记录ID
	Time   global.DateTime `json:"time"`   // 时间
	Reason string          `json:"reason"` // 原因
	Change int             `json:"change"` // 变动积分
}

// UserPointsRecordListResponse 积分记录列表响应
type UserPointsRecordListResponse struct {
	List  []UserPointsRecordResponse `json:"list"`
	Total int64                      `json:"total"`
}

package response

import "github.com/flipped-aurora/gin-vue-admin/server/model/system"

// CheckUpdateResponse 检查更新响应结构体
type CheckUpdateResponse struct {
	NeedUpdate     bool               `json:"needUpdate"`     // 是否需要更新
	LatestVersion  string             `json:"latestVersion"`  // 最新版本号
	CurrentVersion string             `json:"currentVersion"` // 当前版本号
	UpdateContent  string             `json:"updateContent"`  // 更新内容
	VersionInfo    *system.SysVersion `json:"versionInfo"`    // 版本详细信息
}

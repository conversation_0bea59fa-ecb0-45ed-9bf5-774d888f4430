package response

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
)

// DeviceReportResponse 设备上报响应
type DeviceReportResponse struct {
	DeviceID    string    `json:"deviceId"`    // 设备ID
	IsNewDevice bool      `json:"isNewDevice"` // 是否为新设备
	ReportTime  time.Time `json:"reportTime"`  // 上报时间
	Message     string    `json:"message"`     // 响应消息
}

// DeviceResponse 设备信息响应
type DeviceResponse struct {
	Device device.Device `json:"device"` // 设备信息
}

// DeviceListResponse 设备列表响应
type DeviceListResponse struct {
	List  []device.Device `json:"list"`  // 设备列表
	Total int64           `json:"total"` // 总数
}

// DeviceReportListResponse 设备上报记录列表响应
type DeviceReportListResponse struct {
	List  []device.DeviceReport `json:"list"`  // 上报记录列表
	Total int64                 `json:"total"` // 总数
}

// DeviceStatisticsResponse 设备统计响应
type DeviceStatisticsResponse struct {
	TotalDevices   int64                `json:"totalDevices"`   // 总设备数
	ActiveDevices  int64                `json:"activeDevices"`  // 活跃设备数
	NewDevices     int64                `json:"newDevices"`     // 新设备数（今日）
	OnlineDevices  int64                `json:"onlineDevices"`  // 在线设备数
	OSDistribution []OSDistributionItem `json:"osDistribution"` // 操作系统分布
	DailyReports   []DailyReportItem    `json:"dailyReports"`   // 每日上报统计
	DeviceTrends   []DeviceTrendItem    `json:"deviceTrends"`   // 设备趋势
}

// OSDistributionItem 操作系统分布项
type OSDistributionItem struct {
	OSName string `json:"osName"` // 操作系统名称
	Count  int64  `json:"count"`  // 数量
}

// DailyReportItem 每日上报统计项
type DailyReportItem struct {
	Date  string `json:"date"`  // 日期
	Count int64  `json:"count"` // 上报次数
}

// DeviceTrendItem 设备趋势项
type DeviceTrendItem struct {
	Date       string `json:"date"`       // 日期
	NewDevices int64  `json:"newDevices"` // 新设备数
	Total      int64  `json:"total"`      // 累计设备数
}

// DeviceDetailResponse 设备详情响应
type DeviceDetailResponse struct {
	Device        device.Device         `json:"device"`        // 设备信息
	ReportHistory []device.DeviceReport `json:"reportHistory"` // 上报历史
	UserInfo      *UserInfo             `json:"userInfo"`      // 关联用户信息（如果有）
}

// UserInfo 用户信息（简化版）
type UserInfo struct {
	ID       uint   `json:"id"`       // 用户ID
	Username string `json:"username"` // 用户名
	NickName string `json:"nickName"` // 昵称
}

// AssignDefaultAgentResponse 分配默认智能体响应
type AssignDefaultAgentResponse struct {
	MCPAccessAddress string `json:"mcpAccessAddress"` // MCP访问地址
	AgentId          string `json:"agentId"`          // 智能体ID
}

// IpLocationResponse IP归属地查询响应
type IpLocationResponse struct {
	IP       string `json:"ip"`       // IP地址
	Country  string `json:"country"`  // 国家
	Province string `json:"province"` // 省份
	City     string `json:"city"`     // 城市
	ISP      string `json:"isp"`      // 网络服务商
	OrderNo  string `json:"orderNo"`  // 订单号
}

// UpdateDeviceNameResponse 修改设备名称响应
type UpdateDeviceNameResponse struct {
	DeviceID   string `json:"deviceId"`   // 设备ID
	DeviceName string `json:"deviceName"` // 更新后的设备名称
	Message    string `json:"message"`    // 响应消息
}

// DeviceLogoutResponse 设备登出响应
type DeviceLogoutResponse struct {
	DeviceID string `json:"deviceId"` // 登出的设备ID
	Message  string `json:"message"`  // 响应消息
}

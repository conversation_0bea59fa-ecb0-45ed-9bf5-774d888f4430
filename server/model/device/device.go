package device

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Device 设备信息表
type Device struct {
	global.GVA_MODEL
	// 设备标识
	DeviceID     string `json:"deviceId" gorm:"uniqueIndex;not null;comment:设备唯一标识码"` // 设备唯一标识码
	DeviceName   string `json:"deviceName" gorm:"comment:设备自定义名称"`                    // 设备自定义名称
	HardwareHash string `json:"hardwareHash" gorm:"comment:硬件特征哈希值"`                  // 硬件特征哈希值

	// 硬件信息
	CPUInfo     string `json:"cpuInfo" gorm:"type:text;comment:CPU信息"`                // CPU信息
	MemoryInfo  string `json:"memoryInfo" gorm:"comment:内存信息"`                        // 内存信息
	DiskInfo    string `json:"diskInfo" gorm:"type:text;comment:磁盘信息"`                // 磁盘信息
	NetworkInfo string `json:"networkInfo" gorm:"type:text;comment:网络信息"`             // 网络信息
	GPUInfo     string `json:"gpuInfo" gorm:"column:gpu_info;type:text;comment:显卡信息"` // 显卡信息

	// 系统环境信息
	OSName      string `json:"osName" gorm:"comment:操作系统名称"`     // 操作系统名称
	OSVersion   string `json:"osVersion" gorm:"comment:操作系统版本"`  // 操作系统版本
	OSArch      string `json:"osArch" gorm:"comment:系统架构"`       // 系统架构
	Hostname    string `json:"hostname" gorm:"comment:主机名"`      // 主机名
	Username    string `json:"username" gorm:"comment:当前用户名"`    // 当前用户名
	UserHomeDir string `json:"userHomeDir" gorm:"comment:用户主目录"` // 用户主目录
	WorkDir     string `json:"workDir" gorm:"comment:工作目录"`      // 工作目录

	// 应用信息
	AppVersion string `json:"appVersion" gorm:"comment:应用版本"`  // 应用版本
	AppBuildNo string `json:"appBuildNo" gorm:"comment:应用构建号"` // 应用构建号

	// 网络信息
	IPAddress  string `json:"ipAddress" gorm:"comment:IP地址"`   // IP地址
	MACAddress string `json:"macAddress" gorm:"comment:MAC地址"` // MAC地址

	// 用户关联（可为空，表示未登录状态）
	UserID *uint `json:"userId" gorm:"index;comment:关联用户ID"` // 关联用户ID，可为空

	// 时间信息
	FirstSeenAt  time.Time `json:"firstSeenAt" gorm:"comment:首次见到时间"`  // 首次见到时间
	LastSeenAt   time.Time `json:"lastSeenAt" gorm:"comment:最后见到时间"`   // 最后见到时间
	LastReportAt time.Time `json:"lastReportAt" gorm:"comment:最后上报时间"` // 最后上报时间

	// 状态信息
	Status           int    `json:"status" gorm:"default:1;comment:状态 1:正常 2:禁用"`                      // 状态
	IsActive         bool   `json:"isActive" gorm:"-"`                                                 // 是否活跃,用token管理记录上的isActive字段赋值
	ReportCount      int64  `json:"reportCount" gorm:"default:0;comment:上报次数"`                         // 上报次数
	Remark           string `json:"remark" gorm:"comment:备注"`                                          // 备注
	IsDefault        bool   `json:"isDefault" gorm:"default:false;comment:是否默认"`                       // 是否默认
	MCPAccessAddress string `json:"mcpAccessAddress" gorm:"column:mcp_access_address;comment:MCP访问地址"` // MCP访问地址
	AgentId          string `json:"agentId" gorm:"column:agent_id;comment:智能体ID"`                      // 智能体ID
}

// DeviceReport 设备上报记录表
type DeviceReport struct {
	global.GVA_MODEL
	DeviceID   string    `json:"deviceId" gorm:"index;not null;comment:设备ID"`  // 设备ID
	UserID     *uint     `json:"userId" gorm:"index;comment:关联用户ID"`           // 关联用户ID，可为空
	IPAddress  string    `json:"ipAddress" gorm:"comment:上报IP地址"`              // 上报IP地址
	UserAgent  string    `json:"userAgent" gorm:"type:text;comment:用户代理"`      // 用户代理
	ReportData string    `json:"reportData" gorm:"type:longtext;comment:上报数据"` // 上报的完整数据(JSON)
	ReportAt   time.Time `json:"reportAt" gorm:"comment:上报时间"`                 // 上报时间
}

// TableName 设置表名
func (Device) TableName() string {
	return "devices"
}

// TableName 设置表名
func (DeviceReport) TableName() string {
	return "device_reports"
}

// DeviceStatus 设备状态枚举
const (
	DeviceStatusNormal   = 1 // 正常
	DeviceStatusDisabled = 2 // 禁用
)

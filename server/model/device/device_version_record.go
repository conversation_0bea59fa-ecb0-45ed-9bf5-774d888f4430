// 设备版本更新记录
package device

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 设备版本更新记录 结构体 DeviceVersionRecord
type DeviceVersionRecord struct {
	global.GVA_MODEL
	DeviceId       string    `json:"deviceId" form:"deviceId" gorm:"index;column:device_id;comment:设备ID;" binding:"required"`             //设备ID
	OsType         string    `json:"osType" form:"osType" gorm:"index;column:os_type;comment:操作系统标识;" binding:"required"`                 //操作系统标识
	CurrentVersion string    `json:"currentVersion" form:"currentVersion" gorm:"column:current_version;comment:当前版本;" binding:"required"` //当前版本
	TargetVersion  string    `json:"targetVersion" form:"targetVersion" gorm:"column:target_version;comment:目标版本;" binding:"required"`    //目标版本
	UpdateStatus   int       `json:"updateStatus" form:"updateStatus" gorm:"column:update_status;comment:更新状态;default:0"`                 //更新状态 0:待更新 1:更新中 2:更新成功 3:更新失败
	UpdateTime     time.Time `json:"updateTime" form:"updateTime" gorm:"column:update_time;comment:更新时间"`                                 //更新时间
	Remark         string    `json:"remark" form:"remark" gorm:"column:remark;comment:备注;type:text"`                                      //备注
}

// TableName 设备版本更新记录 DeviceVersionRecord自定义表名 device_version_record
func (DeviceVersionRecord) TableName() string {
	return "device_version_record"
}

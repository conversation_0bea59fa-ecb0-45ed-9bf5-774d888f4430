package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
)

// DeviceReportRequest 设备上报请求
type DeviceReportRequest struct {
	// 设备标识
	DeviceID string `json:"deviceId" binding:"required"` // 设备唯一标识码

	// 硬件信息
	CPUInfo     string `json:"cpuInfo"`     // CPU信息
	MemoryInfo  string `json:"memoryInfo"`  // 内存信息
	DiskInfo    string `json:"diskInfo"`    // 磁盘信息
	NetworkInfo string `json:"networkInfo"` // 网络信息
	GPUInfo     string `json:"gpuInfo"`     // 显卡信息

	// 系统环境信息
	OSName      string `json:"osName"`      // 操作系统名称
	OSVersion   string `json:"osVersion"`   // 操作系统版本
	OSArch      string `json:"osArch"`      // 系统架构
	Hostname    string `json:"hostname"`    // 主机名
	Username    string `json:"username"`    // 当前用户名
	UserHomeDir string `json:"userHomeDir"` // 用户主目录
	WorkDir     string `json:"workDir"`     // 工作目录

	// 应用信息
	AppVersion string `json:"appVersion"` // 应用版本
	AppBuildNo string `json:"appBuildNo"` // 应用构建号

	// 网络信息
	MACAddress string `json:"macAddress"` // MAC地址
}

// DeviceSearchRequest 设备搜索请求
type DeviceSearchRequest struct {
	device.Device
	request.PageInfo
	StartCreatedAt *string `json:"startCreatedAt" form:"startCreatedAt"` // 创建时间-开始
	EndCreatedAt   *string `json:"endCreatedAt" form:"endCreatedAt"`     // 创建时间-结束
}

// DeviceReportSearchRequest 设备上报记录搜索请求
type DeviceReportSearchRequest struct {
	device.DeviceReport
	request.PageInfo
	StartReportAt *string `json:"startReportAt" form:"startReportAt"` // 上报时间-开始
	EndReportAt   *string `json:"endReportAt" form:"endReportAt"`     // 上报时间-结束
}

// GetDeviceByIDRequest 根据ID获取设备请求
type GetDeviceByIDRequest struct {
	ID uint `json:"id" form:"id" binding:"required"` // 主键ID
}

// GetDeviceByDeviceIDRequest 根据设备ID获取设备请求
type GetDeviceByDeviceIDRequest struct {
	DeviceID string `json:"deviceId" form:"deviceId" binding:"required"` // 设备ID
}

// UpdateDeviceStatusRequest 更新设备状态请求
type UpdateDeviceStatusRequest struct {
	ID     uint `json:"id" binding:"required"`     // 主键ID
	Status int  `json:"status" binding:"required"` // 状态
}

// DeviceStatisticsRequest 设备统计请求
type DeviceStatisticsRequest struct {
	StartDate *string `json:"startDate" form:"startDate"` // 开始日期
	EndDate   *string `json:"endDate" form:"endDate"`     // 结束日期
	GroupBy   string  `json:"groupBy" form:"groupBy"`     // 分组方式: day, week, month
}

// MyDevicesRequest 获取我的设备列表请求
type MyDevicesRequest struct {
	request.PageInfo
}

// AssignDefaultAgentRequest 分配默认智能体请求
type AssignDefaultAgentRequest struct {
	UserID   string `json:"userId" binding:"required"`   // 用户ID
	DeviceID string `json:"deviceId" binding:"required"` // 设备ID
}

// UpdateDeviceNameRequest 修改设备名称请求
type UpdateDeviceNameRequest struct {
	DeviceID   string `json:"deviceId" binding:"required"`   // 设备ID
	DeviceName string `json:"deviceName" binding:"required"` // 新的设备名称
}

// DeviceLogoutRequest 设备登出请求
type DeviceLogoutRequest struct {
	DeviceID string `json:"deviceId" binding:"required"` // 要登出的设备ID
}

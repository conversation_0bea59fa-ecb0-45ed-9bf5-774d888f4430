package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"time"
)

// DeviceVersionRecordSearch 设备版本更新记录搜索结构体
type DeviceVersionRecordSearch struct {
	StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
	DeviceId       string     `json:"deviceId" form:"deviceId"`
	OsType         string     `json:"osType" form:"osType"`
	UpdateStatus   *int       `json:"updateStatus" form:"updateStatus"`
	request.PageInfo
}

// CheckUpdateRequest 检查更新请求结构体
type CheckUpdateRequest struct {
	CurrentVersion string `json:"currentVersion" form:"currentVersion" binding:"required"` //当前版本
}

// RecordUpdateRequest 记录更新请求结构体
type RecordUpdateRequest struct {
	CurrentVersion string `json:"currentVersion" form:"currentVersion" binding:"required"` //当前版本
	TargetVersion  string `json:"targetVersion" form:"targetVersion" binding:"required"`   //目标版本
	UpdateStatus   int    `json:"updateStatus" form:"updateStatus"`                        //更新状态
	Remark         string `json:"remark" form:"remark"`                                    //备注
}

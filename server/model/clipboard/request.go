package clipboard

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// ClipboardItemCreateRequest 创建剪贴板条目请求
type ClipboardItemCreateRequest struct {
	Title       string `json:"title" validate:"max=255"`
	Content     string `json:"content" validate:"required"`
	ContentType string `json:"content_type" validate:"required,oneof=text image video audio file"`
	FileURL     string `json:"file_url"`
	FileSize    int64  `json:"file_size"`
	Source      string `json:"source" validate:"oneof=wechat web api"`
	MediaId     string `json:"media_id"`
}

// ClipboardItemUpdateRequest 更新剪贴板条目请求
type ClipboardItemUpdateRequest struct {
	ID        uint   `json:"id" validate:"required"`
	Title     string `json:"title" validate:"max=255"`
	Content   string `json:"content"`
	IsRead    *bool  `json:"is_read"`
	IsStarred *bool  `json:"is_starred"`
}

// ClipboardItemListRequest 剪贴板条目列表请求
type ClipboardItemListRequest struct {
	request.PageInfo
	ContentType string `json:"content_type" form:"content_type"`
	Source      string `json:"source" form:"source"`
	IsRead      *bool  `json:"is_read" form:"is_read"`
	IsStarred   *bool  `json:"is_starred" form:"is_starred"`
	Keyword     string `json:"keyword" form:"keyword"`
	StartTime   string `json:"start_time" form:"start_time"`
	EndTime     string `json:"end_time" form:"end_time"`
}

// ClipboardConfigUpdateRequest 更新剪贴板配置请求
type ClipboardConfigUpdateRequest struct {
	MaxItems       int    `json:"max_items" validate:"min=1,max=1000"`
	AutoCleanDays  int    `json:"auto_clean_days" validate:"min=1,max=365"`
	EnableWechat   bool   `json:"enable_wechat"`
	EnableNotify   bool   `json:"enable_notify"`
	AllowFileTypes string `json:"allow_file_types"`
	MaxFileSize    int64  `json:"max_file_size" validate:"min=1,max=104857600"` // 最大100MB
}

// WechatOAuthRequest 微信OAuth请求
type WechatOAuthRequest struct {
	Code  string `json:"code" validate:"required"`
	State string `json:"state"`
}

// WechatWebhookRequest 微信Webhook请求
type WechatWebhookRequest struct {
	Signature string `form:"signature" binding:"required"`
	Timestamp string `form:"timestamp" binding:"required"`
	Nonce     string `form:"nonce" binding:"required"`
	Echostr   string `form:"echostr"`
}

// WechatMessageRequest 微信消息请求
type WechatMessageRequest struct {
	ToUserName   string  `xml:"ToUserName" json:"to_user_name"`
	FromUserName string  `xml:"FromUserName" json:"from_user_name"`
	CreateTime   int64   `xml:"CreateTime" json:"create_time"`
	MsgType      string  `xml:"MsgType" json:"msg_type"`
	Content      string  `xml:"Content" json:"content"`
	MediaId      string  `xml:"MediaId" json:"media_id"`
	PicUrl       string  `xml:"PicUrl" json:"pic_url"`
	Format       string  `xml:"Format" json:"format"`
	Recognition  string  `xml:"Recognition" json:"recognition"`
	ThumbMediaId string  `xml:"ThumbMediaId" json:"thumb_media_id"`
	LocationX    float64 `xml:"Location_X" json:"location_x"`
	LocationY    float64 `xml:"Location_Y" json:"location_y"`
	Scale        int     `xml:"Scale" json:"scale"`
	Label        string  `xml:"Label" json:"label"`
	Title        string  `xml:"Title" json:"title"`
	Description  string  `xml:"Description" json:"description"`
	Url          string  `xml:"Url" json:"url"`
	Event        string  `xml:"Event" json:"event"`
	EventKey     string  `xml:"EventKey" json:"event_key"`
	MsgID        int64   `xml:"MsgId" json:"msg_id"`
}

// ClipboardStatsResponse 剪贴板统计响应
type ClipboardStatsResponse struct {
	TotalItems   int64 `json:"total_items"`
	TextItems    int64 `json:"text_items"`
	ImageItems   int64 `json:"image_items"`
	VideoItems   int64 `json:"video_items"`
	AudioItems   int64 `json:"audio_items"`
	FileItems    int64 `json:"file_items"`
	UnreadItems  int64 `json:"unread_items"`
	StarredItems int64 `json:"starred_items"`
	TodayItems   int64 `json:"today_items"`
}

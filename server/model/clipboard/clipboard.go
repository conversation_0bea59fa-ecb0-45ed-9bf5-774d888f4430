package clipboard

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// ClipboardItem 剪贴板条目
type ClipboardItem struct {
	global.GVA_MODEL
	UserID      uint       `json:"user_id" gorm:"column:user_id;comment:用户ID;not null"`
	Title       string     `json:"title" gorm:"column:title;comment:标题;size:255"`
	Content     string     `json:"content" gorm:"column:content;comment:内容;type:text"`
	ContentType string     `json:"content_type" gorm:"column:content_type;comment:内容类型;size:50;default:'text'"` // text, image, video, audio, file
	FileURL     string     `json:"file_url" gorm:"column:file_url;comment:文件URL;size:500"`
	FileSize    int64      `json:"file_size" gorm:"column:file_size;comment:文件大小"`
	Source      string     `json:"source" gorm:"column:source;comment:来源;size:100;default:'wechat'"` // wechat, web, api
	FromUserID  string     `json:"from_user_id" gorm:"column:from_user_id;comment:发送者微信ID;size:100"`
	MsgID       string     `json:"msg_id" gorm:"column:msg_id;comment:消息ID;size:100"`
	IsRead      bool       `json:"is_read" gorm:"column:is_read;comment:是否已读;default:false"`
	IsStarred   bool       `json:"is_starred" gorm:"column:is_starred;comment:是否收藏;default:false"`
	ExpireAt    *time.Time `json:"expire_at" gorm:"column:expire_at;comment:过期时间"`
	MediaId     string     `json:"media_id" gorm:"column:media_id;comment:媒体ID;size:100"`
}

// TableName 表名
func (ClipboardItem) TableName() string {
	return "clipboard_items"
}

// ClipboardConfig 剪贴板配置
type ClipboardConfig struct {
	global.GVA_MODEL
	UserID         uint   `json:"user_id" gorm:"column:user_id;comment:用户ID;not null;uniqueIndex"`
	MaxItems       int    `json:"max_items" gorm:"column:max_items;comment:最大条目数;default:100"`
	AutoCleanDays  int    `json:"auto_clean_days" gorm:"column:auto_clean_days;comment:自动清理天数;default:30"`
	EnableWechat   bool   `json:"enable_wechat" gorm:"column:enable_wechat;comment:启用微信;default:true"`
	EnableNotify   bool   `json:"enable_notify" gorm:"column:enable_notify;comment:启用通知;default:true"`
	AllowFileTypes string `json:"allow_file_types" gorm:"column:allow_file_types;comment:允许的文件类型;size:500;default:'image,video,audio,document'"`
	MaxFileSize    int64  `json:"max_file_size" gorm:"column:max_file_size;comment:最大文件大小(字节);default:52428800"` // 50MB
}

// TableName 表名
func (ClipboardConfig) TableName() string {
	return "clipboard_configs"
}

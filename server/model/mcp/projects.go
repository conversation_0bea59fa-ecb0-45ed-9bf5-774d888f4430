// 自动生成模板Projects
package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

const (
	ProjectStatusCreated = "created"
	ProjectStatusDeleted = "deleted"
)

// Projects表 结构体  Projects
type Projects struct {
	global.GVA_MODEL
	UUID                       string         `json:"uuid" form:"uuid" gorm:"column:uuid;type:varchar(36);uniqueIndex;not null"`                                                    //项目UUID
	Name                       string         `json:"name" form:"name" gorm:"column:name;type:varchar(100);not null"`                                                               //项目名称
	Description                string         `json:"description" form:"description" gorm:"column:description;type:text"`                                                           //项目描述
	LlmRemark                  string         `json:"llmRemark" form:"llmRemark" gorm:"column:llm_remark;type:text"`                                                                // 备注
	LogoUrl                    string         `json:"logoUrl" form:"logoUrl" gorm:"column:logo_url;type:varchar(191)"`                                                              //项目logo URL
	Github                     string         `json:"github" form:"github" gorm:"column:github;type:varchar(191)"`                                                                  //Github地址
	ProviderUrl                string         `json:"providerUrl" form:"providerUrl" gorm:"column:provider_url;type:varchar(191)"`                                                  //提供商网站地址
	Detail                     string         `json:"detail" form:"detail" gorm:"column:detail;type:text"`                                                                          //详细描述
	ExampleConfig              string         `json:"exampleConfig" form:"exampleConfig" gorm:"column:example_config;type:text"`                                                    //示例配置
	CallMethod                 string         `json:"callMethod" form:"callMethod" gorm:"column:call_method;type:varchar(191)"`                                                     //调用方法
	IsOfficial                 bool           `json:"isOfficial" form:"isOfficial" gorm:"column:is_official"`                                                                       //是否官方
	OnlineUsageCount           int            `json:"onlineUsageCount" form:"onlineUsageCount" gorm:"column:online_usage_count"`                                                    //在线使用次数
	OfflineUsageCount          int            `json:"offlineUsageCount" form:"offlineUsageCount" gorm:"column:offline_usage_count"`                                                 //离线使用次数
	Likes                      int            `json:"likes" form:"likes" gorm:"column:likes"`                                                                                       //点赞数
	Comments                   int            `json:"comments" form:"comments" gorm:"column:comments;default:0"`                                                                    //评论数
	AverageRating              float64        `json:"averageRating" form:"averageRating" gorm:"column:average_rating;type:decimal(3,2);default:0.00"`                               //平均评分
	Status                     string         `json:"status" form:"status" gorm:"column:status;type:varchar(20);default:'created'"`                                                 //状态
	Tags                       string         `json:"tags" form:"tags" gorm:"column:tags;type:text"`                                                                                //标签
	Category                   string         `json:"category" form:"category" gorm:"column:category;type:varchar(50)"`                                                             //分类
	IsFeatured                 bool           `json:"isFeatured" form:"isFeatured" gorm:"column:is_featured;default:true"`                                                          //是否特色,是否AIDO展示
	Sort                       int            `json:"sort" form:"sort" gorm:"column:sort;default:0"`                                                                                //排序
	Code                       string         `json:"code" form:"code" gorm:"column:code;type:varchar(50)"`                                                                         //项目代号
	AllowCall                  bool           `json:"allowCall" form:"allowCall" gorm:"column:allow_call"`                                                                          //是否允许调用
	Package                    string         `json:"package" form:"package" gorm:"column:package;type:varchar(500)"`                                                               //包名
	Provider                   string         `json:"provider" form:"provider" gorm:"column:provider;type:varchar(191)"`                                                            //提供商
	ProjectTools               []ProjectTools `json:"projectTools" gorm:"foreignKey:ProjectId"`                                                                                     //项目工具列表
	IsLiked                    bool           `json:"isLiked" gorm:"-"`                                                                                                             //是否已点赞
	IsEnabled                  bool           `json:"isEnabled" form:"isEnabled" gorm:"column:is_enabled;default:false"`                                                            //是否启用
	AuditStatus                string         `json:"auditStatus" form:"auditStatus" gorm:"column:audit_status;type:varchar(20);default:'pending'"`                                 // 审核状态
	Questions                  string         `json:"questions" form:"questions" gorm:"column:questions;type:json"`                                                                 // 在线提问问题
	Command                    string         `json:"command" form:"command" gorm:"column:command;type:text"`                                                                       // 执行server用的命令
	Args                       string         `json:"args" form:"args" gorm:"column:args;type:json"`                                                                                // 执行server用的参数
	Env                        string         `json:"env" form:"env" gorm:"column:env;type:json"`                                                                                   // 执行server用的环境变量
	EnvReal                    string         `json:"envReal" form:"envReal" gorm:"column:env_real;type:json"`                                                                      // 执行server用的环境变量(真实)
	BaseUrl                    string         `json:"baseUrl" form:"baseUrl" gorm:"column:base_url;type:varchar(500)"`                                                              // sse或streamableHttp的server的请求地址
	Headers                    string         `json:"headers" form:"headers" gorm:"column:headers;type:json"`                                                                       // sse或streamableHttp的server的请求头
	Version                    string         `json:"version" form:"version" gorm:"column:version;type:varchar(50)"`                                                                // 版本号
	KeyParamName               string         `json:"keyParamName" form:"keyParamName" gorm:"column:key_param_name;type:varchar(100)"`                                              // key参数名
	SupportedPlatforms         datatypes.JSON `json:"supportedPlatforms" form:"supportedPlatforms" gorm:"column:supported_platforms;comment:支持的平台;type:text;" swaggertype:"object"` // 支持的平台，JSON数组格式如["mac", "windows", "linux"]
	ProxySseUrl                string         `json:"proxySseUrl" form:"proxySseUrl" gorm:"column:proxy_sse_url;comment:第三方SSE代理URL;type:varchar(500)"`                             // 第三方SSE代理URL
	ProxyHttpUrl               string         `json:"proxyHttpUrl" form:"proxyHttpUrl" gorm:"column:proxy_http_url;comment:第三方HTTP代理URL;type:varchar(500)"`                         // 执行server用的命令
	RequiredVariables          string         `json:"requiredVariables" form:"requiredVariables" gorm:"column:required_variables;comment:必填变量(JSON数组字符串);type:json"`
	Path                       string         `json:"path" form:"path" gorm:"column:path;type:varchar(500)"`                                                    // 路径
	EnableLongTask             bool           `json:"enableLongTask" form:"enableLongTask" gorm:"column:enable_long_task;comment:是否启用长任务;default:false"`        // 长任务开关
	IsDependTransfer           bool           `json:"isDependTransfer" form:"isDependTransfer" gorm:"column:is_depend_transfer;comment:是否依赖上传下载;default:false"` // 是否依赖上传下载
	CanHandleDirectory         *int           `json:"canHandleDirectory" form:"canHandleDirectory" gorm:"column:can_handle_directory;comment:是否可处理目录;size:10;default:0"` // 是否可处理目录：0-否，1-是（基于关联ProjectTools计算得出）
	SupportedExtensions        datatypes.JSON `json:"supportedExtensions" form:"supportedExtensions" gorm:"column:supported_extensions;comment:支持的文件扩展名;type:text;" swaggertype:"object"` // 支持的文件扩展名，JSON数组格式（基于关联ProjectTools的并集）
	AllowToolDownloadRetrieval *bool          `json:"allowToolDownloadRetrieval" gorm:"-"`
}

// TableName projects表 Projects自定义表名 projects
func (Projects) TableName() string {
	return "projects"
}

// ProjectFilter 项目过滤条件
type ProjectFilter struct {
	Page       int    `form:"page"`
	Limit      int    `form:"limit"`
	OrderBy    string `form:"orderBy"`
	Status     string `form:"status"`
	UserUUID   string `form:"userUuid"`
	Keyword    string `form:"keyword"`
	Category   string `form:"category"`
	Tag        string `form:"tag"`
	IsFeatured *bool  `form:"isFeatured"`
	AllowCall  *bool  `form:"allowCall"`
}

// // MarshalJSON 自定义JSON序列化
// func (p Projects) MarshalJSON() ([]byte, error) {
// 	type Alias Projects
// 	return json.Marshal(&struct {
// 		*Alias
// 		CreatedAt string `json:"createdAt"`
// 		UpdatedAt string `json:"updatedAt"`
// 		StartTime string `json:"startTime"`
// 		EndTime   string `json:"endTime"`
// 	}{
// 		Alias:     (*Alias)(&p),
// 		CreatedAt: p.CreatedAt.Format("2006-01-02 15:04:05"),
// 		UpdatedAt: p.UpdatedAt.Format("2006-01-02 15:04:05"),
// 		StartTime: p.StartTime.Format("2006-01-02 15:04:05"),
// 		EndTime:   p.EndTime.Format("2006-01-02 15:04:05"),
// 	})
// }

// // UnmarshalJSON 自定义JSON反序列化
// func (p *Projects) UnmarshalJSON(data []byte) error {
// 	type Alias Projects
// 	aux := &struct {
// 		*Alias
// 		CreatedAt string `json:"createdAt"`
// 		UpdatedAt string `json:"updatedAt"`
// 		StartTime string `json:"startTime"`
// 		EndTime   string `json:"endTime"`
// 	}{
// 		Alias: (*Alias)(p),
// 	}
// 	if err := json.Unmarshal(data, &aux); err != nil {
// 		return err
// 	}

// 	// 解析时间字符串
// 	layout := "2006-01-02 15:04:05"
// 	if aux.CreatedAt != "" {
// 		if t, err := time.Parse(layout, aux.CreatedAt); err == nil {
// 			p.CreatedAt = global.DateTime(t)
// 		}
// 	}
// 	if aux.UpdatedAt != "" {
// 		if t, err := time.Parse(layout, aux.UpdatedAt); err == nil {
// 			p.UpdatedAt = global.DateTime(t)
// 		}
// 	}
// 	if aux.StartTime != "" {
// 		if t, err := time.Parse(layout, aux.StartTime); err == nil {
// 			p.StartTime = t
// 		}
// 	}
// 	if aux.EndTime != "" {
// 		if t, err := time.Parse(layout, aux.EndTime); err == nil {
// 			p.EndTime = t
// 		}
// 	}

// 	return nil
// }

package mcp

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type Token struct {
	ID        uint       `json:"id" gorm:"primarykey"`
	Token     string     `json:"token" gorm:"uniqueIndex;comment:Token值"`
	Status    string     `json:"status" gorm:"comment:状态"`
	CreatedAt time.Time  `json:"createdAt" gorm:"comment:创建时间"`
	ExpiresAt *time.Time `json:"expiresAt" gorm:"comment:过期时间"`
	UserID    uint       `json:"userId" gorm:"comment:用户ID"`
}

func (Token) TableName() string {
	return "tokens"
}

const (
	TokenStatusActive   = "active"
	TokenStatusInactive = "inactive"
	TokenStatusExpired  = "expired"
)

func FindTokenByValue(token string) (*Token, error) {
	if global.GVA_DB == nil {
		return nil, fmt.Errorf("database connection not initialized")
	}
	t := &Token{}
	err := global.GVA_DB.
		Where("token = ?", token).
		Where("status = ?", TokenStatusActive).
		First(t).Error
	return t, err
}

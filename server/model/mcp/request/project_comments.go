package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
)

// ProjectCommentsSearch 项目评论搜索结构体
type ProjectCommentsSearch struct {
	mcp.ProjectComments
	request.PageInfo
	StartCreatedAt *string `json:"startCreatedAt" form:"startCreatedAt"` // 创建时间-开始
	EndCreatedAt   *string `json:"endCreatedAt" form:"endCreatedAt"`     // 创建时间-结束
	ProjectID      *uint   `json:"projectId" form:"projectId"`           // 项目ID过滤
	Rating         *int    `json:"rating" form:"rating"`                 // 评分过滤
	Status         string  `json:"status" form:"status"`                 // 状态过滤
}

// CreateProjectCommentRequest 创建评论请求
type CreateProjectCommentRequest struct {
	ProjectID   uint   `json:"projectId" binding:"required"`              // 项目ID
	Rating      int    `json:"rating" binding:"required,min=1,max=5"`     // 评分(1-5星)
	Content     string `json:"content" binding:"required,min=1,max=1000"` // 评论内容
	IsAnonymous bool   `json:"isAnonymous"`                               // 是否匿名评论
}

// UpdateProjectCommentRequest 更新评论请求
type UpdateProjectCommentRequest struct {
	ID          uint   `json:"id" binding:"required"`                     // 评论ID
	Rating      int    `json:"rating" binding:"required,min=1,max=5"`     // 评分(1-5星)
	Content     string `json:"content" binding:"required,min=1,max=1000"` // 评论内容
	IsAnonymous bool   `json:"isAnonymous"`                               // 是否匿名评论
}

// ProjectCommentsListRequest 获取项目评论列表请求
type ProjectCommentsListRequest struct {
	request.PageInfo
	ProjectID uint   `json:"projectId" form:"projectId" binding:"required"` // 项目ID
	Rating    *int   `json:"rating" form:"rating"`                          // 评分过滤
	Status    string `json:"status" form:"status"`                          // 状态过滤，默认为approved
}

// ReviewCommentRequest 审核评论请求
type ReviewCommentRequest struct {
	CommentID  uint   `json:"commentId" binding:"required"`                   // 评论ID
	Action     string `json:"action" binding:"required,oneof=approve reject"` // 审核动作：approve-通过，reject-拒绝
	ReviewNote string `json:"reviewNote" binding:"max=500"`                   // 审核备注
}

// PendingCommentsListRequest 获取待审核评论列表请求
type PendingCommentsListRequest struct {
	request.PageInfo
	ProjectID *uint  `json:"projectId" form:"projectId"` // 项目ID过滤（可选）
	Rating    *int   `json:"rating" form:"rating"`       // 评分过滤（可选）
	Status    string `json:"status" form:"status"`       // 状态过滤，默认为pending
}

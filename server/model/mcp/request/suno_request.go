package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// SunoTaskSearchReq 搜索请求结构体
type SunoTaskSearchReq struct {
	request.PageInfo
	TaskID       string `json:"taskId" form:"taskId"`             // 任务ID
	Status       string `json:"status" form:"status"`             // 状态
	CallbackType string `json:"callbackType" form:"callbackType"` // 回调类型
	UserUUID     string `json:"userUuid" form:"userUuid"`         // 用户UUID
	StartTime    string `json:"startTime" form:"startTime"`       // 开始时间
	EndTime      string `json:"endTime" form:"endTime"`           // 结束时间
}

// CreateSunoTaskReq 创建任务请求结构体
type CreateSunoTaskReq struct {
	TaskID       string `json:"taskId" binding:"required"`        // 任务ID
	UserUUID     string `json:"userUuid"`                         // 用户UUID
	CallbackType string `json:"callbackType" binding:"required"`  // 回调类型
	Prompt       string `json:"prompt"`                           // 提示词
	ModelName    string `json:"modelName"`                        // 模型名称
}

package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type McpClientSearch struct {
	StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
	Name           *string    `json:"name" form:"name" `
	Category       *string    `json:"category" form:"category" `
	Developer      *string    `json:"developer" form:"developer" `
	request.PageInfo
}

// Suno音乐生成任务模型
package mcp

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// SunoTask Suno任务表结构体
type SunoTask struct {
	global.GVA_MODEL
	TaskID               string     `json:"taskId" form:"taskId" gorm:"column:task_id;type:varchar(100);uniqueIndex;not null;comment:任务ID"`
	MusicID              string     `json:"musicId" form:"musicId" gorm:"column:music_id;type:varchar(100);comment:音乐ID"`
	AudioURL             string     `json:"audioUrl" form:"audioUrl" gorm:"column:audio_url;type:varchar(500);comment:音频URL"`
	SourceAudioURL       string     `json:"sourceAudioUrl" form:"sourceAudioUrl" gorm:"column:source_audio_url;type:varchar(500);comment:源音频URL"`
	StreamAudioURL       string     `json:"streamAudioUrl" form:"streamAudioUrl" gorm:"column:stream_audio_url;type:varchar(500);comment:流音频URL"`
	SourceStreamAudioURL string     `json:"sourceStreamAudioUrl" form:"sourceStreamAudioUrl" gorm:"column:source_stream_audio_url;type:varchar(500);comment:源流音频URL"`
	ImageURL             string     `json:"imageUrl" form:"imageUrl" gorm:"column:image_url;type:varchar(500);comment:图片URL"`
	SourceImageURL       string     `json:"sourceImageUrl" form:"sourceImageUrl" gorm:"column:source_image_url;type:varchar(500);comment:源图片URL"`
	Prompt               string     `json:"prompt" form:"prompt" gorm:"column:prompt;type:text;comment:提示词"`
	ModelName            string     `json:"modelName" form:"modelName" gorm:"column:model_name;type:varchar(100);comment:模型名称"`
	Title                string     `json:"title" form:"title" gorm:"column:title;type:varchar(200);comment:标题"`
	Tags                 string     `json:"tags" form:"tags" gorm:"column:tags;type:varchar(500);comment:标签"`
	Duration             float64    `json:"duration" form:"duration" gorm:"column:duration;type:decimal(10,2);comment:时长(秒)"`
	CallbackType         string     `json:"callbackType" form:"callbackType" gorm:"column:callback_type;type:varchar(50);comment:回调类型"`
	Status               string     `json:"status" form:"status" gorm:"column:status;type:varchar(20);default:'pending';comment:状态"`
	TaskCreateTime       *time.Time `json:"taskCreateTime" form:"taskCreateTime" gorm:"column:task_create_time;comment:任务创建时间"`
	UserUUID             string     `json:"userUuid" form:"userUuid" gorm:"column:user_uuid;type:varchar(36);comment:用户UUID"`
	ErrorMessage         string     `json:"errorMessage" form:"errorMessage" gorm:"column:error_message;type:text;comment:错误信息"`
}

// TableName 自定义表名
func (SunoTask) TableName() string {
	return "suno_tasks"
}

// SunoCallbackRequest 回调请求结构体
type SunoCallbackRequest struct {
	Code int              `json:"code" binding:"required"`
	Msg  string           `json:"msg"`
	Data SunoCallbackData `json:"data"`
}

// SunoCallbackData 回调数据结构体
type SunoCallbackData struct {
	TaskID       string      `json:"task_id" binding:"required"`
	CallbackType string      `json:"callbackType"`
	Data         []SunoMusic `json:"data"`
}

// SunoMusic 音乐数据结构体
type SunoMusic struct {
	ID                   string      `json:"id"`
	AudioURL             string      `json:"audio_url"`
	SourceAudioURL       string      `json:"source_audio_url"`
	StreamAudioURL       string      `json:"stream_audio_url"`
	SourceStreamAudioURL string      `json:"source_stream_audio_url"`
	ImageURL             string      `json:"image_url"`
	SourceImageURL       string      `json:"source_image_url"`
	Prompt               string      `json:"prompt"`
	ModelName            string      `json:"model_name"`
	Title                string      `json:"title"`
	Tags                 string      `json:"tags"`
	CreateTime           interface{} `json:"createTime"` // 支持string和number类型
	Duration             float64     `json:"duration"`
}

// SunoQueryResponse 查询响应结构体
type SunoQueryResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// TaskStatusConstants 任务状态常量
const (
	TaskStatusPending   = "pending"   // 待处理
	TaskStatusRunning   = "running"   // 运行中
	TaskStatusCompleted = "completed" // 已完成
	TaskStatusFailed    = "failed"    // 失败
)

// CallbackTypeConstants 回调类型常量
const (
	CallbackTypeGenerate = "generate" // 音乐生成
	CallbackTypeCover    = "cover"    // 上传并翻唱
	CallbackTypeExtend   = "extend"   // 上传并扩展
	CallbackTypeComplete = "complete" // 所有音乐完成（官方文档中的类型）
	CallbackTypeText     = "text"     // 文本生成完成
	CallbackTypeFirst    = "first"    // 第一首音乐完成
	CallbackTypeError    = "error"    // 任务失败
)

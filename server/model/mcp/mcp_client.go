// 自动生成模板McpClient
package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// mcp客户端 结构体  McpClient
type McpClient struct {
	global.GVA_MODEL
	Name          *string        `json:"name" form:"name" gorm:"column:name;comment:;"`                                                  //客户端名称
	Description   *string        `json:"description" form:"description" gorm:"column:description;comment:;"`                             //描述
	Version       *string        `json:"version" form:"version" gorm:"column:version;comment:;"`                                         //版本
	ReleaseDate   *string        `json:"releaseDate" form:"releaseDate" gorm:"column:release_date;comment:;"`                            //发行日期
	Size          *string        `json:"size" form:"size" gorm:"column:size;comment:;"`                                                  //大小
	Platform      datatypes.JSON `json:"platform" form:"platform" gorm:"column:platform;comment:;type:text;" swaggertype:"array,object"` //适用平台
	Category      *string        `json:"category" form:"category" gorm:"column:category;comment:;"`                                      //客户端类型
	DownloadCount *int           `json:"downloadCount" form:"downloadCount" gorm:"column:download_count;comment:;"`                      //下载次数
	Features      datatypes.JSON `json:"features" form:"features" gorm:"column:features;comment:;type:text;" swaggertype:"array,object"` //产品特点
	Requirements  *string        `json:"requirements" form:"requirements" gorm:"column:requirements;comment:;"`                          //安装要求
	Developer     *string        `json:"developer" form:"developer" gorm:"column:developer;comment:;"`                                   //开发商
	Logo          string         `json:"logo" form:"logo" gorm:"column:logo;comment:;"`                                                  //logo地址
	DownloadUrl   *string        `json:"downloadUrl" form:"downloadUrl" gorm:"column:download_url;comment:;"`                            //下载地址
}

// TableName mcp客户端 McpClient自定义表名 mcp_client
func (McpClient) TableName() string {
	return "mcp_client"
}

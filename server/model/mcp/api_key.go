package mcp

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type Api<PERSON>ey struct {
	global.GVA_MODEL
	Name      string     `json:"name" gorm:"comment:密钥名称"`
	ApiKey    string     `json:"apiKey" gorm:"uniqueIndex;comment:API密钥"`
	UserId    uint       `json:"userId" gorm:"comment:用户ID"`
	Status    string     `json:"status" gorm:"comment:状态"`
	ExpiresAt *time.Time `json:"expiresAt" gorm:"comment:过期时间"`
}

func (ApiKey) TableName() string {
	return "api_keys"
}

const (
	ApiKeyStatusActive   = "active"
	ApiKeyStatusInactive = "inactive"
	ApiKeyStatusExpired  = "expired"
)

func FindApiKeyByKey(apiKey string) (*ApiKey, error) {
	k := &ApiKey{}
	err := global.GVA_DB.
		Where("api_key = ?", apiKey).
		Where("status = ?", ApiKeyStatusActive).
		First(k).Error
	return k, err
}

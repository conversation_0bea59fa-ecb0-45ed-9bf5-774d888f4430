package mcp

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// UintSlice 用于支持 json 数组类型的自动序列化/反序列化
// ServerIds 字段推荐用此类型
type UintSlice []uint

func (s *UintSlice) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("Scan source is not []byte")
	}
	return json.Unmarshal(bytes, s)
}

func (s UintSlice) Value() (driver.Value, error) {
	return json.Marshal(s)
}

type UseCase struct {
	global.GVA_MODEL
	Homepage  string    `json:"homepage"`
	Content   string    `json:"content" gorm:"type:text"`
	Summary   string    `json:"summary"`
	Video     *string   `json:"video"`
	Cover     *string   `json:"cover"`
	Likes     int       `json:"likes"`
	Comments  int       `json:"comments"`
	Shares    int       `json:"shares"`
	Date      string    `json:"date"`
	ServerIds UintSlice `json:"serverIds" gorm:"type:json"`
	ClientId  *int      `json:"clientId"`
	Status    int       `json:"status" gorm:"default:0"`
	UserId    uint      `json:"userId" gorm:"comment:创建人ID"`
}

func (UseCase) TableName() string {
	return "use_cases"
}

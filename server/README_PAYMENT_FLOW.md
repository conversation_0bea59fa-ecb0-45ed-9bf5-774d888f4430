# 支付流程完整说明

## 概述

这个文档说明了从购买商品到支付成功后处理的完整流程，以及 `ProcessPaymentSuccess` 方法的使用位置。

## 完整流程

### 1. 用户购买商品

```go
// 前端调用购买接口
POST /api/v1/product/buy
{
    "productId": 1,
    "quantity": 1,
    "paymentMethod": "wechat",
    "expireMinutes": 30
}
```

**对应代码位置**: `api/v1/product/product_api.go` -> `BuyProduct` -> `service/product/purchase_service.go` -> `BuyProduct`

### 2. 创建订单和支付

`BuyProduct` 方法执行步骤：
1. 验证商品信息
2. 检查库存
3. 创建支付订单（调用支付服务）
4. 创建订单项（产品订单）
5. 返回支付链接

**返回数据**:
```go
{
    "orderNo": "PAY20231201123456789",
    "paymentUrl": "weixin://wxpay/s/...",
    "amount": 10000
}
```

### 3. 用户完成支付

用户通过返回的 `paymentUrl` 完成支付（微信、支付宝等）

### 4. 支付平台异步通知

支付成功后，支付平台会向我们的服务器发送异步通知：

**微信支付通知**: `POST /api/payment/notify/wechat`
**支付宝通知**: `POST /api/payment/notify/alipay`

**对应代码位置**: `api/v1/payment/payment_api.go` -> `WechatNotify`/`AlipayNotify`

### 5. 处理支付通知

```go
// api/v1/payment/payment_api.go
func (p *PaymentApi) WechatNotify(c *gin.Context) {
    // 读取通知数据
    body, err := ioutil.ReadAll(c.Request.Body)
    
    // 调用支付服务处理通知
    err = paymentService.ProcessNotify(payment.PaymentMethodWechat, body)
    
    // 返回处理结果给支付平台
    c.String(http.StatusOK, "SUCCESS")
}
```

### 6. 验证并更新支付状态

```go
// service/payment/payment_service.go
func (p *PaymentService) ProcessNotify(...) error {
    // 1. 验证通知签名
    notifyResult, err := paymentClient.VerifyNotify(notifyData)
    
    // 2. 更新支付订单状态
    global.GVA_DB.Model(&order).Updates(updateData)
    
    // 3. 如果支付成功，处理业务逻辑
    if notifyResult.Status == payment.PaymentStatusPaid {
        go func() {
            p.handlePaymentSuccess(notifyResult.OrderNo)
        }()
    }
}
```

### 7. 发布支付成功事件

```go
// service/payment/payment_service.go
func (p *PaymentService) handlePaymentSuccess(orderNo string) error {
    // 检查是否有相关的产品订单项
    var count int64
    global.GVA_DB.Table("order_items").Where("order_no = ?", orderNo).Count(&count)
    
    if count > 0 {
        // 发布支付成功事件
        return p.callProductProcessPaymentSuccess(orderNo)
    }
}

func (p *PaymentService) callProductProcessPaymentSuccess(orderNo string) error {
    event := global.PaymentSuccessEvent{OrderNo: orderNo}
    return global.GVA_EVENT_MANAGER.Publish(global.EventTypePaymentSuccess, event)
}
```

### 8. 🎯 **ProcessPaymentSuccess 方法被调用** 

```go
// service/product/purchase_service.go
func InitEventListeners() {
    // 监听支付成功事件
    global.GVA_EVENT_MANAGER.Subscribe(global.EventTypePaymentSuccess, func(data interface{}) error {
        event := data.(global.PaymentSuccessEvent)
        
        // 🎯 这里调用了您的 ProcessPaymentSuccess 方法！
        return PurchaseServiceApp.ProcessPaymentSuccess(event.OrderNo)
    })
}
```

### 9. 处理支付成功后的业务逻辑

```go
// service/product/purchase_service.go
func (p *PurchaseService) ProcessPaymentSuccess(orderNo string) error {
    return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
        // 获取订单项
        var orderItems []product.OrderItem
        tx.Where("order_no = ?", orderNo).Find(&orderItems)

        for _, item := range orderItems {
            // 处理会员商品 - 激活会员
            if item.ProductType == product.ProductTypeMembership {
                p.activateMembership(tx, orderNo, item)
            }

            // 更新订单项状态为已处理
            tx.Model(&item).Update("status", product.OrderItemStatusProcessed)

            // 更新商品销量
            tx.Model(&product.Product{}).Where("id = ?", item.ProductID).
                UpdateColumn("sales_count", gorm.Expr("sales_count + ?", item.Quantity))
        }

        return nil
    })
}
```

## 架构设计说明

### 为什么使用事件系统？

1. **避免循环依赖**: 支付服务和产品服务互相依赖会导致编译错误
2. **松耦合**: 支付服务不需要知道具体的业务处理逻辑
3. **可扩展**: 其他服务也可以监听支付成功事件
4. **异步处理**: 不阻塞支付回调的响应

### 事件系统组件

```go
// global/event.go
type EventManager struct {
    handlers map[string][]EventHandler
    mutex    sync.RWMutex
}

// 支付成功事件
type PaymentSuccessEvent struct {
    OrderNo string `json:"orderNo"`
}

const EventTypePaymentSuccess = "payment.success"
```

### 初始化顺序

1. `main.go` -> `initialize.OtherInit()`
2. `initialize/other.go` -> `product.InitEventListeners()`
3. `service/product/purchase_service.go` -> 注册事件监听器

## 调试和监控

### 日志跟踪

整个流程中的关键日志：

```bash
# 1. 创建订单
[INFO] 创建产品订单 orderNo=PAY20231201123456789

# 2. 支付通知
[INFO] 处理支付通知成功 orderNo=PAY20231201123456789 status=2

# 3. 事件发布
[INFO] 支付成功事件发布完成 orderNo=PAY20231201123456789

# 4. 事件处理
[INFO] 收到支付成功事件 orderNo=PAY20231201123456789

# 5. 业务处理
[INFO] 激活会员成功 orderNo=PAY20231201123456789 membershipType=basic
```

### 错误处理

如果 `ProcessPaymentSuccess` 执行失败：

```go
// 错误会被记录，但不会影响支付状态
global.GVA_LOG.Error("处理支付成功业务逻辑失败",
    zap.String("orderNo", orderNo),
    zap.Error(err))
```

## 总结

**`ProcessPaymentSuccess` 方法在支付成功异步通知处理完成后被调用**，具体流程是：

```
支付平台通知 -> ProcessNotify -> 发布事件 -> ProcessPaymentSuccess -> 业务处理
```

这个设计确保了：
- ✅ 支付状态正确更新
- ✅ 业务逻辑正确执行
- ✅ 系统架构清晰
- ✅ 错误处理完善
- ✅ 可扩展性强 
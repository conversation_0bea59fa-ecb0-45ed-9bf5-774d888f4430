# gRPC 集成指南

本项目已集成 gRPC 支持，可用于跨平台后端服务通信。

## 功能特性

- ✅ 完整的 gRPC 服务器实现
- ✅ 用户服务 gRPC 接口
- ✅ JWT 认证拦截器
- ✅ 日志记录拦截器  
- ✅ 错误恢复拦截器
- ✅ gRPC 反射支持
- ✅ 客户端示例代码

## 配置

在 `config.yaml` 中添加 gRPC 相关配置：

```yaml
system:
  # ... 其他配置 ...
  # gRPC相关配置
  grpc-addr: 9999          # gRPC服务端口
  enable-grpc: true        # 是否启用gRPC服务
  grpc-reflection: true    # 是否启用gRPC反射
```

## 项目结构

```
server/
├── grpc/                          # gRPC 相关代码
│   ├── server/                    # gRPC 服务器
│   │   └── server.go             # 服务器核心实现
│   ├── interceptor/              # 拦截器
│   │   ├── auth.go              # 认证拦截器
│   │   ├── logging.go           # 日志拦截器
│   │   └── recovery.go          # 恢复拦截器
│   └── services/                 # gRPC 服务实现
│       └── user_service.go      # 用户服务实现
├── proto/                        # Protocol Buffers 定义
│   ├── common/                   # 通用消息定义
│   │   └── common.proto         
│   ├── user/                     # 用户服务定义
│   │   └── user.proto           
│   ├── generated/                # 生成的 Go 代码
│   └── generate.sh              # 代码生成脚本
└── examples/                     # 示例代码
    └── grpc_client/              # gRPC 客户端示例
        └── main.go              
```

## 快速开始

### 1. 生成 Protocol Buffers 代码

```bash
cd proto
./generate.sh
```

### 2. 启动服务器

确保配置文件中启用了 gRPC，然后启动服务器：

```bash
go run main.go
```

服务器启动后会显示：
- HTTP 服务器运行在端口 8888
- gRPC 服务器运行在端口 9999

### 3. 运行客户端示例

```bash
# 基本测试
cd examples/grpc_client
go run main.go

# 测试用户认证
go run main.go -username=admin -password=123456
```

## API 接口

### 用户服务 (UserService)

#### 1. 用户认证
```protobuf
rpc Authenticate(AuthenticateRequest) returns (AuthenticateResponse);
```

#### 2. 获取用户信息
```protobuf
rpc GetUser(GetUserRequest) returns (GetUserResponse);
```

#### 3. 获取用户列表
```protobuf
rpc GetUserList(GetUserListRequest) returns (GetUserListResponse);
```

#### 4. 创建用户
```protobuf
rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
```

#### 5. 更新用户
```protobuf
rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
```

#### 6. 删除用户
```protobuf
rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse);
```

## 客户端使用示例

### Go 客户端

```go
package main

import (
    "context"
    "log"
    
    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
    "google.golang.org/grpc/metadata"
    
    userPb "path/to/proto/generated/user"
)

func main() {
    // 连接到 gRPC 服务器
    conn, err := grpc.Dial("localhost:9999", 
        grpc.WithTransportCredentials(insecure.NewCredentials()))
    if err != nil {
        log.Fatal(err)
    }
    defer conn.Close()
    
    // 创建客户端
    client := userPb.NewUserServiceClient(conn)
    
    // 用户认证
    authResp, err := client.Authenticate(context.Background(), &userPb.AuthenticateRequest{
        Username: "admin",
        Password: "123456",
    })
    if err != nil {
        log.Fatal(err)
    }
    
    // 使用返回的 token 进行后续请求
    ctx := metadata.AppendToOutgoingContext(context.Background(), 
        "authorization", "Bearer " + authResp.Token)
    
    // 获取用户信息
    userResp, err := client.GetUser(ctx, &userPb.GetUserRequest{
        UserId: 1,
    })
    if err != nil {
        log.Fatal(err)
    }
    
    log.Printf("用户信息: %+v", userResp.User)
}
```

### Python 客户端

```python
import grpc
from proto.generated.user import user_pb2_grpc, user_pb2

def main():
    # 连接到 gRPC 服务器
    channel = grpc.insecure_channel('localhost:9999')
    client = user_pb2_grpc.UserServiceStub(channel)
    
    # 用户认证
    auth_request = user_pb2.AuthenticateRequest(
        username='admin',
        password='123456'
    )
    
    auth_response = client.Authenticate(auth_request)
    
    if auth_response.base.success:
        token = auth_response.token
        
        # 使用 token 进行后续请求
        metadata = [('authorization', f'Bearer {token}')]
        
        user_request = user_pb2.GetUserRequest(user_id=1)
        user_response = client.GetUser(user_request, metadata=metadata)
        
        if user_response.base.success:
            print(f"用户信息: {user_response.user}")
    else:
        print(f"认证失败: {auth_response.base.message}")

if __name__ == '__main__':
    main()
```

## 认证机制

gRPC 服务使用 JWT 认证机制：

1. 通过 `Authenticate` 接口获取 JWT token
2. 在后续请求的 metadata 中携带 token：
   ```
   authorization: Bearer <your_jwt_token>
   ```
3. 认证拦截器会验证 token 并提取用户信息

## 测试工具

### 使用 grpcurl 测试

安装 grpcurl：
```bash
go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest
```

列出所有服务：
```bash
grpcurl -plaintext localhost:9999 list
```

调用用户认证接口：
```bash
grpcurl -plaintext -d '{"username":"admin","password":"123456"}' \
  localhost:9999 user.UserService/Authenticate
```

### 使用 Postman

Postman 也支持 gRPC，可以：
1. 创建新的 gRPC 请求
2. 输入服务器地址：`localhost:9999`
3. 导入 proto 文件
4. 选择要调用的方法并输入参数

## 扩展开发

### 添加新的 gRPC 服务

1. 在 `proto/` 目录下创建新的 `.proto` 文件
2. 定义服务接口和消息类型
3. 运行 `proto/generate.sh` 生成 Go 代码
4. 在 `grpc/services/` 目录下实现服务逻辑
5. 在 `grpc/server/server.go` 中注册新服务

### 添加新的拦截器

在 `grpc/interceptor/` 目录下创建新的拦截器文件，然后在 `grpc/server/server.go` 中注册。

## 部署注意事项

1. **防火墙配置**：确保 gRPC 端口（默认 9999）对外开放
2. **TLS 配置**：生产环境建议启用 TLS
3. **负载均衡**：可以使用 nginx 或 envoy 代理进行负载均衡
4. **监控**：建议添加 gRPC 相关的监控指标

## 常见问题

### Q: gRPC 服务无法启动
A: 检查端口是否被占用，确保配置文件中的端口设置正确。

### Q: 客户端连接失败
A: 确认服务器地址和端口，检查网络连接和防火墙设置。

### Q: 认证失败
A: 确认用户名密码正确，检查 JWT token 是否正确携带在请求头中。

### Q: proto 文件修改后不生效
A: 重新运行 `proto/generate.sh` 生成代码，然后重启服务器。

## 性能优化

1. **连接池**：客户端使用连接池复用连接
2. **批量请求**：合并多个小请求为批量请求
3. **流式处理**：对于大量数据传输使用 streaming RPC
4. **压缩**：启用 gzip 压缩减少网络传输

## 安全建议

1. **启用 TLS**：生产环境必须使用 TLS 加密
2. **token 过期**：设置合理的 JWT token 过期时间
3. **权限控制**：实现细粒度的权限控制
4. **日志审计**：记录重要操作的审计日志

---

更多详细信息请参考官方文档：
- [gRPC Go 官方文档](https://grpc.io/docs/languages/go/)
- [Protocol Buffers 文档](https://developers.google.com/protocol-buffers) 
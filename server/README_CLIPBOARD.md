# Rapido 剪贴板功能

## 功能概述

Rapido 剪贴板是一个通过企业微信客服实现手机和电脑多端联动的剪贴板工具。用户可以通过微信发送各种类型的内容（文字、图片、视频、语音等）到企业微信客服，系统会自动保存到个人剪贴板中，实现轻量级的多端内容同步。

## 主要功能

### 1. 基础剪贴板功能
- ✅ 创建剪贴板条目
- ✅ 查看剪贴板列表（支持分页、筛选、搜索）
- ✅ 更新剪贴板条目（标题、内容、读取状态、收藏状态）
- ✅ 删除剪贴板条目
- ✅ 清空剪贴板
- ✅ 剪贴板统计信息

### 2. 微信集成功能
- ✅ 微信OAuth授权登录
- ✅ 微信用户绑定系统账号
- ✅ 微信消息webhook接收
- ✅ 支持多种消息类型：
  - 文字消息
  - 图片消息
  - 语音消息（支持语音识别）
  - 视频消息
  - 小视频消息
  - 位置信息
  - 链接分享

### 3. 用户配置
- ✅ 最大条目数量限制
- ✅ 自动清理天数设置
- ✅ 文件类型限制
- ✅ 文件大小限制
- ✅ 微信功能开关
- ✅ 通知功能开关

## API 接口

### 剪贴板管理接口
- `POST /api/clipboard/item` - 创建剪贴板条目
- `GET /api/clipboard/itemList` - 获取剪贴板条目列表
- `GET /api/clipboard/item/:id` - 获取单个剪贴板条目
- `PUT /api/clipboard/item` - 更新剪贴板条目
- `DELETE /api/clipboard/item/:id` - 删除剪贴板条目
- `POST /api/clipboard/clear` - 清空剪贴板
- `GET /api/clipboard/config` - 获取用户配置
- `PUT /api/clipboard/config` - 更新用户配置
- `GET /api/clipboard/stats` - 获取剪贴板统计信息

### 微信集成接口
- `GET /api/clipboard/wechat/oauth/url` - 获取微信OAuth授权URL
- `GET /api/clipboard/wechat/oauth/callback` - 处理微信OAuth回调
- `POST /api/clipboard/wechat/bind` - 绑定微信用户到系统用户
- `GET /api/clipboard/wechat/user` - 获取微信用户信息
- `ANY /api/clipboard/wechat/webhook` - 微信消息webhook（不需要认证）

## 配置说明

### 1. 微信配置 (config.yaml)
```yaml
wechat:
    app-id: ""  # 微信公众号/小程序 AppID
    app-secret: ""  # 微信公众号/小程序 AppSecret
    token: "rapido_clipboard_token"  # 微信公众号 Token
    encoding-aes-key: ""  # 微信公众号 EncodingAESKey
    redirect-uri: "https://mcpcn.cc/api/clipboard/wechat/oauth/callback"  # OAuth回调地址
```

### 2. 剪贴板配置 (config.yaml)
```yaml
clipboard:
    max-file-size: 52428800  # 最大文件大小 50MB
    allowed-file-types: "image,video,audio,document"  # 允许的文件类型
    auto-clean-days: 30  # 自动清理天数
    enable-notification: true  # 是否启用通知
```

## 数据库表结构

### 1. clipboard_items - 剪贴板条目表
- `id` - 主键
- `user_id` - 用户ID
- `title` - 标题
- `content` - 内容
- `content_type` - 内容类型（text, image, video, audio, file）
- `file_url` - 文件URL
- `file_size` - 文件大小
- `source` - 来源（wechat, web, api）
- `from_user_id` - 发送者微信ID
- `msg_id` - 消息ID
- `is_read` - 是否已读
- `is_starred` - 是否收藏
- `expire_at` - 过期时间
- `created_at` - 创建时间
- `updated_at` - 更新时间

### 2. wechat_users - 微信用户表
- `id` - 主键
- `open_id` - 微信OpenID
- `union_id` - 微信UnionID
- `nickname` - 昵称
- `avatar` - 头像
- `user_id` - 关联的系统用户ID
- `is_active` - 是否激活
- `created_at` - 创建时间
- `updated_at` - 更新时间

### 3. clipboard_configs - 剪贴板配置表
- `id` - 主键
- `user_id` - 用户ID
- `max_items` - 最大条目数
- `auto_clean_days` - 自动清理天数
- `enable_wechat` - 启用微信
- `enable_notify` - 启用通知
- `allow_file_types` - 允许的文件类型
- `max_file_size` - 最大文件大小
- `created_at` - 创建时间
- `updated_at` - 更新时间

## 使用流程

### 1. 配置企业微信客服
1. 在企业微信管理后台创建客服应用
2. 配置客服应用的基本信息
3. 设置消息接收URL为: `https://你的域名/api/clipboard/wechat/webhook`
4. 配置Token和EncodingAESKey

### 2. 用户授权绑定
1. 用户访问系统获取OAuth授权URL
2. 用户在微信中授权
3. 系统接收授权回调，创建或更新微信用户信息
4. 用户绑定微信账号到系统账号

### 3. 使用剪贴板
1. 用户在微信中向客服发送消息
2. 系统接收webhook消息
3. 解析消息内容并保存到剪贴板
4. 返回确认消息给用户
5. 用户可在网页端查看和管理剪贴板内容

## 技术特点

- **多端同步**: 通过微信实现手机与电脑的无缝内容同步
- **类型支持**: 支持文字、图片、语音、视频等多种内容类型
- **安全可靠**: 基于OAuth2.0的安全授权机制
- **配置灵活**: 支持用户自定义配置存储限制和清理策略
- **轻量级**: 无需安装客户端，通过微信即可使用

## 注意事项

1. 微信媒体文件下载功能需要完整实现（当前为占位符）
2. 建议配置文件上传大小限制，避免存储空间浪费
3. 定期清理过期内容，保持系统性能
4. 确保webhook URL的可访问性和安全性
5. 生产环境中需要配置实际的微信AppID和AppSecret 
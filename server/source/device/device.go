package device

import (
	"context"

	deviceModel "github.com/flipped-aurora/gin-vue-admin/server/model/device"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"gorm.io/gorm"
)

const initOrderDevice = system.InitOrderSystem + 10

type initDevice struct{}

// auto run
func init() {
	system.RegisterInit(initOrderDevice, &initDevice{})
}

func (i *initDevice) InitializerName() string {
	return deviceModel.Device{}.TableName()
}

func (i *initDevice) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&deviceModel.Device{}, &deviceModel.DeviceReport{})
}

func (i *initDevice) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&deviceModel.Device{}) && db.Migrator().HasTable(&deviceModel.DeviceReport{})
}

func (i *initDevice) InitializeData(ctx context.Context) (next context.Context, err error) {
	// No initial data needed for device tables
	return ctx, nil
}

func (i *initDevice) DataInserted(ctx context.Context) bool {
	// Always return true since we don't insert initial data
	return true
}

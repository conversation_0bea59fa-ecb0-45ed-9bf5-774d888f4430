package llm

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type LLMRouter struct{}

func (s *LLMRouter) InitLLMRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	llmGroup := Router.Group("llm").Use(middleware.OperationRecord())
	llmGroupWithoutAuth := PublicRouter.Group("llm")
	{
		llmGroupWithoutAuth.POST("/free-chat-stream", llmApi.FreeChatStream)
	}
	{
		llmGroup.POST("/chat-stream", llmApi.ChatStream)
	}
}

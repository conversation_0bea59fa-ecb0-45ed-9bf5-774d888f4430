package websocket

import (
	"github.com/gin-gonic/gin"
)

type WebSocketRouter struct{}

// InitWebSocketRouter 初始化WebSocket路由
func (w *WebSocketRouter) InitWebSocketRouter(Router *gin.RouterGroup) {
	websocketRouter := Router.Group("websocket")
	{
		websocketRouter.GET("", webSocketApi.HandleWebSocket)                   // WebSocket连接
		websocketRouter.GET("users", webSocketApi.GetConnectedUsers)            // 获取连接用户
		websocketRouter.GET("check", webSocketApi.CheckUserConnection)          // 检查用户连接状态
		websocketRouter.GET("kf-status", webSocketApi.CheckKfConnectionStatus)  // 检查企业微信客服连接状态
		websocketRouter.GET("devices", webSocketApi.GetUserConnectedDevices)    // 获取用户已连接的设备列表
		websocketRouter.GET("device/check", webSocketApi.CheckDeviceConnection) // 检查设备连接状态
	}
}

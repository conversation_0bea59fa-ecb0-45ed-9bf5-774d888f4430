package device

import (
	"github.com/gin-gonic/gin"
)

type DeviceRouter struct{}

// InitDeviceRouter 初始化设备路由
func (s *DeviceRouter) InitDeviceRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) (R gin.IRoutes) {
	// 私有路由组（需要认证）
	deviceRouterWithoutRecord := Router.Group("device")

	// 公共路由组（不需要认证）
	devicePublicRouter := PublicRouter.Group("device")

	{
		// 需要认证但不需要操作记录的路由
		deviceRouterWithoutRecord.POST("list", deviceApi.GetDeviceList)                // 获取设备列表
		deviceRouterWithoutRecord.GET("detail", deviceApi.GetDeviceDetail)             // 获取设备详情
		deviceRouterWithoutRecord.GET("myDevices", deviceApi.GetMyDevices)             // 获取我的设备列表（不分页）
		deviceRouterWithoutRecord.POST("report/list", deviceApi.GetDeviceReportList)   // 获取设备上报记录列表
		deviceRouterWithoutRecord.GET("assignAgent", deviceApi.AssignDefaultAgent)     // 获取mcp接入点地址
		deviceRouterWithoutRecord.GET("ipLocation", deviceApi.GetIpLocation)           // 获取IP归属地
		deviceRouterWithoutRecord.POST("updateDeviceName", deviceApi.UpdateDeviceName) // 修改设备名称
		deviceRouterWithoutRecord.POST("deviceLogout", deviceApi.DeviceLogout)         // 指定设备登出
	}
	{
		// 公共路由（不需要认证）
		devicePublicRouter.POST("report", deviceApi.ReportDevice)                 // 设备信息上报
		devicePublicRouter.GET("getIpLocation", deviceApi.GetIpLocation)          // 获取IP归属地
		devicePublicRouter.POST("updateDeviceRedis", deviceApi.UpdateDeviceRedis) // 跟新设备信息缓存
	}

	return deviceRouterWithoutRecord
}

package device

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type DeviceVersionRecordRouter struct{}

// InitDeviceVersionRecordRouter 初始化 设备版本更新记录 路由信息
func (s *DeviceVersionRecordRouter) InitDeviceVersionRecordRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	deviceVersionRecordRouter := Router.Group("deviceVersionRecord").Use(middleware.OperationRecord())
	deviceVersionRecordRouterWithoutRecord := Router.Group("deviceVersionRecord")
	deviceVersionRecordRouterWithoutAuth := PublicRouter.Group("deviceVersionRecord")
	{
		deviceVersionRecordRouter.POST("recordUpdate", deviceVersionRecordApi.RecordUpdate)                             // 记录设备版本更新
		deviceVersionRecordRouter.PUT("updateDeviceVersionRecord", deviceVersionRecordApi.UpdateDeviceVersionRecord)    // 更新设备版本更新记录
		deviceVersionRecordRouter.DELETE("deleteDeviceVersionRecord", deviceVersionRecordApi.DeleteDeviceVersionRecord) // 删除设备版本更新记录
	}
	{
		deviceVersionRecordRouterWithoutRecord.GET("findDeviceVersionRecord", deviceVersionRecordApi.FindDeviceVersionRecord)       // 根据ID获取设备版本更新记录
		deviceVersionRecordRouterWithoutRecord.GET("getDeviceVersionRecordList", deviceVersionRecordApi.GetDeviceVersionRecordList) // 获取设备版本更新记录列表
	}
	{
		deviceVersionRecordRouterWithoutAuth.POST("checkUpdate", deviceVersionRecordApi.CheckUpdate) // 检查设备版本更新（公开接口）
	}
}

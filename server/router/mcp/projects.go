package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProjectsRouter struct{}

// InitProjectsRouter 初始化 projects表 路由信息
func (s *ProjectsRouter) InitProjectsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	projectsRouter := Router.Group("projects").Use(middleware.OperationRecord())
	projectsRouterWithoutRecord := Router.Group("projects")
	projectsRouterWithoutAuth := PublicRouter.Group("projects")

	{
		projectsRouter.POST("createProjects", projectsApi.CreateProjects)                  // 新建projects表
		projectsRouter.DELETE("deleteProjects", projectsApi.DeleteProjects)                // 删除projects表
		projectsRouter.DELETE("deleteProjectsByIds", projectsApi.DeleteProjectsByIds)      // 批量删除projects表
		projectsRouter.PUT("updateProjects", projectsApi.UpdateProjects)                   // 更新projects表
		projectsRouterWithoutAuth.GET("getProjectsList", projectsApi.GetAdminProjectsList) // 管理端获取projects表列表

		// 新增：提交mcp和我的项目接口
		//projectsRouter.POST("submitMcp", projectsApi.SubmitMcp)     // 提交mcp
		projectsRouter.GET("myProjects", projectsApi.GetMyProjects) // 我的项目

		// UUID相关路由
		projectsRouter.POST("getProjectList", projectsApi.GetProjectList)                   // 获取项目列表
		projectsRouter.GET("findProjectByUUID/:uuid", projectsApi.FindProjectByUUID)        // 获取项目详情
		projectsRouter.POST("createProjectWithUUID", projectsApi.CreateProjectWithUUID)     // 创建项目
		projectsRouter.PUT("updateProjectByUUID", projectsApi.UpdateProjectByUUID)          // 更新项目
		projectsRouter.DELETE("deleteProjectByUUID/:uuid", projectsApi.DeleteProjectByUUID) // 删除项目
	}

	{
		projectsRouterWithoutRecord.POST("findProjects", projectsApi.FindProjects) // 根据ID获取projects表
	}

	{
		// 计数相关路由 - 放在公共路由组中
		projectsRouterWithoutAuth.POST("incrementOnlineUsageCount/:projectID", projectsApi.IncrementOnlineUsageCount)   // 增加在线使用次数
		projectsRouterWithoutAuth.POST("incrementOfflineUsageCount/:projectID", projectsApi.IncrementOfflineUsageCount) // 增加离线使用次数
		projectsRouterWithoutAuth.POST("likeProject/:projectID", projectsApi.LikeProject)                               // 点赞项目
		projectsRouterWithoutAuth.POST("unlikeProject/:projectID", projectsApi.UnlikeProject)                           // 取消点赞项目
		projectsRouterWithoutAuth.POST("getProjectsList", projectsApi.GetProjectList)                                   // 获取projects表列表
		projectsRouterWithoutAuth.POST("findProjectByUUID/:uuid", projectsApi.FindProjectByUUID)                        // 获取项目详情
		projectsRouterWithoutAuth.GET("findProjects", projectsApi.FindProjects)                                         // 根据ID获取projects表
		projectsRouterWithoutAuth.GET("isLiked", projectsApi.IsProjectLiked)                                            // 查询当前用户是否点赞
		projectsRouterWithoutAuth.POST("testMCPMonitor", projectsApi.TestMCPMonitor)                                    // 手动测试 MCP 连接监控（无需认证）
		projectsRouterWithoutAuth.POST("projectsSearch", projectsApi.ProjectsSearch)                                    // 工具库查询
		projectsRouterWithoutAuth.POST("createProjectsSimple", projectsApi.CreateProjectsSimple)                        // 简化创建项目（无需认证）
		projectsRouterWithoutAuth.POST("updateAllCapabilities", projectsApi.UpdateAllProjectsCapabilities)              // 简化创建项目（无需认证）
	}
}

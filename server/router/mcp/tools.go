package mcp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ToolsRouter struct{}

// InitToolsRouter 初始化工具路由
func (s *ToolsRouter) InitToolsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	toolsRouter := Router.Group("tools").Use(middleware.OperationRecord())
	toolsRouterWithoutAuth := PublicRouter.Group("tools")
	toolsApi := v1.ApiGroupApp.McpApiGroup.ToolsApi
	{
		toolsRouter.GET("list", toolsApi.GetTools) // 获取工具列表(需要认证)
	}
	{
		toolsRouterWithoutAuth.GET("public/list", toolsApi.GetTools) // 获取工具列表(公开访问)
	}
}

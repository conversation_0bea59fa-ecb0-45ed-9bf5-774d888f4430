package mcp

import (
	"github.com/gin-gonic/gin"
)

type ProjectToolsRouter struct{}

// InitProjectToolsRouter 初始化 projectTools表 路由信息
func (p *ProjectToolsRouter) InitProjectToolsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	// projectToolsRouter := Router.Group("projectTools").Use(middleware.OperationRecord())
	projectToolsRouter := Router.Group("projectTools")
	// projectToolsRouterWithoutRecord := Router.Group("projectTools")
	projectToolsRouterWithoutAuth := PublicRouter.Group("projectTools")
	// 所有接口都不加权限
	{
		projectToolsRouter.POST("createProjectTools", projectToolsApi.CreateProjectTools)             // 新建ProjectTools
		projectToolsRouter.DELETE("deleteProjectTools", projectToolsApi.DeleteProjectTools)           // 删除ProjectTools
		projectToolsRouter.DELETE("deleteProjectToolsByIds", projectToolsApi.DeleteProjectToolsByIds) // 批量删除ProjectTools
		projectToolsRouter.PUT("updateProjectTools", projectToolsApi.UpdateProjectTools)              // 更新ProjectTools

	}
	{
		projectToolsRouterWithoutAuth.GET("findProjectTools", projectToolsApi.FindProjectTools)                   // 根据ID获取ProjectTools
		projectToolsRouterWithoutAuth.GET("getProjectToolsList", projectToolsApi.GetProjectToolsList)             // 获取ProjectTools列表
		projectToolsRouterWithoutAuth.GET("getProjectToolsDataSource", projectToolsApi.GetProjectToolsDataSource) // 获取ProjectTools数据源
		projectToolsRouterWithoutAuth.POST("getAllProjectToolsList", projectToolsApi.GetAllProjectToolsList)      // 提供给向量检索服务，Embedding所有工具
		projectToolsRouterWithoutAuth.GET("getToolPoints", projectToolsApi.GetToolPointsByProjectUUIDAndName)     // 根据projectId和name查询工具积分Points

	}
}

package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type McpClientRouter struct{}

// InitMcpClientRouter 初始化 mcp客户端 路由信息
func (s *McpClientRouter) InitMcpClientRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	mcpClientRouter := Router.Group("mcpClient").Use(middleware.OperationRecord())
	mcpClientRouterWithoutRecord := Router.Group("mcpClient")
	mcpClientRouterWithoutAuth := PublicRouter.Group("mcpClient")
	{
		mcpClientRouter.POST("createMcpClient", mcpClientApi.CreateMcpClient)                           // 新建mcp客户端
		mcpClientRouter.DELETE("deleteMcpClient", mcpClientApi.DeleteMcpClient)                         // 删除mcp客户端
		mcpClientRouter.DELETE("deleteMcpClientByIds", mcpClientApi.DeleteMcpClientByIds)               // 批量删除mcp客户端
		mcpClientRouter.PUT("updateMcpClient", mcpClientApi.UpdateMcpClient)                            // 更新mcp客户端
		mcpClientRouter.POST("updateMcpClientDownloadCount", mcpClientApi.UpdateMcpClientDownloadCount) // 更新下载次数
	}
	{
		mcpClientRouterWithoutRecord.GET("findMcpClient", mcpClientApi.FindMcpClient)       // 根据ID获取mcp客户端
		mcpClientRouterWithoutRecord.GET("getMcpClientList", mcpClientApi.GetMcpClientList) // 获取mcp客户端列表
	}
	{
		mcpClientRouterWithoutAuth.POST("getMcpClientPublic", mcpClientApi.GetMcpClientList) // mcp客户端开放接口
	}
}

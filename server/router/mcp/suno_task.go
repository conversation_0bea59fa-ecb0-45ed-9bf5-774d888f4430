package mcp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type SunoTaskRouter struct{}

// InitSunoTaskRouter 初始化 SunoTask 路由信息
func (s *SunoTaskRouter) InitSunoTaskRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	sunoTaskApi := v1.ApiGroupApp.McpApiGroup.SunoTaskApi

	// 公共路由（不需要登录）
	sunoTaskPublicRouter := PublicRouter.Group("suno")
	{
		sunoTaskPublicRouter.POST("callback", sunoTaskApi.SunoCallback)                     // 接收Suno回调（通用）
		sunoTaskPublicRouter.POST("callback/generate", sunoTaskApi.SunoCallbackGenerate)    // 接收Suno生成音乐回调
		sunoTaskPublicRouter.POST("callback/upload-cover", sunoTaskApi.SunoCallbackCover)   // 接收Suno上传并翻唱回调
		sunoTaskPublicRouter.POST("callback/upload-extend", sunoTaskApi.SunoCallbackExtend) // 接收Suno上传并扩展回调
		sunoTaskPublicRouter.GET("task/:taskId", sunoTaskApi.QueryTaskProgress)             // 查询任务进度
	}

	// 私有路由（需要登录）
	sunoTaskRouter := Router.Group("suno")
	{
		sunoTaskRouter.POST("task", sunoTaskApi.CreateSunoTask)                // 创建任务
		sunoTaskRouter.GET("tasks", sunoTaskApi.GetSunoTaskList)               // 获取任务列表
		sunoTaskRouter.GET("task/detail/:taskId", sunoTaskApi.GetSunoTaskByID) // 获取任务详情
	}
}

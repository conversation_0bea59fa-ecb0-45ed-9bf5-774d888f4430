package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UseCaseRouter struct{}

func (s *UseCaseRouter) InitUseCaseRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	useCaseRouter := Router.Group("useCase").Use(middleware.OperationRecord())
	useCaseRouterWithoutRecord := Router.Group("useCase")
	useCaseRouterWithoutAuth := PublicRouter.Group("useCase")
	{
		useCaseRouter.POST("createUseCase", useCaseApi.CreateUseCase)
		useCaseRouter.POST("approveUseCase", useCaseApi.ApproveUseCase) // 审核通过案例
	}
	{
		useCaseRouterWithoutRecord.GET("myUseCaseList", useCaseApi.MyUseCaseList)
	}
	{
		useCaseRouterWithoutAuth.GET("findUseCase", useCaseApi.FindUseCase)
		useCaseRouterWithoutAuth.POST("getUseCasePublic", useCaseApi.GetUseCaseList)
	}
}

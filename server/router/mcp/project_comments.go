package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProjectCommentsRouter struct{}

// InitProjectCommentsRouter 初始化项目评论路由
func (s *ProjectCommentsRouter) InitProjectCommentsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	projectCommentsRouter := Router.Group("projectComments").Use(middleware.OperationRecord())
	projectCommentsRouterWithoutRecord := Router.Group("projectComments")
	projectCommentsRouterWithoutAuth := PublicRouter.Group("projectComments")

	{
		// 需要认证的路由
		projectCommentsRouter.POST("createProjectComment", projectCommentsApi.CreateProjectComment)                   // 创建项目评论
		projectCommentsRouter.PUT("updateProjectComment", projectCommentsApi.UpdateProjectComment)                    // 更新项目评论
		projectCommentsRouter.DELETE("deleteProjectComment/:commentID", projectCommentsApi.DeleteProjectComment)      // 删除项目评论
		projectCommentsRouter.GET("checkCommentPermission/:projectID", projectCommentsApi.CheckUserCommentPermission) // 检查评论权限

		// 管理员审核功能
		projectCommentsRouter.POST("getPendingCommentsList", projectCommentsApi.GetPendingCommentsList) // 获取待审核评论列表
		projectCommentsRouter.POST("reviewComment", projectCommentsApi.ReviewComment)                   // 审核评论
	}

	{
		// 不需要操作记录的认证路由
		projectCommentsRouterWithoutRecord.GET("getProjectComment/:commentID", projectCommentsApi.GetProjectCommentByID) // 获取评论详情
	}

	{
		// 公共路由（无需认证）
		projectCommentsRouterWithoutAuth.POST("getProjectCommentsList", projectCommentsApi.GetProjectCommentsList)         // 获取项目评论列表
		projectCommentsRouterWithoutAuth.GET("getProjectRatingStats/:projectID", projectCommentsApi.GetProjectRatingStats) // 获取项目评分统计
	}
}

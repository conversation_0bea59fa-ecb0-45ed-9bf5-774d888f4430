package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ApiKeyRouter struct{}

func (s *ApiKeyRouter) InitApiKeyRouter(Router *gin.RouterGroup) {
	apiKeyRouter := Router.Group("apiKey").Use(middleware.OperationRecord())
	apiKeyRouterWithoutRecord := Router.Group("apiKey")
	{
		apiKeyRouter.POST("createApiKey", apiKeyApi.CreateApiKey)          // 创建API密钥
		apiKeyRouter.DELETE("deleteApiKey", apiKeyApi.DeleteApiKey)        // 删除API密钥
		apiKeyRouterWithoutRecord.POST("getApiKeys", apiKeyApi.GetApiKeys) // 获取API密钥列表
	}
}

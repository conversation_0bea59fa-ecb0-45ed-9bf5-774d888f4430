package mcp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type MapCoordinatesRouter struct{}

// InitMapCoordinatesRouter 初始化地图坐标相关路由
func (s *MapCoordinatesRouter) InitMapCoordinatesRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	mapCoordinatesApi := v1.ApiGroupApp.McpApiGroup.MapCoordinatesApi
	mapCoordinatesRouterWithoutAuth := PublicRouter.Group("map")
	{
		mapCoordinatesRouterWithoutAuth.POST("setCoordinates", mapCoordinatesApi.SetMapCoordinates)
		mapCoordinatesRouterWithoutAuth.GET("getCoordinates", mapCoordinatesApi.GetMapCoordinates)
		mapCoordinatesRouterWithoutAuth.POST("setString", mapCoordinatesApi.SetMapString)
		mapCoordinatesRouterWithoutAuth.GET("getString", mapCoordinatesApi.GetMapString)
	}
}

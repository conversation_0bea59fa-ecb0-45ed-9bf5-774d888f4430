package wecom

import (
	"github.com/gin-gonic/gin"
)

type WechatWorkRouter struct{}

// InitWechatWorkRouter 初始化企业微信路由
func (w *WechatWorkRouter) InitWechatWorkRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	productRouterWithoutAuth := PublicRouter.Group("wecom")
	{
		//productRouterWithoutAuth.GET("access_token", wechatWorkApi.GetAccessToken)
		productRouterWithoutAuth.GET("callback/event", wechatWorkApi.VerifyURL)
		productRouterWithoutAuth.POST("callback/event", wechatWorkApi.CallbackEvent)
	}
}

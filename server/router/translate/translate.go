package translate

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type TranslateRouter struct{}

func (s *TranslateRouter) InitTranslateRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	// 需要认证的翻译接口
	translateGroupWithAuth := Router.Group("translate").Use(middleware.OperationRecord())
	{
		translateGroupWithAuth.POST("/baiduTranslate", translateApi.TranslateHandler)
	}

	// 不需要认证的接口
	translateGroupWithoutAuth := PublicRouter.Group("translate")
	{
		translateGroupWithoutAuth.GET("/languages", translateApi.ListLanguagesHandler)
	}
}

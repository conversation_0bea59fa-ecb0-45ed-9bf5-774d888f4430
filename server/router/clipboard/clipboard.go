package clipboard

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ClipboardRouter struct{}

// InitClipboardRouter 初始化剪贴板路由
func (c *ClipboardRouter) InitClipboardRouter(Router *gin.RouterGroup) {
	clipboardRouter := Router.Group("clipboard").Use(middleware.OperationRecord())
	clipboardRouterWithoutRecord := Router.Group("clipboard")
	clipboardApi := v1.ApiGroupApp.ClipboardApiGroup.ClipboardApi
	{
		clipboardRouter.POST("item", clipboardApi.CreateClipboardItem)       // 创建剪贴板条目
		clipboardRouter.PUT("item", clipboardApi.UpdateClipboardItem)        // 更新剪贴板条目
		clipboardRouter.DELETE("item/:id", clipboardApi.DeleteClipboardItem) // 删除剪贴板条目
		clipboardRouter.POST("clear", clipboardApi.ClearClipboard)           // 清空剪贴板
		clipboardRouter.PUT("config", clipboardApi.UpdateUserConfig)         // 更新用户配置
	}
	{
		clipboardRouterWithoutRecord.GET("itemList", clipboardApi.GetClipboardItemList) // 获取剪贴板条目列表
		clipboardRouterWithoutRecord.GET("item/:id", clipboardApi.GetClipboardItem)     // 获取单个剪贴板条目
		clipboardRouterWithoutRecord.GET("config", clipboardApi.GetUserConfig)          // 获取用户配置
		clipboardRouterWithoutRecord.GET("stats", clipboardApi.GetClipboardStats)       // 获取剪贴板统计信息
	}
}

package authing

import (
	"github.com/gin-gonic/gin"
)

type AuthRouter struct{}

func (s *AuthRouter) InitAuthRouter(Router *gin.RouterGroup, PublicGroup *gin.RouterGroup) {
	//authRouter := Router.Group("auth").Use(middleware.OperationRecord())
	mcpClientRouterWithoutAuth := PublicGroup.Group("auth") // 创建 /auth 路由组
	{
		mcpClientRouterWithoutAuth.POST("registerByEmail", authApi.RegisterByEmail) // POST /auth/registerByEmail
		mcpClientRouterWithoutAuth.POST("loginByEmail", authApi.LoginByEmail)       // POST /auth/loginByEmail

		// 手机号相关路由
		mcpClientRouterWithoutAuth.POST("registerByPhone", authApi.RegisterByPhone)           // POST /auth/registerByPhone
		mcpClientRouterWithoutAuth.POST("loginByPhonePassword", authApi.LoginByPhonePassword) // POST /auth/loginByPhonePassword
		mcpClientRouterWithoutAuth.POST("loginByPhoneCode", authApi.LoginByPhoneCode)         // POST /auth/loginByPhoneCode
		mcpClientRouterWithoutAuth.POST("sendSmsCode", authApi.SendSmsCode)                   // POST /auth/sendSmsCode

		// 用户名相关路由
		mcpClientRouterWithoutAuth.POST("registerByUsername", authApi.RegisterByUsername) // POST /auth/RegisterByUsername
		mcpClientRouterWithoutAuth.POST("loginByUsername", authApi.LoginByUsername)       // POST /auth/LoginByUsername

		// Authing 托管登录回调地址
		mcpClientRouterWithoutAuth.GET("handleAuthingCallback", authApi.HandleAuthingCallback) // GET /auth/callback
		//Authing 嵌入登录根据token登录
		mcpClientRouterWithoutAuth.GET("loginByUserInfo", authApi.LoginByUserInfo) // GET /auth/loginByUserInfo
	}

}

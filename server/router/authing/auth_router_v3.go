package authing

import (
	"github.com/gin-gonic/gin"
)

type AuthV3Router struct{}

// InitAuthingV3Router 初始化 V3 Authing 路由
func (s *AuthV3Router) InitAuthingV3Router(Router *gin.RouterGroup, PublicGroup *gin.RouterGroup) {
	// 创建 /v3/auth 路由组
	authV3Router := Router.Group("auth/v3")
	mcpClientRouterWithoutAuth := PublicGroup.Group("auth/v3") // 创建 /auth 路由组
	{
		// 注册路由
		mcpClientRouterWithoutAuth.POST("register", authV3Api.Register) // POST /auth/v3/register
		// 可以在这里添加 V3 的其他路由，例如登录等
		// authV3Router.POST("login", V3ApiGroup.AuthApi.Login) // 示例
	}

	{
		authV3Router.POST("bindPhone", authV3Api.BindPhone)           // POST /auth/v3/bindPhone
		authV3Router.POST("changePassword", authV3Api.UpdatePassword) // POST /auth/v3/changePassword

		authV3Router.POST("verifyUpdatePhoneRequest", authV3Api.VerifyUpdatePhoneRequest) // POST /auth/v3/VerifyUpdatePhoneRequest
		authV3Router.POST("updatePhone", authV3Api.UpdatePhone)                           // POST /auth/v3/updatePhone
	}
}

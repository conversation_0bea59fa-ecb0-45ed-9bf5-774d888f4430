package pansou

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

// SearchRouter 搜索路由
type SearchRouter struct{}

// InitSearchRouter 初始化搜索路由
func (s *SearchRouter) InitSearchRouter(Router *gin.RouterGroup) {
	pansouRouter := Router.Group("pansou")

	// 直接获取API实例（服务实例将在系统启动时设置）
	pansouApiV1 := v1.ApiGroupApp.PanSouApiGroup.GetSearchApi()
	{
		pansouRouter.POST("search", pansouApiV1.SearchHandler) // 搜索接口 - POST
		pansouRouter.GET("search", pansouApiV1.SearchHandler)  // 搜索接口 - GET
		pansouRouter.GET("health", pansouApiV1.HealthHandler)  // 健康检查接口
	}
}

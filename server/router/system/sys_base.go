package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type BaseRouter struct{}

func (s *BaseRouter) InitBaseRouter(Router *gin.RouterGroup) (R gin.IRoutes) {
	baseRouter := Router.Group("base")
	{
		baseRouter.POST("register", baseApi.Register)
		baseRouter.POST("login", baseApi.Login)
		baseRouter.POST("captcha", baseApi.Captcha)
		baseRouter.POST("unified-login", authProviderApi.UnifiedLogin)
		baseRouter.POST("phoneRegister", baseApi.PhoneRegister)
		// 为短信验证码接口添加专门的限流中间件
		baseRouter.POST("sendSMSCode", middleware.DefaultSmsLimit(), baseApi.SendSMSCode)
		baseRouter.POST("bindWechatPhone", authProviderApi.BindWechatPhone)
	}
	return baseRouter
}

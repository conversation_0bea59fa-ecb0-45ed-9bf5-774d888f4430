package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type SysVersionRouter struct{}

// InitSysVersionRouter 初始化 系统版本 路由信息
func (s *SysVersionRouter) InitSysVersionRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	sysVersionRouter := Router.Group("sysVersion").Use(middleware.OperationRecord())
	sysVersionRouterWithoutRecord := Router.Group("sysVersion")
	sysVersionRouterWithoutAuth := PublicRouter.Group("sysVersion")
	{
		sysVersionRouter.POST("createSysVersion", sysVersionApi.CreateSysVersion)             // 新建系统版本
		sysVersionRouter.DELETE("deleteSysVersion", sysVersionApi.DeleteSysVersion)           // 删除系统版本
		sysVersionRouter.DELETE("deleteSysVersionByIds", sysVersionApi.DeleteSysVersionByIds) // 批量删除系统版本
		sysVersionRouter.PUT("updateSysVersion", sysVersionApi.UpdateSysVersion)              // 更新系统版本
	}
	{
		sysVersionRouterWithoutRecord.GET("findSysVersion", sysVersionApi.FindSysVersion)       // 根据ID获取系统版本
		sysVersionRouterWithoutRecord.GET("getSysVersionList", sysVersionApi.GetSysVersionList) // 获取系统版本列表
	}
	{
		sysVersionRouterWithoutAuth.GET("getSysVersionPublic", sysVersionApi.GetSysVersionPublic) // 系统版本开放接口
	}
}

package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserGeneralSettingsRouter struct{}

// InitUserGeneralSettingsRouter 初始化用户通用设置路由信息
func (s *UserGeneralSettingsRouter) InitUserGeneralSettingsRouter(Router *gin.RouterGroup) {
	userGeneralSettingsRouter := Router.Group("userGeneralSettings").Use(middleware.OperationRecord())
	userGeneralSettingsRouterWithoutRecord := Router.Group("userGeneralSettings")
	userGeneralSettingsApi := v1.ApiGroupApp.SystemApiGroup.UserGeneralSettingsApi
	{
		userGeneralSettingsRouter.POST("createUserGeneralSettings", userGeneralSettingsApi.CreateUserGeneralSettings)             // 新建用户通用设置
		userGeneralSettingsRouter.DELETE("deleteUserGeneralSettings", userGeneralSettingsApi.DeleteUserGeneralSettings)           // 删除用户通用设置
		userGeneralSettingsRouter.DELETE("deleteUserGeneralSettingsByIds", userGeneralSettingsApi.DeleteUserGeneralSettingsByIds) // 批量删除用户通用设置
		userGeneralSettingsRouter.PUT("updateUserGeneralSettings", userGeneralSettingsApi.UpdateUserGeneralSettings)              // 更新用户通用设置
	}
	{
		userGeneralSettingsRouterWithoutRecord.GET("findUserGeneralSettings", userGeneralSettingsApi.FindUserGeneralSettings)       // 根据ID获取用户通用设置
		userGeneralSettingsRouterWithoutRecord.GET("getUserGeneralSettingsList", userGeneralSettingsApi.GetUserGeneralSettingsList) // 获取用户通用设置列表
	}
}

// InitDesktopSettingsRouter 初始化桌面设置路由信息
func (s *UserGeneralSettingsRouter) InitDesktopSettingsRouter(Router *gin.RouterGroup) {
	desktopRouter := Router.Group("desktop").Use(middleware.OperationRecord())
	desktopRouterWithoutRecord := Router.Group("desktop")
	userGeneralSettingsApi := v1.ApiGroupApp.SystemApiGroup.UserGeneralSettingsApi
	{
		desktopRouter.PUT("settings", userGeneralSettingsApi.UpdateMyGeneralSettings) // 更新当前用户设置
		desktopRouter.PUT("permissions", userGeneralSettingsApi.UpdateMyPermissions)  // 更新当前用户权限状态
	}
	{
		desktopRouterWithoutRecord.GET("settings", userGeneralSettingsApi.GetMyGeneralSettings)     // 获取当前用户设置
		desktopRouterWithoutRecord.GET("options", userGeneralSettingsApi.GetGeneralSettingsOptions) // 获取设置选项
	}
}

package system

import (
	"github.com/gin-gonic/gin"
)

type WechatOAuthRouter struct{}

// InitWechatOAuthRouter 初始化微信OAuth路由
func (w *WechatOAuthRouter) InitWechatOAuthRouter(PublicRouter *gin.RouterGroup) {

	// 公开路由 - 不需要认证
	wechatOAuthPublicRouter := PublicRouter.Group("wechat")
	{
		wechatOAuthPublicRouter.GET("wechatConfig", wechatOAuthApi.GetWechatConfig) // 获取网站应用AppId等信息
		wechatOAuthPublicRouter.GET("callback", wechatOAuthApi.WechatCallback)      // 微信授权回调
		wechatOAuthPublicRouter.GET("pollCode", wechatOAuthApi.PollWechatCode)      // 轮询获取授权code
	}
}

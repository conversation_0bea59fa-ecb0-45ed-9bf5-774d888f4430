package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserSessionRouter struct{}

// InitUserSessionRouter 初始化用户会话管理路由信息
func (s *UserSessionRouter) InitUserSessionRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	sessionRouter := Router.Group("userSession").Use(middleware.OperationRecord())
	sessionRouterWithoutAuth := PublicRouter.Group("userSession")
	{
		sessionRouter.GET("sessions", userSessionApi.GetMySessions)                           // 获取用户会话列表
		sessionRouter.DELETE("sessions/logout", userSessionApi.LogoutSession)                 // 注销指定会话
		sessionRouter.DELETE("sessions/logout-others", userSessionApi.LogoutAllOtherSessions) // 注销其他所有会话
		sessionRouter.GET("sessions/statistics", userSessionApi.GetSessionStatistics)         // 获取会话统计
	}
	{
		sessionRouterWithoutAuth.POST("refreshToken", userSessionApi.RefreshToken) // 刷新Token
	}
}

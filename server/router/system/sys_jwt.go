package system

import (
	"github.com/gin-gonic/gin"
)

type JwtRouter struct{}

func (s *JwtRouter) InitJwtRouter(PrivateRouter *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	// 需要认证的JWT路由
	jwtPrivateRouter := PrivateRouter.Group("jwt")
	{
		jwtPrivateRouter.POST("jsonInBlacklist", jwtApi.JsonInBlacklist) // jwt加入黑名单

	}

	// 不需要认证的JWT路由
	jwtPublicRouter := PublicRouter.Group("jwt")
	{
		jwtPublicRouter.POST("verify", jwtApi.VerifyToken)      // 验证token是否有效
		jwtPublicRouter.POST("expireToken", jwtApi.ExpireToken) // 测试用，使指定token过期
	}
}

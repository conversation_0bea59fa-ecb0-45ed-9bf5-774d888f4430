package system

import (
	"github.com/gin-gonic/gin"
)

type AuthProviderRouter struct{}

// InitAuthProviderRouter 初始化认证提供商路由
func (r *AuthProviderRouter) InitAuthProviderRouter(Router *gin.RouterGroup, RouterPublic *gin.RouterGroup) {

	authProviderRouter := Router.Group("auth")
	publicAuthProviderRouter := RouterPublic.Group("auth")
	// 公开路由（不需要认证）
	{
		publicAuthProviderRouter.POST("check-provider", authProviderApi.CheckProviderExist)        // 检查认证提供商是否存在
		publicAuthProviderRouter.GET("supported-providers", authProviderApi.GetSupportedProviders) // 获取支持的认证提供商列表
		publicAuthProviderRouter.POST("send-code", authProviderApi.SendVerificationCode)           // 发送验证码
	}

	// 需要认证的路由
	{
		authProviderRouter.POST("bind-provider", authProviderApi.BindProvider)            // 绑定认证提供商
		authProviderRouter.POST("unbind-provider", authProviderApi.UnbindProvider)        // 解绑认证提供商
		authProviderRouter.GET("providers", authProviderApi.GetUserProviders)             // 获取用户认证提供商列表
		authProviderRouter.POST("merge-accounts", authProviderApi.MergeAccounts)          // 合并账号
		authProviderRouter.GET("accounts-for-merge", authProviderApi.GetAccountsForMerge) // 获取可合并的账号列表
	}
}

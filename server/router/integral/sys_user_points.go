package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type SysUserPointsRouter struct{}

// InitSysUserPointsRouter 初始化积分路由信息
func (s *SysUserPointsRouter) InitSysUserPointsRouter(Router *gin.RouterGroup) {
	userPointsRouterWithoutRecord := Router.Group("userPoints")
	var userPointsApi = v1.ApiGroupApp.IntegralApiGroup.SysUserPointsApi
	{
		userPointsRouterWithoutRecord.GET("getPointsRecords", userPointsApi.GetUserPointsRecords) // 获取用户积分记录
	}
}

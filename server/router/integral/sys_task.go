package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type SysTaskRouter struct{}

// InitSysTaskRouter 初始化任务路由信息
func (s *SysTaskRouter) InitSysTaskRouter(Router *gin.RouterGroup) {
	taskRouter := Router.Group("task").Use(middleware.OperationRecord())
	taskRouterWithoutRecord := Router.Group("task")
	var taskApi = v1.ApiGroupApp.IntegralApiGroup.TaskApi
	{
		taskRouter.POST("completeTask", taskApi.CompleteTask) // 完成任务
	}
	{
		taskRouterWithoutRecord.GET("getTaskList", taskApi.GetTaskList) // 获取任务列表
		taskRouterWithoutRecord.GET("getTaskById", taskApi.GetTaskById) // 根据ID获取任务详情
	}
}

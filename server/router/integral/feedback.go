package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type FeedbackRouter struct{}

// InitFeedbackRouter 初始化反馈路由信息
func (s *FeedbackRouter) InitFeedbackRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	feedbackRouter := Router.Group("feedback").Use(middleware.OperationRecord())
	feedbackRouterWithoutRecord := Router.Group("feedback")

	feedbackApi := v1.ApiGroupApp.IntegralApiGroup.FeedbackApi

	{
		// 需要记录操作的路由
		feedbackRouter.POST("create", feedbackApi.CreateFeedback)       // 创建反馈
		feedbackRouter.PUT("update", feedbackApi.UpdateFeedback)        // 更新反馈
		feedbackRouter.DELETE("delete/:id", feedbackApi.DeleteFeedback) // 删除反馈
		feedbackRouter.POST("review", feedbackApi.ReviewFeedback)       // 审核反馈
		feedbackRouter.POST("like", feedbackApi.LikeFeedback)           // 点赞反馈
	}

	{
		// 不需要记录操作的路由
		feedbackRouterWithoutRecord.GET("getUserList", feedbackApi.GetUserFeedbackList)      // 获取用户自己的反馈列表
		feedbackRouterWithoutRecord.GET("getPublicList", feedbackApi.GetPublicFeedbackList)  // 获取公开的反馈列表
		feedbackRouterWithoutRecord.GET("getAdminList", feedbackApi.GetAdminFeedbackList)    // 获取管理员反馈列表
		feedbackRouterWithoutRecord.GET("find/:id", feedbackApi.GetFeedbackByID)             // 根据ID获取反馈详情
		feedbackRouterWithoutRecord.GET("stats", feedbackApi.GetFeedbackStats)               // 获取反馈统计信息
		feedbackRouterWithoutRecord.GET("feedbackTaskList", feedbackApi.GetFeedbackTaskList) // 获取当前用户的反馈任务完成列表
	}

	{
		// 公开路由（无需登录）
		// 暂时没有公开的反馈路由
	}
}

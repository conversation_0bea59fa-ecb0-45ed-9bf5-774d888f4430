package product

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MembershipRouter struct{}

func (m *MembershipRouter) InitMembershipRouter(Router *gin.RouterGroup) {
	membershipRouter := Router.Group("membership").Use(middleware.OperationRecord())
	membershipRouterWithoutRecord := Router.Group("membership")

	//membershipApi := v1.ApiGroupApp.ProductApiGroup.MembershipApi
	{
		membershipRouter.POST("admin/grant-points", membershipApi.GrantMonthlyPoints)         // 手动发放积分
		membershipRouter.POST("admin/update-expired", membershipApi.UpdateExpiredMemberships) // 更新过期会员
	}
	{
		membershipRouterWithoutRecord.GET("status", membershipApi.GetUserMembershipStatus)  // 获取用户会员状态
		membershipRouterWithoutRecord.GET("list", membershipApi.GetUserMembershipList)      // 获取用户会员列表
		membershipRouterWithoutRecord.GET("admin/list", membershipApi.GetAllMembershipList) // 管理员获取所有会员列表
	}
}

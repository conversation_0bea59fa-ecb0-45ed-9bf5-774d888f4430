package product

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProductRouter struct{}

func (p *ProductRouter) InitProductRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	productRouter := Router.Group("product").Use(middleware.OperationRecord())
	productRouterWithoutRecord := Router.Group("product")
	productRouterWithoutAuth := PublicRouter.Group("product")
	{
		productRouter.POST("create", productApi.CreateProduct)        // 创建商品
		productRouter.PUT("update", productApi.UpdateProduct)         // 更新商品
		productRouter.DELETE(":id", productApi.DeleteProduct)         // 删除商品
		productRouter.POST("buy", productApi.BuyProduct)              // 购买商品
		productRouter.POST("cancel/:orderNo", productApi.CancelOrder) // 取消订单
	}
	{
		productRouterWithoutRecord.GET("list", productApi.GetProductList) // 获取商品列表
		productRouterWithoutRecord.GET(":id", productApi.GetProduct)      // 获取单个商品
	}
	{
		productRouterWithoutAuth.GET("membership", productApi.GetMembershipProducts) // 获取会员商品列表（无需认证）
	}
}

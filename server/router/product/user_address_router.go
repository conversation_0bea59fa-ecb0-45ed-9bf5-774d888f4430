package product

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserAddressRouter struct{}

// InitUserAddressRouter 初始化用户地址路由
func (s *UserAddressRouter) InitUserAddressRouter(Router *gin.RouterGroup) (R gin.IRoutes) {
	userAddressRouter := Router.Group("userAddress").Use(middleware.OperationRecord())
	userAddressRouterWithoutRecord := Router.Group("userAddress")
	var userAddressApi = v1.ApiGroupApp.ProductApiGroup.UserAddressApi

	{
		userAddressRouter.POST("create", userAddressApi.CreateUserAddress)       // 创建地址
		userAddressRouter.PUT("update", userAddressApi.UpdateUserAddress)        // 更新地址
		userAddressRouter.DELETE("deleteById", userAddressApi.DeleteUserAddress) // 删除地址
		userAddressRouter.POST("setDefault", userAddressApi.SetDefaultAddress)   // 设置默认地址
	}
	{
		userAddressRouterWithoutRecord.GET("list", userAddressApi.GetUserAddressList)   // 获取地址列表
		userAddressRouterWithoutRecord.GET("findById", userAddressApi.GetUserAddress)   // 获取单个地址
		userAddressRouterWithoutRecord.GET("default", userAddressApi.GetDefaultAddress) // 获取默认地址
	}
	return userAddressRouter
}

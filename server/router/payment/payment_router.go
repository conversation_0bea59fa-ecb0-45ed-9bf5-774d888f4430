package payment

import (
	"github.com/gin-gonic/gin"
)

type PaymentRouter struct{}

// InitPaymentRouter 初始化支付路由
func (p *PaymentRouter) InitPaymentRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	paymentRouter := Router.Group("payment")
	paymentRouterWithoutAuth := PublicRouter.Group("payment/notify")
	{
		//paymentRouter.POST("create", paymentApi.CreatePayment)       // 创建支付订单
		paymentRouter.GET("queryByOrderNo", paymentApi.QueryPayment) // 查询支付状态
		paymentRouter.POST("refund", paymentApi.RefundPayment)       // 退款
		paymentRouter.GET("config", paymentApi.GetPaymentConfig)     // 获取支付配置
		paymentRouter.GET("list", paymentApi.GetPaymentList)         // 获取支付订单列表
	}
	{
		paymentRouterWithoutAuth.POST("wechatNotify", paymentApi.WechatNotify) // 微信支付通知
		paymentRouterWithoutAuth.POST("alipayNotify", paymentApi.AlipayNotify) // 支付宝通知
	}
}

package mcprouter

import (
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/handle/proxy"
	"github.com/labstack/echo/v4"
)

// ProxyRoute will create the routes for the http server
func ProxyRoute(e *echo.Echo) {
	// sse proxy
	e.GET("/sse/:key/:token", func(c echo.Context) error {
		// 跨域头
		c.Response().Header().Set("Access-Control-Allow-Origin", "*")
		c.Response().Header().Set("Access-Control-Allow-Methods", "GET,POST,OPTIONS")
		c.Response().Header().Set("Access-Control-Allow-Headers", "Content-Type,Authorization,Token,X-Token,X-User-Id")
		if c.Request().Method == "OPTIONS" {
			return c.NoContent(204)
		}
		// 处理参数
		key := strings.Split(c.Param("key"), "/")[0]
		token := c.Param("token")

		// 设置处理后的参数
		c.Set("key", key)
		c.Set("token", token)

		return proxy.SSE(c)
	})

	e.POST("/messages", func(c echo.Context) error {
		c.Response().Header().Set("Access-Control-Allow-Origin", "*")
		c.Response().Header().Set("Access-Control-Allow-Methods", "GET,POST,OPTIONS")
		c.Response().Header().Set("Access-Control-Allow-Headers", "Content-Type,Authorization,Token,X-Token,X-User-Id")
		if c.Request().Method == "OPTIONS" {
			return c.NoContent(204)
		}
		return proxy.Messages(c)
	})

	// streamable http proxy
	e.Any("/mcp/:key/:token", func(c echo.Context) error {
		c.Response().Header().Set("Access-Control-Allow-Origin", "*")
		c.Response().Header().Set("Access-Control-Allow-Methods", "GET,POST,OPTIONS")
		c.Response().Header().Set("Access-Control-Allow-Headers", "Content-Type,Authorization,Token,X-Token,X-User-Id")
		if c.Request().Method == "OPTIONS" {
			return c.NoContent(204)
		}
		// 处理参数
		key := strings.Split(c.Param("key"), "/")[0]
		token := c.Param("token")

		// 设置处理后的参数
		c.Set("key", key)
		c.Set("token", token)

		return proxy.MCP(c)
	})
}

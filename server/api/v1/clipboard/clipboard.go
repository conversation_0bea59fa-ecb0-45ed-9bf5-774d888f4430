package clipboard

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/clipboard"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ClipboardApi struct{}

// CreateClipboardItem 创建剪贴板条目
// @Tags 剪贴板管理
// @Summary 创建剪贴板条目
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clipboard.ClipboardItemCreateRequest true "创建剪贴板条目"
// @Success 200 {object} response.Response{data=clipboard.ClipboardItem,msg=string} "创建成功"
// @Router /clipboard/item [post]
func (c *ClipboardApi) CreateClipboardItem(ctx *gin.Context) {
	var req clipboard.ClipboardItemCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	item, err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.CreateClipboardItem(userID.(uint), req)
	if err != nil {
		global.GVA_LOG.Error("创建剪贴板条目失败", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), ctx)
		return
	}

	response.OkWithDetailed(item, "创建成功", ctx)
}

// GetClipboardItemList 获取剪贴板条目列表
// @Tags 剪贴板管理
// @Summary 获取剪贴板条目列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query clipboard.ClipboardItemListRequest true "分页获取剪贴板条目列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /clipboard/itemList [get]
func (c *ClipboardApi) GetClipboardItemList(ctx *gin.Context) {
	var req clipboard.ClipboardItemListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	list, total, err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.GetClipboardItemList(userID.(uint), req)
	if err != nil {
		global.GVA_LOG.Error("获取剪贴板条目列表失败", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", ctx)
}

// GetClipboardItem 获取单个剪贴板条目
// @Tags 剪贴板管理
// @Summary 获取单个剪贴板条目
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "剪贴板条目ID"
// @Success 200 {object} response.Response{data=clipboard.ClipboardItem,msg=string} "获取成功"
// @Router /clipboard/item/{id} [get]
func (c *ClipboardApi) GetClipboardItem(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", ctx)
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	item, err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.GetClipboardItem(userID.(uint), uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取剪贴板条目失败", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithDetailed(item, "获取成功", ctx)
}

// UpdateClipboardItem 更新剪贴板条目
// @Tags 剪贴板管理
// @Summary 更新剪贴板条目
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clipboard.ClipboardItemUpdateRequest true "更新剪贴板条目"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /clipboard/item [put]
func (c *ClipboardApi) UpdateClipboardItem(ctx *gin.Context) {
	var req clipboard.ClipboardItemUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.UpdateClipboardItem(userID.(uint), req)
	if err != nil {
		global.GVA_LOG.Error("更新剪贴板条目失败", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("更新成功", ctx)
}

// DeleteClipboardItem 删除剪贴板条目
// @Tags 剪贴板管理
// @Summary 删除剪贴板条目
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "剪贴板条目ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /clipboard/item/{id} [delete]
func (c *ClipboardApi) DeleteClipboardItem(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", ctx)
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	err = service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.DeleteClipboardItem(userID.(uint), uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除剪贴板条目失败", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("删除成功", ctx)
}

// ClearClipboard 清空剪贴板
// @Tags 剪贴板管理
// @Summary 清空剪贴板
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "清空成功"
// @Router /clipboard/clear [post]
func (c *ClipboardApi) ClearClipboard(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.ClearClipboard(userID.(uint))
	if err != nil {
		global.GVA_LOG.Error("清空剪贴板失败", zap.Error(err))
		response.FailWithMessage("清空失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("清空成功", ctx)
}

// GetUserConfig 获取用户配置
// @Tags 剪贴板管理
// @Summary 获取用户配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=clipboard.ClipboardConfig,msg=string} "获取成功"
// @Router /clipboard/config [get]
func (c *ClipboardApi) GetUserConfig(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	config, err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.GetUserConfig(userID.(uint))
	if err != nil {
		global.GVA_LOG.Error("获取用户配置失败", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithDetailed(config, "获取成功", ctx)
}

// UpdateUserConfig 更新用户配置
// @Tags 剪贴板管理
// @Summary 更新用户配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clipboard.ClipboardConfigUpdateRequest true "更新用户配置"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /clipboard/config [put]
func (c *ClipboardApi) UpdateUserConfig(ctx *gin.Context) {
	var req clipboard.ClipboardConfigUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.UpdateUserConfig(userID.(uint), req)
	if err != nil {
		global.GVA_LOG.Error("更新用户配置失败", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("更新成功", ctx)
}

// GetClipboardStats 获取剪贴板统计信息
// @Tags 剪贴板管理
// @Summary 获取剪贴板统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=clipboard.ClipboardStatsResponse,msg=string} "获取成功"
// @Router /clipboard/stats [get]
func (c *ClipboardApi) GetClipboardStats(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		response.FailWithMessage("用户未登录", ctx)
		return
	}

	stats, err := service.ServiceGroupApp.ClipboardServiceGroup.ClipboardService.GetClipboardStats(userID.(uint))
	if err != nil {
		global.GVA_LOG.Error("获取剪贴板统计信息失败", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithDetailed(stats, "获取成功", ctx)
}

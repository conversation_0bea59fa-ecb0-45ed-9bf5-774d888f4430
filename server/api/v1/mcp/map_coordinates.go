package mcp

import (
	"context"
	"encoding/json"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
)

type MapCoordinatesApi struct{}

type MapCoordinatesRequest struct {
	Key   string                 `json:"key" binding:"required"`
	Value map[string]interface{} `json:"value" binding:"required"`
}

type MapCoordinatesGetRequest struct {
	Key string `json:"key" form:"key" binding:"required"`
}

// SetMapStringRequest 用于设置字符串 value
type SetMapStringRequest struct {
	Key   string `json:"key" binding:"required"`
	Value string `json:"value" binding:"required"`
}

// SetRedisKV 接收key和value（一个对象），存入redis
func (r *MapCoordinatesApi) SetMapCoordinates(c *gin.Context) {
	var req MapCoordinatesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	redisClient := global.GetRedis("map")

	jsonValue, err := json.Marshal(req.Value)
	if err != nil {
		response.FailWithMessage("序列化失败: "+err.Error(), c)
		return
	}
	err = redisClient.Set(context.Background(), "map_coordinates:"+req.Key, jsonValue, 0).Err()
	if err != nil {
		response.FailWithMessage("写入redis失败: "+err.Error(), c)
		return
	}
	response.Ok(c)
}

// GetRedisKV 接收key返回value
func (r *MapCoordinatesApi) GetMapCoordinates(c *gin.Context) {
	var req MapCoordinatesGetRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	redisClient := global.GetRedis("map")
	val, err := redisClient.Get(context.Background(), "map_coordinates:"+req.Key).Result()
	if err != nil {
		response.FailWithMessage("读取redis失败: "+err.Error(), c)
		return
	}
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		response.FailWithMessage("反序列化失败: "+err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}

// SetMapString 接收key和value（字符串），存入redis
func (r *MapCoordinatesApi) SetMapString(c *gin.Context) {
	var req SetMapStringRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	redisClient := global.GetRedis("map")
	err := redisClient.Set(context.Background(), "map_string:"+req.Key, req.Value, 2*time.Hour).Err()
	if err != nil {
		response.FailWithMessage("写入redis失败: "+err.Error(), c)
		return
	}
	response.Ok(c)
}

// GetMapStringRequest 用于获取字符串 value
type GetMapStringRequest struct {
	Key string `json:"key" form:"key" binding:"required"`
}

// GetMapString 接收key返回value（字符串）
func (r *MapCoordinatesApi) GetMapString(c *gin.Context) {
	var req GetMapStringRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	redisClient := global.GetRedis("map")
	val, err := redisClient.Get(context.Background(), "map_string:"+req.Key).Result()
	if err != nil {
		response.FailWithMessage("读取redis失败: "+err.Error(), c)
		return
	}
	response.OkWithData(val, c)
}

package mcp

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ApiKeyApi struct{}

// CreateApiKeyRequest 创建API密钥请求
type CreateApiKeyRequest struct {
	Name string `json:"name" binding:"required"`
}

// CreateApiKey 创建API密钥
// @Tags ApiKey
// @Summary 创建API密钥
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body CreateApiKeyRequest true "密钥名称"
// @Success 200 {object} response.Response{data=system.SysApiKey} "创建成功"
// @Router /apiKey/createApiKey [post]
func (s *ApiKeyApi) CreateApiKey(c *gin.Context) {
	var req CreateApiKeyRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	apiKey, err := apiKeyService.CreateApiKey(req.Name, c)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithDetailed(apiKey, "创建成功", c)
}

// DeleteApiKey 删除API密钥
// @Tags ApiKey
// @Summary 删除API密钥
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "主键ID"
// @Success 200 {object} response.Response "删除成功"
// @Router /apiKey/deleteApiKey [delete]
func (s *ApiKeyApi) DeleteApiKey(c *gin.Context) {
	idStr := c.Query("id")
	if idStr == "" {
		response.FailWithMessage("ID不能为空", c)
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID格式", c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 检查API密钥是否存在且属于当前用户
	var apiKey mcp.ApiKey
	if err := global.GVA_DB.First(&apiKey, id).Error; err != nil {
		global.GVA_LOG.Error("API密钥不存在!", zap.Error(err))
		response.FailWithMessage("API密钥不存在", c)
		return
	}

	if apiKey.UserId != userId {
		response.FailWithMessage("无权删除他人的API密钥", c)
		return
	}

	err = apiKeyService.DeleteApiKey(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GetApiKeys 获取API密钥列表
// @Tags ApiKey
// @Summary 获取API密钥列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页大小"
// @Param keyword query string false "关键字"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]mcp.ApiKey}} "获取成功"
// @Router /apiKey/getApiKeys [post]
func (s *ApiKeyApi) GetApiKeys(c *gin.Context) {
	var pageInfo request.PageInfo
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	list, total, err := apiKeyService.GetApiKeys(pageInfo, userId)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

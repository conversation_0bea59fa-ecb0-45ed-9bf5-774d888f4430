package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BindingApi struct{}

var bindingService = mcprouter.BindingService{}

// BindServerLogAndAgentChatHistory 绑定ServerLog与AgentChatHistory
// @Tags MCP
// @Summary 绑定ServerLog与AgentChatHistory
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcprouter.BindingRequest true "绑定请求参数"
// @Success 200 {object} response.Response{data=mcprouter.BindingResponse,msg=string} "绑定成功"
// @Router /mcp/bindServerLogAndAgentChatHistory [post]
func (bindingApi *BindingApi) BindServerLogAndAgentChatHistory(c *gin.Context) {
	var req mcprouter.BindingRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数绑定失败: "+err.Error(), c)
		return
	}

	// 调用服务层进行绑定
	result, err := bindingService.BindServerLogAndAgentChatHistory(&req)
	if err != nil {
		global.GVA_LOG.Error("绑定ServerLog与AgentChatHistory失败", zap.Error(err))
		response.FailWithMessage("绑定失败: "+err.Error(), c)
		return
	}

	// 根据绑定结果返回响应
	if result.Success {
		response.OkWithDetailed(result, "绑定成功", c)
	} else {
		response.FailWithDetailed(result, result.Message, c)
	}
}

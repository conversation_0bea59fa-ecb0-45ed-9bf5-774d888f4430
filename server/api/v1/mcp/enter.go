package mcp

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	ProjectToolsApi
	ProjectsApi
	McpClientApi
	ApiKeyApi
	UseCaseApi
	ToolsApi
	MapCoordinatesApi
	BindingApi
	SunoTaskApi
	ProjectCommentsApi
}

var (
	projectToolsService    = service.ServiceGroupApp.McpServiceGroup.ProjectToolsService
	projectsService        = service.ServiceGroupApp.McpServiceGroup.ProjectsService
	mcpClientService       = service.ServiceGroupApp.McpServiceGroup.McpClientService
	apiKeyService          = service.ServiceGroupApp.McpServiceGroup.ApiKeyService
	useCaseService         = service.ServiceGroupApp.McpServiceGroup.UseCaseService
	sunoTaskService        = service.ServiceGroupApp.McpServiceGroup.SunoTaskService
	projectCommentsService = service.ServiceGroupApp.McpServiceGroup.ProjectCommentsService
)

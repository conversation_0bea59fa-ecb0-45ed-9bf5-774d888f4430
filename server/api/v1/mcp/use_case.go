package mcp

import (
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type UseCaseApi struct{}

func (useCaseApi *UseCaseApi) CreateUseCase(c *gin.Context) {
	var useCase mcp.UseCase
	if err := c.ShouldBindJSON(&useCase); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	useCase.UserId = utils.GetUserID(c)
	useCase.Date = time.Now().Format("2006-01-02")
	if err := useCaseService.CreateUseCase(c.Request.Context(), &useCase); err != nil {
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

func (useCaseApi *UseCaseApi) DeleteUseCase(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	userId := utils.GetUserID(c)
	if err := useCaseService.DeleteUseCase(c.Request.Context(), uint(id), userId); err != nil {
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

func (useCaseApi *UseCaseApi) UpdateUseCase(c *gin.Context) {
	var useCase mcp.UseCase
	if err := c.ShouldBindJSON(&useCase); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := useCaseService.UpdateUseCase(c.Request.Context(), &useCase); err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

func (useCaseApi *UseCaseApi) FindUseCase(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	useCase, err := useCaseService.GetUseCaseByID(c.Request.Context(), uint(id))
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	response.OkWithData(useCase, c)
}

func (useCaseApi *UseCaseApi) GetUseCaseList(c *gin.Context) {
	type Req struct {
		systemReq.SearchApiParams
		ProjectsId int64 `json:"projectsId"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	list, total, err := useCaseService.GetUseCaseList(c.Request.Context(), req.SearchApiParams, req.ProjectsId)
	if err != nil {
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

func (useCaseApi *UseCaseApi) MyUseCaseList(c *gin.Context) {
	type Req struct {
		systemReq.SearchApiParams
	}
	var req Req
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	userId := utils.GetUserID(c)
	list, total, err := useCaseService.MyUseCaseList(c.Request.Context(), req.SearchApiParams, userId)
	if err != nil {
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// ApproveUseCase 审核通过案例
func (useCaseApi *UseCaseApi) ApproveUseCase(c *gin.Context) {
	type Req struct {
		ID uint `json:"id" binding:"required"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := useCaseService.ApproveUseCase(c.Request.Context(), req.ID); err != nil {
		response.FailWithMessage("审核失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("审核通过成功", c)
}

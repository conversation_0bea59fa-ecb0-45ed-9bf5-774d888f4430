package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ToolsApi struct{}

// GetTools 获取工具列表
// @Tags Tools
// @Summary 获取工具列表
// @Accept application/json
// @Produce application/json
// @Param project_id query int true "项目ID"
// @Success 200 {object} response.Response{data=[]object,msg=string} "获取成功"
// @Router /tools/list [get]
func (t *ToolsApi) GetTools(c *gin.Context) {
	var project mcp.Projects
	projectId := c.Query("project_id")
	if projectId == "" {
		response.FailWithMessage("project_id不能为空", c)
		return
	}

	// 查询项目信息
	if err := global.GVA_DB.First(&project, projectId).Error; err != nil {
		global.GVA_LOG.Error("查询项目信息失败!", zap.Error(err))
		response.FailWithMessage("查询项目信息失败:"+err.Error(), c)
		return
	}

	// 使用事务处理工具获取和更新
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 使用统一的工具获取方法
	global.GVA_LOG.Info("[GetTools] 开始获取工具列表", zap.String("project", project.UUID))
	tools, err := FetchToolsListTx(tx, &project)
	if err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("[GetTools] 获取工具列表失败", zap.Error(err))
		response.FailWithMessage("获取工具列表失败:"+err.Error(), c)
		return
	}
	global.GVA_LOG.Info("[GetTools] 获取到工具数量", zap.Int("count", len(tools)))

	// 使用统一的工具插入方法
	if err := InsertToolsForProjectTx(tx, &project, tools); err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("[GetTools] 插入工具失败", zap.Error(err))
		response.FailWithMessage("插入工具失败:"+err.Error(), c)
		return
	}
	global.GVA_LOG.Info("[GetTools] 插入工具成功", zap.Int("count", len(tools)))

	tx.Commit()

	// 构造返回结果
	result := map[string]interface{}{
		"tools": tools,
	}
	response.OkWithDetailed(result, "获取成功", c)
}

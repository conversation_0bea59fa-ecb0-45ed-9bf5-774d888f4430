package mcp

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type McpClientApi struct{}

// CreateMcpClient 创建mcp客户端
// @Tags McpClient
// @Summary 创建mcp客户端
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.McpClient true "创建mcp客户端"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /mcpClient/createMcpClient [post]
func (mcpClientApi *McpClientApi) CreateMcpClient(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var mcpClient mcp.McpClient
	err := c.ShouldBindJSON(&mcpClient)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = mcpClientService.CreateMcpClient(ctx, &mcpClient)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteMcpClient 删除mcp客户端
// @Tags McpClient
// @Summary 删除mcp客户端
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.McpClient true "删除mcp客户端"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /mcpClient/deleteMcpClient [delete]
func (mcpClientApi *McpClientApi) DeleteMcpClient(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := mcpClientService.DeleteMcpClient(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteMcpClientByIds 批量删除mcp客户端
// @Tags McpClient
// @Summary 批量删除mcp客户端
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /mcpClient/deleteMcpClientByIds [delete]
func (mcpClientApi *McpClientApi) DeleteMcpClientByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := mcpClientService.DeleteMcpClientByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateMcpClient 更新mcp客户端
// @Tags McpClient
// @Summary 更新mcp客户端
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.McpClient true "更新mcp客户端"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /mcpClient/updateMcpClient [put]
func (mcpClientApi *McpClientApi) UpdateMcpClient(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var mcpClient mcp.McpClient
	err := c.ShouldBindJSON(&mcpClient)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = mcpClientService.UpdateMcpClient(ctx, mcpClient)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindMcpClient 用id查询mcp客户端
// @Tags McpClient
// @Summary 用id查询mcp客户端
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询mcp客户端"
// @Success 200 {object} response.Response{data=mcp.McpClient,msg=string} "查询成功"
// @Router /mcpClient/findMcpClient [get]
func (mcpClientApi *McpClientApi) FindMcpClient(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	remcpClient, err := mcpClientService.GetMcpClient(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(remcpClient, c)
}

// GetMcpClientList 分页获取mcp客户端列表
// @Tags McpClient
// @Summary 分页获取mcp客户端列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query mcpReq.McpClientSearch true "分页获取mcp客户端列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /mcpClient/getMcpClientList [get]
func (mcpClientApi *McpClientApi) GetMcpClientList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo mcpReq.McpClientSearch
	var err error

	// 根据请求方法选择不同的绑定方式
	if c.Request.Method == "POST" {
		err = c.ShouldBindJSON(&pageInfo)
	} else {
		err = c.ShouldBindQuery(&pageInfo)
	}

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 如果是POST请求且Category字段使用大写，手动处理
	if c.Request.Method == "POST" {
		if category, exists := c.GetPostForm("category"); exists {
			pageInfo.Category = &category
		}
	}

	list, total, err := mcpClientService.GetMcpClientInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// UpdateMcpClientDownloadCount 更新mcp客户端下载次数
// @Tags McpClient
// @Summary 更新mcp客户端下载次数
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query string true "更新mcp客户端下载次数"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /mcpClient/updateMcpClientDownloadCount [put]
func (mcpClientApi *McpClientApi) UpdateMcpClientDownloadCount(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	idStr := c.Query("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	err = mcpClientService.UpdateMcpClientDownloadCount(ctx, uint(id))
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	integralRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type TaskApi struct{}

// GetTaskList
// @Tags      Task
// @Summary   获取任务列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=integralRes.TaskListResponse,msg=string}  "获取任务列表成功"
// @Router    /task/getTaskList [get]
func (t *TaskApi) GetTaskList(c *gin.Context) {

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 获取任务列表
	list, err := taskService.GetTaskList(userID)
	if err != nil {
		global.GVA_LOG.Error("获取任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取任务列表失败", c)
		return
	}

	response.OkWithDetailed(integralRes.TaskListResponse{
		List: list,
	}, "获取任务列表成功", c)
}

// CompleteTask
// @Tags      Task
// @Summary   完成任务
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      integralReq.CompleteTaskRequest                                    true  "完成任务"
// @Success   200   {object}  response.Response{data=integralRes.CompleteTaskResponse,msg=string}  "完成任务成功"
// @Router    /task/completeTask [post]
func (t *TaskApi) CompleteTask(c *gin.Context) {
	var req integralReq.CompleteTaskRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 完成任务
	result, err := taskService.CompleteTask(userID, req.TaskID)
	if err != nil {
		global.GVA_LOG.Error("完成任务失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "完成任务成功", c)
}

// GetTaskById
// @Tags      Task
// @Summary   根据ID获取任务详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   query     uint                                                           true  "任务ID"
// @Success   200  {object}  response.Response{data=system.SysTask,msg=string}              "获取任务详情成功"
// @Router    /task/getTaskById [get]
func (t *TaskApi) GetTaskById(c *gin.Context) {
	var idInfo request.GetById
	err := c.ShouldBindQuery(&idInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	task, err := taskService.GetTaskByID(uint(idInfo.ID))
	if err != nil {
		global.GVA_LOG.Error("获取任务详情失败!", zap.Error(err))
		response.FailWithMessage("获取任务详情失败", c)
		return
	}

	response.OkWithDetailed(task, "获取任务详情成功", c)
}

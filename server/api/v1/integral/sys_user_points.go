package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SysUserPointsApi struct{}

// GetUserPointsRecords
// @Tags      SysUserPoints
// @Summary   获取用户积分记录
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      integralReq.SysUserPointsSearch                                    true  "查询参数"
// @Success   200   {object}  response.Response{data=integralRes.UserTaskListResponse,msg=string}  "获取积分记录成功"
// @Router    /userPoints/getUserPointsRecords [post]
func (s *SysUserPointsApi) GetUserPointsRecords(c *gin.Context) {
	var req integralReq.SysUserPointsSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 获取积分记录
	result, err := userPointsService.GetUserPointsRecords(userID, req)
	if err != nil {
		global.GVA_LOG.Error("获取积分记录失败!", zap.Error(err))
		response.FailWithMessage("获取积分记录失败", c)
		return
	}

	response.OkWithDetailed(result, "获取积分记录成功", c)
}

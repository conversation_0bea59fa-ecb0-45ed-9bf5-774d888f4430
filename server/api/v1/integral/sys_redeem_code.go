package integral

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RedeemCodeApi struct{}

// CreateRedeemCode 创建兑换码
// @Tags RedeemCode
// @Summary 创建兑换码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.CreateRedeemCodeRequest true "创建兑换码"
// @Success 200 {object} response.Response{data=integral.SysRedeemCode,msg=string} "创建成功"
// @Router /redeemCode/create [post]
func (r *RedeemCodeApi) CreateRedeemCode(c *gin.Context) {
	var req integralReq.CreateRedeemCodeRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	createdBy := utils.GetUserID(c)
	redeemCode, err := redeemCodeService.CreateRedeemCode(req, createdBy)
	if err != nil {
		global.GVA_LOG.Error("创建兑换码失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(redeemCode, "创建成功", c)
}

// BatchCreateRedeemCode 批量创建兑换码
// @Tags RedeemCode
// @Summary 批量创建兑换码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.BatchCreateRedeemCodeRequest true "批量创建兑换码"
// @Success 200 {object} response.Response{data=integralRes.BatchCreateRedeemCodeResponse,msg=string} "创建成功"
// @Router /redeemCode/batchCreate [post]
func (r *RedeemCodeApi) BatchCreateRedeemCode(c *gin.Context) {
	var req integralReq.BatchCreateRedeemCodeRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	createdBy := utils.GetUserID(c)
	result, err := redeemCodeService.BatchCreateRedeemCode(req, createdBy)
	if err != nil {
		global.GVA_LOG.Error("批量创建兑换码失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "创建成功", c)
}

// GetRedeemCodeList 获取兑换码列表
// @Tags RedeemCode
// @Summary 获取兑换码列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query integralReq.RedeemCodeSearch true "查询参数"
// @Success 200 {object} response.Response{data=integralRes.RedeemCodeListResponse,msg=string} "获取成功"
// @Router /redeemCode/list [get]
func (r *RedeemCodeApi) GetRedeemCodeList(c *gin.Context) {
	var req integralReq.RedeemCodeSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, err := redeemCodeService.GetRedeemCodeList(req)
	if err != nil {
		global.GVA_LOG.Error("获取兑换码列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GetRedeemCodeByID 根据ID获取兑换码详情
// @Tags RedeemCode
// @Summary 根据ID获取兑换码详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "兑换码ID"
// @Success 200 {object} response.Response{data=integralRes.RedeemCodeResponse,msg=string} "获取成功"
// @Router /redeemCode/{id} [get]
func (r *RedeemCodeApi) GetRedeemCodeByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	redeemCode, err := redeemCodeService.GetRedeemCodeByID(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取兑换码详情失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(redeemCode, "获取成功", c)
}

// UpdateRedeemCode 更新兑换码
// @Tags RedeemCode
// @Summary 更新兑换码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.UpdateRedeemCodeRequest true "更新兑换码"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /redeemCode/update [put]
func (r *RedeemCodeApi) UpdateRedeemCode(c *gin.Context) {
	var req integralReq.UpdateRedeemCodeRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = redeemCodeService.UpdateRedeemCode(req)
	if err != nil {
		global.GVA_LOG.Error("更新兑换码失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteRedeemCode 删除兑换码
// @Tags RedeemCode
// @Summary 删除兑换码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "兑换码ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /redeemCode/{id} [delete]
func (r *RedeemCodeApi) DeleteRedeemCode(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	err = redeemCodeService.DeleteRedeemCode(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除兑换码失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// DeleteRedeemCodeBatch 批量删除兑换码
// @Tags RedeemCode
// @Summary 批量删除兑换码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param batchId path string true "批次ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /redeemCode/batch/{batchId} [delete]
func (r *RedeemCodeApi) DeleteRedeemCodeBatch(c *gin.Context) {
	batchID := c.Param("batchId")
	if batchID == "" {
		response.FailWithMessage("批次ID不能为空", c)
		return
	}

	err := redeemCodeService.DeleteRedeemCodeBatch(batchID)
	if err != nil {
		global.GVA_LOG.Error("批量删除兑换码失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// UseRedeemCode 使用兑换码
// @Tags RedeemCode
// @Summary 使用兑换码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.UseRedeemCodeRequest true "使用兑换码"
// @Success 200 {object} response.Response{data=integralRes.UseRedeemCodeResponse,msg=string} "兑换成功"
// @Router /redeemCode/use [post]
func (r *RedeemCodeApi) UseRedeemCode(c *gin.Context) {
	var req integralReq.UseRedeemCodeRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	result, err := redeemCodeService.UseRedeemCode(req, userID, ipAddress, userAgent)
	if err != nil {
		global.GVA_LOG.Error("使用兑换码失败!", zap.Error(err))
		response.FailWithMessage("兑换失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "兑换成功", c)
}

// ValidateRedeemCode 验证兑换码
// @Tags RedeemCode
// @Summary 验证兑换码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param code query string true "兑换码"
// @Success 200 {object} response.Response{data=integral.SysRedeemCode,msg=string} "验证成功"
// @Router /redeemCode/validate [get]
func (r *RedeemCodeApi) ValidateRedeemCode(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		response.FailWithMessage("兑换码不能为空", c)
		return
	}

	redeemCode, err := redeemCodeService.ValidateRedeemCode(code)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithDetailed(redeemCode, "验证成功", c)
}

// GetRedeemCodeUsageList 获取兑换码使用记录列表
// @Tags RedeemCode
// @Summary 获取兑换码使用记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query integralReq.RedeemCodeUsageSearch true "查询参数"
// @Success 200 {object} response.Response{data=integralRes.RedeemCodeUsageListResponse,msg=string} "获取成功"
// @Router /redeemCode/usage/list [get]
func (r *RedeemCodeApi) GetRedeemCodeUsageList(c *gin.Context) {
	var req integralReq.RedeemCodeUsageSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, err := redeemCodeService.GetRedeemCodeUsageList(req)
	if err != nil {
		global.GVA_LOG.Error("获取兑换码使用记录失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GetUserRedeemHistory 获取用户兑换历史
// @Tags RedeemCode
// @Summary 获取用户兑换历史
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query integralReq.RedeemCodeUsageSearch true "查询参数"
// @Success 200 {object} response.Response{data=integralRes.RedeemCodeUsageListResponse,msg=string} "获取成功"
// @Router /redeemCode/user/history [get]
func (r *RedeemCodeApi) GetUserRedeemHistory(c *gin.Context) {
	var req integralReq.RedeemCodeUsageSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	list, err := redeemCodeService.GetUserRedeemHistory(userID, req)
	if err != nil {
		global.GVA_LOG.Error("获取用户兑换历史失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GetRedeemCodeStats 获取兑换码统计信息
// @Tags RedeemCode
// @Summary 获取兑换码统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=integralRes.RedeemCodeStatsResponse,msg=string} "获取成功"
// @Router /redeemCode/stats [get]
func (r *RedeemCodeApi) GetRedeemCodeStats(c *gin.Context) {
	stats, err := redeemCodeService.GetRedeemCodeStats()
	if err != nil {
		global.GVA_LOG.Error("获取兑换码统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(stats, "获取成功", c)
}

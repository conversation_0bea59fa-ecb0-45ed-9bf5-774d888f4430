package integral

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	integralRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FeedbackApi struct{}

// CreateFeedback 创建反馈
// @Tags Feedback
// @Summary 创建反馈
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.CreateFeedbackRequest true "创建反馈"
// @Success 200 {object} response.Response{data=integral.Feedback,msg=string} "创建成功"
// @Router /feedback/create [post]
func (feedbackApi *FeedbackApi) CreateFeedback(c *gin.Context) {
	var req integralReq.CreateFeedbackRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	feedback, err := feedbackService.CreateFeedback(userID, req)
	if err != nil {
		global.GVA_LOG.Error("创建反馈失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(feedback, "创建成功", c)
}

// UpdateFeedback 更新反馈
// @Tags Feedback
// @Summary 更新反馈
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.UpdateFeedbackRequest true "更新反馈"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /feedback/update [put]
func (feedbackApi *FeedbackApi) UpdateFeedback(c *gin.Context) {
	var req integralReq.UpdateFeedbackRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	err = feedbackService.UpdateFeedback(userID, req)
	if err != nil {
		global.GVA_LOG.Error("更新反馈失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteFeedback 删除反馈
// @Tags Feedback
// @Summary 删除反馈
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "反馈ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /feedback/delete/{id} [delete]
func (feedbackApi *FeedbackApi) DeleteFeedback(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	userID := utils.GetUserID(c)
	err = feedbackService.DeleteFeedback(userID, uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除反馈失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetUserFeedbackList 获取用户自己的反馈列表
// @Tags Feedback
// @Summary 获取用户自己的反馈列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query integralReq.FeedbackSearch true "分页获取用户反馈列表"
// @Success 200 {object} response.Response{data=integralRes.FeedbackListResponse,msg=string} "获取成功"
// @Router /feedback/getUserList [get]
func (feedbackApi *FeedbackApi) GetUserFeedbackList(c *gin.Context) {
	var pageInfo integralReq.FeedbackSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	list, total, err := feedbackService.GetUserFeedbackList(userID, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取用户反馈列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(integralRes.FeedbackListResponse{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetPublicFeedbackList 获取公开的反馈列表
// @Tags Feedback
// @Summary 获取公开的反馈列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query integralReq.FeedbackSearch true "分页获取公开反馈列表"
// @Success 200 {object} response.Response{data=integralRes.FeedbackListResponse,msg=string} "获取成功"
// @Router /feedback/getPublicList [get]
func (feedbackApi *FeedbackApi) GetPublicFeedbackList(c *gin.Context) {
	var pageInfo integralReq.FeedbackSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := feedbackService.GetPublicFeedbackList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取公开反馈列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(integralRes.FeedbackListResponse{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAdminFeedbackList 获取管理员反馈列表（所有反馈）
// @Tags Feedback
// @Summary 获取管理员反馈列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query integralReq.FeedbackSearch true "分页获取管理员反馈列表"
// @Success 200 {object} response.Response{data=integralRes.FeedbackListResponse,msg=string} "获取成功"
// @Router /feedback/getAdminList [get]
func (feedbackApi *FeedbackApi) GetAdminFeedbackList(c *gin.Context) {
	var pageInfo integralReq.FeedbackSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := feedbackService.GetAdminFeedbackList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取管理员反馈列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(integralRes.FeedbackListResponse{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// ReviewFeedback 审核反馈
// @Tags Feedback
// @Summary 审核反馈
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.ReviewFeedbackRequest true "审核反馈"
// @Success 200 {object} response.Response{msg=string} "审核成功"
// @Router /feedback/review [post]
func (feedbackApi *FeedbackApi) ReviewFeedback(c *gin.Context) {
	var req integralReq.ReviewFeedbackRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	adminUserID := utils.GetUserID(c)
	err = feedbackService.ReviewFeedback(adminUserID, req)
	if err != nil {
		global.GVA_LOG.Error("审核反馈失败!", zap.Error(err))
		response.FailWithMessage("审核失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("审核成功", c)
}

// LikeFeedback 点赞反馈
// @Tags Feedback
// @Summary 点赞反馈
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body integralReq.LikeFeedbackRequest true "点赞反馈"
// @Success 200 {object} response.Response{msg=string} "点赞成功"
// @Router /feedback/like [post]
func (feedbackApi *FeedbackApi) LikeFeedback(c *gin.Context) {
	var req integralReq.LikeFeedbackRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	err = feedbackService.LikeFeedback(userID, req.ID)
	if err != nil {
		global.GVA_LOG.Error("点赞反馈失败!", zap.Error(err))
		response.FailWithMessage("点赞失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("点赞成功", c)
}

// GetFeedbackByID 根据ID获取反馈详情
// @Tags Feedback
// @Summary 根据ID获取反馈详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "反馈ID"
// @Success 200 {object} response.Response{data=integral.Feedback,msg=string} "获取成功"
// @Router /feedback/find/{id} [get]
func (feedbackApi *FeedbackApi) GetFeedbackByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	userID := utils.GetUserID(c)
	feedback, err := feedbackService.GetFeedbackByID(uint(id), userID)
	if err != nil {
		global.GVA_LOG.Error("获取反馈详情失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(feedback, "获取成功", c)
}

// GetFeedbackStats 获取反馈统计信息
// @Tags Feedback
// @Summary 获取反馈统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=integralRes.FeedbackStatsResponse,msg=string} "获取成功"
// @Router /feedback/stats [get]
func (feedbackApi *FeedbackApi) GetFeedbackStats(c *gin.Context) {
	stats, err := feedbackService.GetFeedbackStats()
	if err != nil {
		global.GVA_LOG.Error("获取反馈统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(stats, "获取成功", c)
}

// GetFeedbackTaskList 获取当前用户的反馈任务完成列表
// @Tags Feedback
// @Summary 获取当前用户的反馈任务完成列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query integralReq.FeedbackTaskListRequest true "分页获取反馈任务列表"
// @Success 200 {object} response.Response{data=integralRes.UserTaskListResponse,msg=string} "获取成功"
// @Router /feedback/feedbackTaskList [get]
func (feedbackApi *FeedbackApi) GetFeedbackTaskList(c *gin.Context) {
	var pageInfo integralReq.FeedbackTaskListRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	list, total, err := feedbackService.GetFeedbackTaskList(userID, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取反馈任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(integralRes.UserTaskListResponse{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

package example

import (
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/example"
	"github.com/flipped-aurora/gin-vue-admin/server/model/example/request"
	exampleRes "github.com/flipped-aurora/gin-vue-admin/server/model/example/response"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

type FileUploadAndDownloadApi struct{}

// UploadFile
// @Tags      ExaFileUploadAndDownload
// @Summary   上传文件示例
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file                                                           true  "上传文件示例"
// @Success   200   {object}  response.Response{data=exampleRes.ExaFileResponse,msg=string}  "上传文件示例,返回包括文件详情"
// @Router    /fileUploadAndDownload/upload [post]
func (b *FileUploadAndDownloadApi) UploadFile(c *gin.Context) {
	var file example.ExaFileUploadAndDownload
	noSave := c.DefaultQuery("noSave", "0")
	_, header, err := c.Request.FormFile("file")
	classId, _ := strconv.Atoi(c.DefaultPostForm("classId", "0"))
	if err != nil {
		global.GVA_LOG.Error("接收文件失败!", zap.Error(err))
		response.FailWithMessage("接收文件失败", c)
		return
	}
	file, err = fileUploadAndDownloadService.UploadFile(header, noSave, classId) // 文件上传后拿到文件路径
	if err != nil {
		global.GVA_LOG.Error("上传文件失败!", zap.Error(err))
		response.FailWithMessage("上传文件失败", c)
		return
	}
	response.OkWithDetailed(exampleRes.ExaFileResponse{File: file}, "上传成功", c)
}

// EditFileName 编辑文件名或者备注
func (b *FileUploadAndDownloadApi) EditFileName(c *gin.Context) {
	var file example.ExaFileUploadAndDownload
	err := c.ShouldBindJSON(&file)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = fileUploadAndDownloadService.EditFileName(file)
	if err != nil {
		global.GVA_LOG.Error("编辑失败!", zap.Error(err))
		response.FailWithMessage("编辑失败", c)
		return
	}
	response.OkWithMessage("编辑成功", c)
}

// DeleteFile
// @Tags      ExaFileUploadAndDownload
// @Summary   删除文件
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     data  body      example.ExaFileUploadAndDownload  true  "传入文件里面id即可"
// @Success   200   {object}  response.Response{msg=string}     "删除文件"
// @Router    /fileUploadAndDownload/deleteFile [post]
func (b *FileUploadAndDownloadApi) DeleteFile(c *gin.Context) {
	var file example.ExaFileUploadAndDownload
	err := c.ShouldBindJSON(&file)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := fileUploadAndDownloadService.DeleteFile(file); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GetFileList
// @Tags      ExaFileUploadAndDownload
// @Summary   分页文件列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.ExaAttachmentCategorySearch                                        true  "页码, 每页大小, 分类id"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "分页文件列表,返回包括列表,总数,页码,每页数量"
// @Router    /fileUploadAndDownload/getFileList [post]
func (b *FileUploadAndDownloadApi) GetFileList(c *gin.Context) {
	var pageInfo request.ExaAttachmentCategorySearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := fileUploadAndDownloadService.GetFileRecordInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// ImportURL
// @Tags      ExaFileUploadAndDownload
// @Summary   导入URL
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     data  body      example.ExaFileUploadAndDownload  true  "对象"
// @Success   200   {object}  response.Response{msg=string}     "导入URL"
// @Router    /fileUploadAndDownload/importURL [post]
func (b *FileUploadAndDownloadApi) ImportURL(c *gin.Context) {
	var file []example.ExaFileUploadAndDownload
	err := c.ShouldBindJSON(&file)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := fileUploadAndDownloadService.ImportURL(&file); err != nil {
		global.GVA_LOG.Error("导入URL失败!", zap.Error(err))
		response.FailWithMessage("导入URL失败", c)
		return
	}
	response.OkWithMessage("导入URL成功", c)
}

// NewUploadFile 新上传接口（无需登录）
// @Tags      ExaFileUploadAndDownload
// @Summary   新上传接口
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file  true  "上传文件"
// @Success   200   {object}  response.Response{data=object,msg=string}  "上传成功"
// @Router    /fileUploadAndDownload/uploadMcpFile [post]
func (b *FileUploadAndDownloadApi) NewUploadFile(c *gin.Context) {
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		global.GVA_LOG.Error("接收文件失败!", zap.Error(err))
		response.FailWithMessage("接收文件失败", c)
		return
	}
	if header.Size > 20*1024*1024 {
		response.FailWithMessage("文件大小不能超过20M", c)
		return
	}
	ext := filepath.Ext(header.Filename)
	newName := uuid.New().String() + ext
	dateStr := time.Now().Format("2006-01-02")
	header.Filename = "file/uploads/mcp/temp/" + dateStr + "/" + newName

	file, err := fileUploadAndDownloadService.UploadFile(header, "0", 0)
	if err != nil {
		global.GVA_LOG.Error("上传文件失败!", zap.Error(err))
		response.FailWithMessage("上传文件失败: "+err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"url": file.Url}, c)
}

// UploadFileNoAuth 无需认证的文件上传接口
// @Tags      ExaFileUploadAndDownload
// @Summary   无需认证的文件上传
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file       formData  file    true   "上传文件"
// @Param     uploadKey  formData  string  false  "自定义文件键名"
// @Param     uploadPath formData  string  false  "自定义上传路径"
// @Success   200        {object}  response.Response{data=object,msg=string}  "上传成功"
// @Router    /fileUploadAndDownload/uploadFileNoAuth [post]
func (b *FileUploadAndDownloadApi) UploadFileAuth(c *gin.Context) {
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		global.GVA_LOG.Error("接收文件失败!", zap.Error(err))
		response.FailWithMessage("接收文件失败", c)
		return
	}

	// 文件大小限制检查
	if header.Size > 20*1024*1024 {
		response.FailWithMessage("文件大小不能超过20M", c)
		return
	}

	// 获取自定义参数
	uploadKey := c.DefaultPostForm("uploadKey", "")
	uploadPath := c.DefaultPostForm("uploadPath", "")

	if uploadKey != global.GVA_CONFIG.System.UploadKey {
		response.FailWithMessage("上传密钥错误", c)
		return
	}
	// 处理文件名和路径
	ext := filepath.Ext(header.Filename)
	newName := uuid.New().String() + ext
	var finalPath string

	// 如果提供了自定义路径，使用自定义路径，否则使用默认路径
	if uploadPath != "" {
		// 确保路径不以 / 开头
		if strings.HasPrefix(uploadPath, "/") {
			uploadPath = uploadPath[1:]
		}

		// 确保路径以 file/uploads/ 开头
		if !strings.HasPrefix(uploadPath, "file/uploads/") {
			uploadPath = "file/uploads/" + uploadPath
		}
		// 确保路径以 / 结尾
		if !strings.HasSuffix(uploadPath, "/") {
			uploadPath = uploadPath + "/"
		}
		finalPath = uploadPath + newName
	} else {
		// 使用默认路径
		dateStr := time.Now().Format("2006-01-02")
		finalPath = "file/uploads/mcp/temp/" + dateStr + "/" + newName
	}

	// 设置文件名为完整路径
	header.Filename = finalPath

	file, err := fileUploadAndDownloadService.UploadFile(header, "0", 0)
	if err != nil {
		global.GVA_LOG.Error("上传文件失败!", zap.Error(err))
		response.FailWithMessage("上传文件失败: "+err.Error(), c)
		return
	}

	response.OkWithData(gin.H{"url": file.Url}, c)
}

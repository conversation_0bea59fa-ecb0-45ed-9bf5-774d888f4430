package authing

import (
	"errors"
	"strings"
	"time"

	systemApi "github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	systemModel "github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service/authing"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/captcha"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var store = captcha.NewDefaultRedisStore()

type AuthApi struct {
	systemApi.BaseApi
}

// RegisterByEmail
// @Tags Authing
// @Summary 使用邮箱注册 (Authing)
// @Description 使用 Authing 服务通过邮箱和密码注册新用户，同时在本地系统创建对应用户，注册成功后自动登录
// @Accept json
// @Produce application/json
// @Param data body authing.RegisterByEmailInput true "用户注册信息"
// @Success 200 {object} response.Response{data=systemRes.LoginResponse,msg=string} "注册并登录成功"
// @Router /auth/registerByEmail [post]
func (a *AuthApi) RegisterByEmail(c *gin.Context) {
	var input authing.RegisterByEmailInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}
	// 检查用户名是否已存在（本地系统）
	if !errors.Is(global.GVA_DB.Where("username = ?", input.Username).First(&systemModel.SysUser{}).Error, gorm.ErrRecordNotFound) {
		response.FailWithMessage("用户名已存在", c)
		return
	}

	// 检查邮箱是否已存在（本地系统）
	if !errors.Is(global.GVA_DB.Where("email = ?", input.Email).First(&systemModel.SysUser{}).Error, gorm.ErrRecordNotFound) {
		response.FailWithMessage("邮箱已被注册", c)
		return
	}

	// 调用服务层进行注册
	user, err := authService.RegisterByEmail(input)
	if err != nil {
		global.GVA_LOG.Error("注册失败!", zap.Error(err))
		response.FailWithMessage("注册失败: "+err.Error(), c)
		return
	}

	// 查找对应的系统用户
	var sysUser systemModel.SysUser
	if err := global.GVA_DB.Where("authing_id = ?", user.AuthingId).First(&sysUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithMessage("用户创建失败", c)
		} else {
			global.GVA_LOG.Error("查找本地用户失败!", zap.Error(err))
			response.FailWithMessage("系统错误", c)
		}
		return
	}

	// 检查用户状态
	if sysUser.Enable != 1 {
		response.FailWithMessage("用户已被禁用", c)
		return
	}

	// 使用 BaseApi 的 TokenNext 方法生成 JWT token 并登录
	a.TokenNext(c, sysUser)

}

// LoginByEmail
// @Tags Authing
// @Summary 使用邮箱登录 (Authing)
// @Description 使用 Authing 服务通过邮箱和密码登录，返回用户信息和 token
// @Accept json
// @Produce application/json
// @Param data body authing.LoginByEmailInput true "登录信息"
// @Success 200 {object} response.Response{data=systemRes.LoginResponse,msg=string} "登录成功"
// @Router /auth/loginByEmail [post]
func (a *AuthApi) LoginByEmail(c *gin.Context) {
	var input authing.LoginByEmailInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	// 调用 Authing 服务进行登录
	authingUser, err := authService.LoginByEmail(input)
	if err != nil {
		global.GVA_LOG.Error("Authing 登录失败!", zap.Error(err))
		response.FailWithMessage("登录失败: "+err.Error(), c)
		return
	}

	// 查找对应的系统用户
	var sysUser systemModel.SysUser
	if err := global.GVA_DB.Where("authing_id = ?", authingUser.Id).First(&sysUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithMessage("用户未在系统中注册", c)
		} else {
			global.GVA_LOG.Error("查找本地用户失败!", zap.Error(err))
			response.FailWithMessage("系统错误", c)
		}
		return
	}

	// 检查用户状态
	if sysUser.Enable != 1 {
		response.FailWithMessage("用户已被禁用", c)
		return
	}

	// 使用 BaseApi 的 TokenNext 方法生成 JWT token
	a.TokenNext(c, sysUser)
}

// RegisterByPhone
// @Tags Authing
// @Summary 使用手机号注册 (Authing)
// @Description 使用 Authing 服务通过手机号和验证码注册新用户，同时在本地系统创建对应用户
// @Accept json
// @Produce application/json
// @Param data body authing.RegisterByPhoneInput true "用户注册信息"
// @Success 200 {object} response.Response{data=system.SysUser,msg=string} "注册成功"
// @Router /auth/registerByPhone [post]
func (a *AuthApi) RegisterByPhone(c *gin.Context) {
	var input authing.RegisterByPhoneInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	// 调用服务层进行注册
	user, err := authService.RegisterByPhone(input)
	if err != nil {
		global.GVA_LOG.Error("注册失败!", zap.Error(err))
		response.FailWithMessage("注册失败: "+err.Error(), c)
		return
	}

	// 使用 BaseApi 的 TokenNext 方法生成 JWT token
	a.TokenNext(c, *user)
}

// LoginByPhonePassword
// @Tags Authing
// @Summary 使用手机号密码登录 (Authing)
// @Description 使用 Authing 服务通过手机号和密码登录，返回用户信息和 token
// @Accept json
// @Produce application/json
// @Param data body authing.LoginByPhonePasswordInput true "登录信息"
// @Success 200 {object} response.Response{data=systemRes.LoginResponse,msg=string} "登录成功"
// @Router /auth/loginByPhonePassword [post]
func (a *AuthApi) LoginByPhonePassword(c *gin.Context) {
	var input authing.LoginByPhonePasswordInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	// 调用 Authing 服务进行登录
	authingUser, err := authService.LoginByPhonePassword(input)
	if err != nil {
		global.GVA_LOG.Error("Authing 登录失败!", zap.Error(err))
		response.FailWithMessage("登录失败: "+err.Error(), c)
		return
	}

	// 查找对应的系统用户
	var sysUser systemModel.SysUser
	if err := global.GVA_DB.Where("authing_id = ?", authingUser.Id).First(&sysUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithMessage("用户未在系统中注册", c)
		} else {
			global.GVA_LOG.Error("查找本地用户失败!", zap.Error(err))
			response.FailWithMessage("系统错误", c)
		}
		return
	}

	// 检查用户状态
	if sysUser.Enable != 1 {
		response.FailWithMessage("用户已被禁用", c)
		return
	}

	// 使用 BaseApi 的 TokenNext 方法生成 JWT token
	a.TokenNext(c, sysUser)
}

// LoginByPhoneCode
// @Tags Authing
// @Summary 使用手机号验证码登录 (Authing)
// @Description 使用 Authing 服务通过手机号和验证码登录，返回用户信息和 token
// @Accept json
// @Produce application/json
// @Param data body authing.LoginByPhoneCodeInput true "登录信息"
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "登录成功"
// @Router /auth/loginByPhoneCode [post]
func (a *AuthApi) LoginByPhoneCode(c *gin.Context) {
	var input authing.LoginByPhoneCodeInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	// 调用 Authing 服务进行登录
	authingUser, err := authService.LoginByPhoneCode(input)
	if err != nil {
		global.GVA_LOG.Error("Authing 登录失败!", zap.Error(err))
		response.FailWithMessage("登录失败: "+err.Error(), c)
		return
	}

	// 查找对应的系统用户
	var sysUser systemModel.SysUser
	if err := global.GVA_DB.Where("authing_id = ?", authingUser.Id).First(&sysUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithMessage("用户未在系统中注册", c)
		} else {
			global.GVA_LOG.Error("查找本地用户失败!", zap.Error(err))
			response.FailWithMessage("系统错误", c)
		}
		return
	}

	// 检查用户状态
	if sysUser.Enable != 1 {
		response.FailWithMessage("用户已被禁用", c)
		return
	}

	// 返回成功响应，包含用户信息和 Authing token
	response.OkWithDetailed(gin.H{
		"user": gin.H{
			"id":          sysUser.ID,
			"uuid":        sysUser.UUID,
			"username":    sysUser.Username,
			"nickname":    sysUser.NickName,
			"headerImg":   sysUser.HeaderImg,
			"authorityId": sysUser.AuthorityId,
			"email":       sysUser.Email,
			"phone":       sysUser.Phone,
			"enable":      sysUser.Enable,
		},
		"token": authingUser.Token,
	}, "登录成功", c)
}

// SendSmsCode
// @Tags Authing
// @Summary 发送短信验证码 (Authing)
// @Description 使用 Authing 服务发送短信验证码
// @Accept json
// @Produce application/json
// @Param phone query string true "手机号"
// @Success 200 {object} response.Response{msg=string} "发送成功"
// @Router /auth/sendSmsCode [post]
func (a *AuthApi) SendSmsCode(c *gin.Context) {
	phone := c.Query("phone")
	if phone == "" {
		response.FailWithMessage("手机号不能为空", c)
		return
	}

	// 调用 Authing 服务发送验证码
	err := authService.SendSmsCode(phone)
	if err != nil {
		global.GVA_LOG.Error("发送验证码失败!", zap.Error(err))
		response.FailWithMessage("发送验证码失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("发送成功", c)
}

// RegisterByUsername
// @Tags Authing
// @Summary 使用用户名注册 (Authing)
// @Description 使用 Authing 服务通过用户名和密码注册新用户，同时在本地系统创建对应用户，注册成功后自动登录
// @Accept json
// @Produce application/json
// @Param data body authing.RegisterByUsernameInput true "用户注册信息"
// @Success 200 {object} response.Response{data=systemRes.LoginResponse,msg=string} "注册并登录成功"
// @Router /auth/registerByUsername [post]
func (a *AuthApi) RegisterByUsername(c *gin.Context) {
	var input authing.RegisterByUsernameInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	key := c.ClientIP()
	// 判断验证码是否开启
	openCaptcha := global.GVA_CONFIG.Captcha.OpenCaptcha               // 是否开启防爆次数
	openCaptchaTimeOut := global.GVA_CONFIG.Captcha.OpenCaptchaTimeOut // 缓存超时时间
	v, ok := global.BlackCache.Get(key)
	if !ok {
		global.BlackCache.Set(key, 1, time.Second*time.Duration(openCaptchaTimeOut))
	}
	var oc bool = openCaptcha == 0 || openCaptcha < systemApi.InterfaceToInt(v)
	if !oc || (input.CaptchaId != "" && input.Captcha != "" && store.Verify(input.CaptchaId, input.Captcha, true)) {
		// 调用服务层进行注册
		user, err := authService.RegisterByUsername(input)
		if err != nil {
			global.GVA_LOG.Error("注册失败!", zap.Error(err))
			response.FailWithMessage("注册失败: "+err.Error(), c)
			return
		}

		// 使用 BaseApi 的 TokenNext 方法生成 JWT token 并登录
		a.TokenNext(c, *user)
		return
	}
	// 验证码次数+1
	global.BlackCache.Increment(key, 1)
	response.FailWithMessage("验证码错误", c)

}

// LoginByUsername
// @Tags Authing
// @Summary 使用用户名登录 (Authing)
// @Description 使用 Authing 服务通过用户名和密码登录，返回用户信息和 token
// @Accept json
// @Produce application/json
// @Param data body authing.LoginByUsernameInput true "登录信息"
// @Success 200 {object} response.Response{data=systemRes.LoginResponse,msg=string} "登录成功"
// @Router /auth/loginByUsername [post]
func (a *AuthApi) LoginByUsername(c *gin.Context) {
	var input authing.LoginByUsernameInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	key := c.ClientIP()
	// 判断验证码是否开启
	openCaptcha := global.GVA_CONFIG.Captcha.OpenCaptcha               // 是否开启防爆次数
	openCaptchaTimeOut := global.GVA_CONFIG.Captcha.OpenCaptchaTimeOut // 缓存超时时间
	v, ok := global.BlackCache.Get(key)
	if !ok {
		global.BlackCache.Set(key, 1, time.Second*time.Duration(openCaptchaTimeOut))
	}
	var oc bool = openCaptcha == 0 || openCaptcha < systemApi.InterfaceToInt(v)
	if !oc || (input.CaptchaId != "" && input.Captcha != "" && store.Verify(input.CaptchaId, input.Captcha, true)) {
		// 调用 Authing 服务进行登录
		authingUser, err := authService.LoginByUsername(input)
		if err != nil {
			global.GVA_LOG.Error("Authing 登录失败!", zap.Error(err))
			response.FailWithMessage("登录失败: "+err.Error(), c)
			return
		}

		// 查找对应的系统用户
		var sysUser systemModel.SysUser
		if err := global.GVA_DB.Where("authing_id = ?", authingUser.Id).First(&sysUser).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				response.FailWithMessage("用户未在系统中注册", c)
			} else {
				global.GVA_LOG.Error("查找本地用户失败!", zap.Error(err))
				response.FailWithMessage("系统错误", c)
			}
			return
		}

		// 检查用户状态
		if sysUser.Enable != 1 {
			response.FailWithMessage("用户已被禁用", c)
			return
		}

		// 使用 BaseApi 的 TokenNext 方法生成 JWT token
		a.TokenNext(c, sysUser)
		return
	}
	// 验证码次数+1
	global.BlackCache.Increment(key, 1)
	response.FailWithMessage("验证码错误", c)
}

// HandleAuthingCallback
// @Tags Authing
// @Summary 处理 Authing 回调 (Authing)
// @Description 处理 Authing 回调，包括 code 换 token 和获取用户信息，返回用户信息和 token
// @Accept json
// @Produce application/json
// @Param code query string true "Authing 回调 code"
// @Success 200 {object} response.Response{data=systemRes.LoginResponse,msg=string} "登录成功"
// @Router /auth/callback [get]
func (a *AuthApi) HandleAuthingCallback(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		response.FailWithMessage("缺少必要的code参数", c)
		return
	}

	// 调用服务层处理回调
	sysUser, err := authService.HandleAuthingCallback(code)
	if err != nil {
		global.GVA_LOG.Error("处理Authing回调失败!", zap.Error(err))
		// 检查是否包含特定的错误信息
		if strings.Contains(err.Error(), "invalid_grant") {
			response.FailWithMessage("授权码无效或已过期，请重新登录", c)
			return
		}
		response.FailWithMessage("处理回调失败: "+err.Error(), c)
		return
	}

	// 检查用户状态
	if sysUser.Enable != 1 {
		response.FailWithMessage("用户已被禁用", c)
		return
	}

	// 使用 BaseApi 的 TokenNext 方法生成 JWT token
	a.TokenNext(c, *sysUser)
}

// LoginByUserInfo
// @Tags Authing
// @Summary 使用用户信息登录 (Authing)
// @Description 使用 Authing 服务通过用户信息登录，返回用户信息和 token
// @Accept json
// @Produce application/json
// @Param data body authing.LoginByUserInfoInput true "用户信息"
// @Success 200 {object} response.Response{data=systemRes.LoginResponse,msg=string} "登录成功"
// @Router /auth/loginByUserInfo [post]
func (a *AuthApi) LoginByUserInfo(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		response.FailWithMessage("缺少token参数", c)
		return
	}

	// 调用服务层进行登录
	sysUser, err := authService.LoginByUserInfo(token)
	if err != nil {
		global.GVA_LOG.Error("登录失败!", zap.Error(err))
		response.FailWithMessage("登录失败: "+err.Error(), c)
		return
	}
	sysUser.AuthingToken = token
	_ = global.GVA_REDIS.Set(c.Request.Context(), "authing_token:"+sysUser.GetAuthingId(), sysUser.AuthingToken, 0).Err()

	// 使用 BaseApi 的 TokenNext 方法生成 JWT token
	a.TokenNext(c, *sysUser)
}

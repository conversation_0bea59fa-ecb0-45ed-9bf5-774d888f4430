package authing

import (
	systemApi "github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	authingReq "github.com/flipped-aurora/gin-vue-admin/server/model/authing/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service/authing"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AuthV3Api struct {
	systemApi.BaseApi // 继承 BaseApi 以便可能复用 TokenNext 等方法
}

// Register
// @Tags AuthingV3
// @Summary 通用注册接口 (Authing V3)
// @Description 根据 Connection (PASSWORD/CODE) 使用 Authing 服务注册新用户，同时在本地系统创建对应用户。目前仅支持邮箱密码注册(PASSWORD)。
// @Accept json
// @Produce application/json
// @Param data body authing.RegisterInput true "用户注册信息 (Connection, Email, Password 等)"
// @Success 200 {object} response.Response{data=system.SysUser,msg=string} "注册成功"
// @Router /v3/auth/register [post]
func (a *AuthV3Api) Register(c *gin.Context) {
	var input authing.RegisterInput
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	// 目前 V3 版本注册
	if input.Connection != "PASSWORD" || input.Connection != "PASSCODE" {
		response.FailWithMessage("请求参数错误", c)
		return
	}

	// 调用服务层进行注册
	user, err := authV3Service.Register(input)
	if err != nil {
		global.GVA_LOG.Error("注册失败!", zap.Error(err))
		response.FailWithMessage("注册失败: "+err.Error(), c)
		return
	}
	// 返回注册成功的用户信息 (不含敏感信息)
	response.OkWithDetailed(user, "注册成功", c)
}

// 绑定手机号
// @Tags AuthingV3
// @Summary 绑定手机号
// @Description 绑定手机号
// @Accept json
// @Produce application/json
// @Param data body authing.BindPhoneInput true "绑定手机号信息"
// @Success 200 {object} response.Response{data=system.SysUser,msg=string} "绑定成功"
// @Router /v3/auth/bindPhone [post]
func (a *AuthV3Api) BindPhone(c *gin.Context) {
	var input systemReq.BindPhoneReq
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	authingId := utils.GetUserAuthingId(c)
	// 调用服务层进行绑定
	err := authV3Service.BindPhone(authingId, input.Phone, input.Code)
	if err != nil {
		global.GVA_LOG.Error("绑定失败!", zap.Error(err))
		response.FailWithMessage("绑定失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("绑定成功", c)
}

// 修改密码
// @Tags AuthingV3
// @Summary 修改密码
// @Description 修改密码
// @Accept json
// @Produce application/json
// @Param data body authing.ChangePasswordReq true "修改密码信息"
// @Success 200 {object} response.Response{data=system.SysUser,msg=string} "修改密码成功"
// @Router /v3/auth/changePassword [post]
func (a *AuthV3Api) UpdatePassword(c *gin.Context) {
	var req systemReq.ChangePasswordReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}
	authingId := utils.GetUserAuthingId(c)
	uid := utils.GetUserID(c)
	req.AuthingId = authingId
	req.ID = uid
	err := authV3Service.UpdatePassword(req)
	if err != nil {
		global.GVA_LOG.Error("修改密码失败!", zap.Error(err))
		response.FailWithMessage("修改密码失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("修改成功", c)
}

// 验证更新手机号请求
// @Tags AuthingV3
// @Summary 验证更新手机号请求
// @Description 验证更新手机号请求
// @Accept json
// @Produce application/json
// @Param data body authing.VerifyUpdatePhoneRequest true "验证更新手机号请求信息"
// @Success 200 {object} response.Response{data=system.SysUser,msg=string} "验证成功"
// @Router /v3/auth/VerifyUpdatePhoneRequest [post]
func (a *AuthV3Api) VerifyUpdatePhoneRequest(c *gin.Context) {
	var input authingReq.VerifyUpdatePhoneRequest
	if err := c.ShouldBindJSON(&input); err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}
	authingId := utils.GetUserAuthingId(c)
	if authingId == "" {
		response.FailWithMessage("请求参数错误", c)
		return
	}
	input.AuthingId = authingId
	updatePhoneToken, err := authV3Service.VerifyUpdatePhoneRequest(input)
	if err != nil {
		global.GVA_LOG.Error("验证更新手机号请求失败!", zap.Error(err))
		response.FailWithMessage("验证更新手机号请求失败: "+err.Error(), c)
		return
	}
	response.OkWithData(updatePhoneToken, c)
}

// 更新手机号
// @Tags AuthingV3
// @Summary 更新手机号
// @Description 更新手机号
// @Accept json
// @Produce application/json
// @Param data body authing.UpdatePhoneReq true "更新手机号信息"
// @Success 200 {object} response.Response{data=system.SysUser,msg=string} "更新成功"
// @Router /v3/auth/updatePhone [post]
func (a *AuthV3Api) UpdatePhone(c *gin.Context) {
	updatePhoneToken := c.Query("updatePhoneToken")
	authingId := utils.GetUserAuthingId(c)
	if authingId == "" {
		response.FailWithMessage("请求参数错误", c)
		return
	}
	err := authV3Service.UpdatePhone(authingId, updatePhoneToken)
	if err != nil {
		global.GVA_LOG.Error("更新手机号失败!", zap.Error(err))
		response.FailWithMessage("更新手机号失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

package pansou

import (
	"net/http"
	"strings"

	pansouConfig "github.com/flipped-aurora/gin-vue-admin/server/config/pansou"
	"github.com/flipped-aurora/gin-vue-admin/server/model/pansou"
	pansouService "github.com/flipped-aurora/gin-vue-admin/server/service/pansou"
	pansouUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/pansou"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/pansou/cache"
	pansouJson "github.com/flipped-aurora/gin-vue-admin/server/utils/pansou/json"
	"github.com/gin-gonic/gin"
)

// SearchApi pansou搜索API
type SearchApi struct {
	searchService *pansouService.SearchService
}

// NewSearchApi 创建搜索API实例
func NewSearchApi(searchService *pansouService.SearchService) *SearchApi {
	return &SearchApi{
		searchService: searchService,
	}
}

// SearchHandler 搜索处理函数
// @Tags PanSou
// @Summary 网盘资源搜索
// @Description 通过关键词搜索网盘资源，支持多种网盘类型
// @Accept json
// @Produce json
// @Param request body pansou.SearchRequest true "搜索请求参数"
// @Success 200 {object} pansou.Response{data=pansou.SearchResponse} "搜索成功"
// @Failure 400 {object} pansou.Response "请求参数错误"
// @Failure 500 {object} pansou.Response "服务器内部错误"
// @Router /pansou/search [post]
func (s *SearchApi) SearchHandler(c *gin.Context) {
	// 动态获取搜索服务实例
	if s.searchService == nil {
		c.JSON(http.StatusServiceUnavailable, pansou.NewErrorResponse(503, "PanSou 搜索服务未初始化"))
		return
	}
	var req pansou.SearchRequest
	var err error

	// 根据请求方法不同处理参数
	if c.Request.Method == http.MethodGet {
		// GET方式：从URL参数获取
		req = s.parseGetRequest(c)
	} else {
		// POST方式：从请求体获取
		data, err := c.GetRawData()
		if err != nil {
			c.JSON(http.StatusBadRequest, pansou.NewErrorResponse(400, "读取请求数据失败: "+err.Error()))
			return
		}

		if err := pansouJson.Unmarshal(data, &req); err != nil {
			c.JSON(http.StatusBadRequest, pansou.NewErrorResponse(400, "无效的请求参数: "+err.Error()))
			return
		}
	}

	// 验证请求参数
	if req.Keyword == "" {
		c.JSON(http.StatusBadRequest, pansou.NewErrorResponse(400, "关键词不能为空"))
		return
	}

	// 设置默认值
	s.setDefaults(&req)

	// 执行搜索
	result, err := s.searchService.Search(
		req.Keyword,
		req.Channels,
		req.Concurrency,
		req.ForceRefresh,
		req.ResultType,
		req.SourceType,
		req.Plugins,
		req.CloudTypes,
		req.Ext,
	)

	if err != nil {
		response := pansou.NewErrorResponse(500, "搜索失败: "+err.Error())
		jsonData, _ := pansouJson.Marshal(response)
		c.Data(http.StatusInternalServerError, "application/json", jsonData)
		return
	}

	// 返回结果
	response := pansou.NewSuccessResponse(result)
	jsonData, _ := pansouJson.Marshal(response)
	c.Data(http.StatusOK, "application/json", jsonData)
}

// parseGetRequest 解析GET请求参数
func (s *SearchApi) parseGetRequest(c *gin.Context) pansou.SearchRequest {
	req := pansou.SearchRequest{
		Keyword: c.Query("kw"),
		Ext:     make(map[string]interface{}),
	}

	// 处理channels参数
	channelsStr := c.Query("channels")
	if channelsStr != "" && channelsStr != " " {
		parts := strings.Split(channelsStr, ",")
		for _, part := range parts {
			trimmed := strings.TrimSpace(part)
			if trimmed != "" {
				req.Channels = append(req.Channels, trimmed)
			}
		}
	}

	// 处理并发数
	concStr := c.Query("conc")
	if concStr != "" && concStr != " " {
		req.Concurrency = pansouUtils.StringToInt(concStr)
	}

	// 处理强制刷新
	refreshStr := c.Query("refresh")
	if refreshStr == "true" {
		req.ForceRefresh = true
	}

	// 处理结果类型和来源类型
	req.ResultType = c.DefaultQuery("res", "merge")
	req.SourceType = c.DefaultQuery("src", "all")

	// 处理plugins参数
	if c.Request.URL.Query().Has("plugins") {
		pluginsStr := c.Query("plugins")
		if pluginsStr != "" && pluginsStr != " " {
			parts := strings.Split(pluginsStr, ",")
			for _, part := range parts {
				trimmed := strings.TrimSpace(part)
				if trimmed != "" {
					req.Plugins = append(req.Plugins, trimmed)
				}
			}
		}
	}

	// 处理cloud_types参数
	if c.Request.URL.Query().Has("cloud_types") {
		cloudTypesStr := c.Query("cloud_types")
		if cloudTypesStr != "" && cloudTypesStr != " " {
			parts := strings.Split(cloudTypesStr, ",")
			for _, part := range parts {
				trimmed := strings.TrimSpace(part)
				if trimmed != "" {
					req.CloudTypes = append(req.CloudTypes, trimmed)
				}
			}
		}
	}

	// 处理ext参数
	extStr := c.Query("ext")
	if extStr != "" && extStr != " " {
		if extStr == "{}" {
			req.Ext = make(map[string]interface{})
		} else {
			var ext map[string]interface{}
			if err := pansouJson.Unmarshal([]byte(extStr), &ext); err == nil {
				req.Ext = ext
			}
		}
	}

	return req
}

// setDefaults 设置默认值
func (s *SearchApi) setDefaults(req *pansou.SearchRequest) {
	// 检查并设置默认频道
	if len(req.Channels) == 0 {
		req.Channels = pansouConfig.AppConfig.DefaultChannels
	}

	// 如果未指定结果类型，默认返回merge并转换为merged_by_type
	if req.ResultType == "" {
		req.ResultType = "merged_by_type"
	} else if req.ResultType == "merge" {
		req.ResultType = "merged_by_type"
	}

	// 如果未指定数据来源类型，默认为全部
	if req.SourceType == "" {
		req.SourceType = "all"
	}

	// 参数互斥逻辑：当src=tg时忽略plugins参数，当src=plugin时忽略channels参数
	if req.SourceType == "tg" {
		req.Plugins = nil
	} else if req.SourceType == "plugin" {
		req.Channels = nil
	} else if req.SourceType == "all" {
		if req.Plugins == nil || len(req.Plugins) == 0 {
			req.Plugins = nil
		}
	}

	// 确保ext不为nil
	if req.Ext == nil {
		req.Ext = make(map[string]interface{})
	}
}

// HealthHandler 健康检查处理函数
// @Tags PanSou
// @Summary 健康检查
// @Description 检查PanSou搜索服务状态
// @Accept json
// @Produce json
// @Success 200 {object} gin.H "服务状态正常"
// @Router /pansou/health [get]
func (s *SearchApi) HealthHandler(c *gin.Context) {
	// 获取插件信息
	pluginCount := 0
	pluginNames := []string{}
	if s.searchService != nil && s.searchService.GetPluginManager() != nil {
		plugins := s.searchService.GetPluginManager().GetPlugins()
		pluginCount = len(plugins)
		for _, p := range plugins {
			pluginNames = append(pluginNames, p.Name())
		}
	}

	// 获取缓存状态
	cacheStatus := s.getCacheStatus()

	// TODO: 获取频道信息（需要配置支持）
	channels := []string{"tgsearchers3"} // 默认频道

	c.JSON(200, gin.H{
		"status":          "ok",
		"plugins_enabled": true,
		"plugin_count":    pluginCount,
		"plugins":         pluginNames,
		"channels":        channels,
		"cache":           cacheStatus,
	})
}

// getCacheStatus 获取缓存状态
func (s *SearchApi) getCacheStatus() map[string]interface{} {
	status := map[string]interface{}{
		"enabled": false,
		"type":    "none",
	}

	// 检查Redis缓存
	if redisCache, err := cache.NewPanSouRedisCache(); err == nil {
		stats, err := redisCache.GetCacheStats()
		if err == nil {
			status["enabled"] = true
			status["type"] = "redis"
			status["stats"] = stats
			status["prefix"] = "pansou:kw:"
			return status
		}
	}

	// 检查内存缓存
	if memCache := pansouService.GetEnhancedTwoLevelCache(); memCache != nil {
		status["enabled"] = true
		status["type"] = "memory"
		status["stats"] = map[string]interface{}{
			"status": "active",
		}
	}

	return status
}

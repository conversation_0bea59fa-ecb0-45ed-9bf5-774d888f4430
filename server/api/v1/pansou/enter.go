package pansou

import (
	pansouService "github.com/flipped-aurora/gin-vue-admin/server/service/pansou"
)

// PanSouApiGroup PanSou API 组
type PanSouApiGroup struct {
	SearchApi
}

// 全局搜索服务实例
var globalSearchService *pansouService.SearchService

// SetGlobalSearchService 设置全局搜索服务实例
func SetGlobalSearchService(service *pansouService.SearchService) {
	globalSearchService = service
}

// GetSearchApi 获取SearchApi实例
func (p *PanSouApiGroup) GetSearchApi() *SearchApi {
	return NewSearchApi(globalSearchService)
}

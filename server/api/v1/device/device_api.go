package device

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	deviceReq "github.com/flipped-aurora/gin-vue-admin/server/model/device/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DeviceApi struct{}

// ReportDevice 设备信息上报接口
// @Tags Device
// @Summary 设备信息上报
// @accept application/json
// @Produce application/json
// @Param data body deviceReq.DeviceReportRequest true "设备信息"
// @Success 200 {object} response.Response{data=deviceRes.DeviceReportResponse} "上报成功"
// @Router /device/report [post]
func (deviceApi *DeviceApi) ReportDevice(c *gin.Context) {
	var req deviceReq.DeviceReportRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取客户端IP和User-Agent
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 获取用户ID（如果已登录）
	var userIDPtr *uint
	token := c.GetHeader("X-Token")

	// 如果token存在，尝试解析获取用户ID
	if token != "" {
		j := utils.NewJWT()
		claims, err := j.ParseToken(token)
		if err == nil && claims != nil {
			userID := claims.BaseClaims.ID
			// 只有当userID不为0时才设置指针，这样数据库中会存储实际的用户ID
			if userID != 0 {
				userIDPtr = &userID
			}
		}
		// 注意：如果token解析失败，我们不返回错误，而是继续处理（userIDPtr保持为nil）
		// 这样可以支持匿名用户上报设备信息
	}
	// 如果token为空或解析失败，userIDPtr为nil，数据库中会存储NULL

	result, err := deviceService.ReportDevice(&req, ipAddress, userAgent, userIDPtr)
	if err != nil {
		global.GVA_LOG.Error("设备上报失败!", zap.Error(err))
		response.FailWithMessage("设备上报失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "设备上报成功", c)
}

// GetDeviceList 获取设备列表
// @Tags Device
// @Summary 获取设备列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body deviceReq.DeviceSearchRequest true "查询参数"
// @Success 200 {object} response.Response{data=deviceRes.DeviceListResponse} "获取成功"
// @Router /device/list [post]
func (deviceApi *DeviceApi) GetDeviceList(c *gin.Context) {
	var req deviceReq.DeviceSearchRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 验证分页参数
	err = utils.Verify(req.PageInfo, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := deviceService.GetDeviceList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取设备列表失败!", zap.Error(err))
		response.FailWithMessage("获取设备列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(gin.H{
		"list":     result.List,
		"total":    result.Total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, "获取设备列表成功", c)
}

// GetDeviceDetail 获取设备详情
// @Tags Device
// @Summary 获取设备详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param deviceId query string true "设备ID"
// @Success 200 {object} response.Response{data=deviceRes.DeviceDetailResponse} "获取成功"
// @Router /device/detail [get]
func (deviceApi *DeviceApi) GetDeviceDetail(c *gin.Context) {
	deviceID := c.Query("deviceId")
	if deviceID == "" {
		response.FailWithMessage("设备ID不能为空", c)
		return
	}

	result, err := deviceService.GetDeviceDetail(deviceID)
	if err != nil {
		global.GVA_LOG.Error("获取设备详情失败!", zap.Error(err))
		response.FailWithMessage("获取设备详情失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "获取设备详情成功", c)
}

// GetMyDevices 获取当前用户的设备列表
// @Tags Device
// @Summary 获取当前用户的设备列表（不分页）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=deviceRes.DeviceListResponse} "获取成功"
// @Router /device/my [get]
func (deviceApi *DeviceApi) GetMyDevices(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	result, err := deviceService.GetMyDevices(userID)
	if err != nil {
		global.GVA_LOG.Error("获取我的设备列表失败!", zap.Error(err))
		response.FailWithMessage("获取我的设备列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(gin.H{
		"list":  result.List,
		"total": result.Total,
	}, "获取我的设备列表成功", c)
}

// GetDeviceReportList 获取设备上报记录列表
// @Tags Device
// @Summary 获取设备上报记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body deviceReq.DeviceReportSearchRequest true "查询参数"
// @Success 200 {object} response.Response{data=deviceRes.DeviceReportListResponse} "获取成功"
// @Router /device/report/list [post]
func (deviceApi *DeviceApi) GetDeviceReportList(c *gin.Context) {
	var req deviceReq.DeviceReportSearchRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 验证分页参数
	err = utils.Verify(req.PageInfo, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := deviceService.GetDeviceReportList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取设备上报记录列表失败!", zap.Error(err))
		response.FailWithMessage("获取设备上报记录列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(gin.H{
		"list":     result.List,
		"total":    result.Total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, "获取设备上报记录列表成功", c)
}

// AssignDefaultAgent 分配默认智能体
// @Tags Device
// @Summary 获取mcp接入点地址
// @accept application/json
// @Produce application/json
// @Param data body deviceReq.AssignDefaultAgentRequest true "用户ID"
// @Success 200 {object} response.Response{data=deviceRes.AssignDefaultAgentResponse} "分配成功"
// @Router /device/assign-default-agent [post]
func (deviceApi *DeviceApi) AssignDefaultAgent(c *gin.Context) {

	userId := utils.GetUserID(c)
	userIdStr := strconv.Itoa(int(userId))
	deviceID := c.GetHeader("Device-ID")
	result, err := deviceService.AssignDefaultAgent(userIdStr, deviceID)
	if err != nil {
		global.GVA_LOG.Error("分配默认智能体失败!", zap.Error(err))
		response.FailWithMessage("分配默认智能体失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// GetIpLocation 获取IP归属地
// @Tags Device
// @Summary 获取IP归属地
// @accept application/json
// @Produce application/json
// @Param
// @Success 200 {object} response.Response{data=deviceRes.IpLocationResponse} "查询成功"
// @Router /device/ipLocation [get]
func (deviceApi *DeviceApi) GetIpLocation(c *gin.Context) {

	ip := c.Query("ip")
	if ip == "" {
		ip = c.ClientIP()
		// 如果是本地IP，使用一个公网IP进行测试
		if ip == "127.0.0.1" || ip == "::1" || ip == "localhost" {
			response.FailWithMessage("无法获取有效的IP地址", c)
		}
	}

	// 验证IP地址格式
	if ip == "" {
		response.FailWithMessage("无法获取有效的IP地址", c)
		return
	}

	result, err := deviceService.GetIpLocation(ip)
	if err != nil {
		global.GVA_LOG.Error("IP归属地查询失败!", zap.Error(err))
		response.FailWithMessage("IP归属地查询失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "IP归属地查询成功", c)
}

func (deviceApi *DeviceApi) UpdateDeviceRedis(c *gin.Context) {
	userIDStr := c.Query("userId")
	if userIDStr == "" {
		response.FailWithMessage("用户ID不能为空", c)
		return
	}

	// 将字符串转换为uint
	userIDUint64, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("用户ID格式错误", c)
		return
	}

	userID := uint(userIDUint64)
	err = deviceService.UpdateDeviceRedis(&userID)
	if err != nil {
		global.GVA_LOG.Error("更新设备缓存失败!", zap.Error(err))
		response.FailWithMessage("更新设备缓存失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新设备信息缓存成功", c)
}

// UpdateDeviceName 修改设备名称
// @Tags Device
// @Summary 修改设备名称
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body deviceReq.UpdateDeviceNameRequest true "修改设备名称请求"
// @Success 200 {object} response.Response{data=deviceRes.UpdateDeviceNameResponse} "修改成功"
// @Router /device/updateDeviceName [post]
func (deviceApi *DeviceApi) UpdateDeviceName(c *gin.Context) {
	var req deviceReq.UpdateDeviceNameRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 验证请求参数
	if req.DeviceID == "" {
		response.FailWithMessage("设备ID不能为空", c)
		return
	}
	if req.DeviceName == "" {
		response.FailWithMessage("设备名称不能为空", c)
		return
	}

	// 调用服务层方法
	result, err := deviceService.UpdateDeviceName(&req, userID)
	if err != nil {
		global.GVA_LOG.Error("修改设备名称失败!", zap.Error(err))
		response.FailWithMessage("修改设备名称失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "修改设备名称成功", c)
}

// DeviceLogout 指定设备登出
// @Tags Device
// @Summary 指定设备登出
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body deviceReq.DeviceLogoutRequest true "设备登出请求"
// @Success 200 {object} response.Response{data=deviceRes.DeviceLogoutResponse} "登出成功"
// @Router /device/deviceLogout [post]
func (deviceApi *DeviceApi) DeviceLogout(c *gin.Context) {
	var req deviceReq.DeviceLogoutRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前设备ID（从请求头）
	currentDeviceID := c.GetHeader("Device-ID")

	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 验证请求参数
	if req.DeviceID == "" {
		response.FailWithMessage("设备ID不能为空", c)
		return
	}

	// 不能登出当前设备
	if currentDeviceID != "" && req.DeviceID == currentDeviceID {
		response.FailWithMessage("不能登出当前设备", c)
		return
	}

	// 调用服务层方法
	result, err := deviceService.DeviceLogout(&req, userID)
	if err != nil {
		global.GVA_LOG.Error("设备登出失败!", zap.Error(err))
		response.FailWithMessage("设备登出失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "设备登出成功", c)
}

package device

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
	deviceReq "github.com/flipped-aurora/gin-vue-admin/server/model/device/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DeviceVersionRecordApi struct{}

// CheckUpdate 检查设备版本更新
// @Tags DeviceVersionRecord
// @Summary 检查设备版本更新
// @Accept application/json
// @Produce application/json
// @Param Device-Id header string true "设备ID"
// @Param data body deviceReq.CheckUpdateRequest true "检查更新请求"
// @Success 200 {object} response.Response{data=deviceRes.CheckUpdateResponse,msg=string} "检查成功"
// @Router /deviceVersionRecord/checkUpdate [post]
func (deviceVersionRecordApi *DeviceVersionRecordApi) CheckUpdate(c *gin.Context) {
	ctx := c.Request.Context()

	// 从请求头获取设备ID
	deviceId := c.GetHeader("Device-Id")
	if deviceId == "" {
		response.FailWithMessage("请求头中缺少Device-Id", c)
		return
	}

	var req deviceReq.CheckUpdateRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	res, err := deviceVersionRecordService.CheckUpdate(ctx, deviceId, req)
	if err != nil {
		global.GVA_LOG.Error("检查更新失败!", zap.Error(err))
		response.FailWithMessage("检查更新失败:"+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}

// RecordUpdate 记录设备版本更新
// @Tags DeviceVersionRecord
// @Summary 记录设备版本更新
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param Device-Id header string true "设备ID"
// @Param data body deviceReq.RecordUpdateRequest true "记录更新请求"
// @Success 200 {object} response.Response{msg=string} "记录成功"
// @Router /deviceVersionRecord/recordUpdate [post]
func (deviceVersionRecordApi *DeviceVersionRecordApi) RecordUpdate(c *gin.Context) {
	ctx := c.Request.Context()

	// 从请求头获取设备ID
	deviceId := c.GetHeader("Device-Id")
	if deviceId == "" {
		response.FailWithMessage("请求头中缺少Device-Id", c)
		return
	}

	var req deviceReq.RecordUpdateRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = deviceVersionRecordService.RecordUpdate(ctx, deviceId, req)
	if err != nil {
		global.GVA_LOG.Error("记录更新失败!", zap.Error(err))
		response.FailWithMessage("记录更新失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("记录更新成功", c)
}

// GetDeviceVersionRecordList 分页获取设备版本更新记录列表
// @Tags DeviceVersionRecord
// @Summary 分页获取设备版本更新记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query deviceReq.DeviceVersionRecordSearch true "分页获取设备版本更新记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /deviceVersionRecord/getDeviceVersionRecordList [get]
func (deviceVersionRecordApi *DeviceVersionRecordApi) GetDeviceVersionRecordList(c *gin.Context) {
	ctx := c.Request.Context()

	var pageInfo deviceReq.DeviceVersionRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := deviceVersionRecordService.GetDeviceVersionRecordList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// FindDeviceVersionRecord 用id查询设备版本更新记录
// @Tags DeviceVersionRecord
// @Summary 用id查询设备版本更新记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询设备版本更新记录"
// @Success 200 {object} response.Response{data=device.DeviceVersionRecord,msg=string} "查询成功"
// @Router /deviceVersionRecord/findDeviceVersionRecord [get]
func (deviceVersionRecordApi *DeviceVersionRecordApi) FindDeviceVersionRecord(c *gin.Context) {
	ctx := c.Request.Context()

	ID := c.Query("ID")
	record, err := deviceVersionRecordService.GetDeviceVersionRecord(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}

	response.OkWithData(record, c)
}

// UpdateDeviceVersionRecord 更新设备版本更新记录
// @Tags DeviceVersionRecord
// @Summary 更新设备版本更新记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body device.DeviceVersionRecord true "更新设备版本更新记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /deviceVersionRecord/updateDeviceVersionRecord [put]
func (deviceVersionRecordApi *DeviceVersionRecordApi) UpdateDeviceVersionRecord(c *gin.Context) {
	ctx := c.Request.Context()

	var record device.DeviceVersionRecord
	err := c.ShouldBindJSON(&record)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = deviceVersionRecordService.UpdateDeviceVersionRecord(ctx, record)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteDeviceVersionRecord 删除设备版本更新记录
// @Tags DeviceVersionRecord
// @Summary 删除设备版本更新记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "删除设备版本更新记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /deviceVersionRecord/deleteDeviceVersionRecord [delete]
func (deviceVersionRecordApi *DeviceVersionRecordApi) DeleteDeviceVersionRecord(c *gin.Context) {
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := deviceVersionRecordService.DeleteDeviceVersionRecord(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

package product

import (
	"strconv"

	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/product/request"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserAddressApi struct{}

// CreateUserAddress 创建用户地址
// @Tags UserAddress
// @Summary 创建用户地址
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.CreateUserAddressRequest true "地址信息"
// @Success 200 {object} response.Response{data=response.UserAddressResponse,msg=string} "创建成功"
// @Router /userAddress/create [post]
func (u *UserAddressApi) CreateUserAddress(c *gin.Context) {
	var req systemReq.CreateUserAddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	address, err := userAddressService.CreateUserAddress(userID, req)
	if err != nil {
		global.GVA_LOG.Error("创建地址失败!", zap.Error(err))
		response.FailWithMessage("创建地址失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(address, "创建成功", c)
}

// UpdateUserAddress 更新用户地址
// @Tags UserAddress
// @Summary 更新用户地址
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.UpdateUserAddressRequest true "地址信息"
// @Success 200 {object} response.Response{data=response.UserAddressResponse,msg=string} "更新成功"
// @Router /userAddress/update [put]
func (u *UserAddressApi) UpdateUserAddress(c *gin.Context) {
	var req systemReq.UpdateUserAddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	address, err := userAddressService.UpdateUserAddress(userID, req)
	if err != nil {
		global.GVA_LOG.Error("更新地址失败!", zap.Error(err))
		response.FailWithMessage("更新地址失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(address, "更新成功", c)
}

// GetUserAddressList 获取用户地址列表
// @Tags UserAddress
// @Summary 获取用户地址列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.UserAddressSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.UserAddressListResponse,msg=string} "获取成功"
// @Router /userAddress/list [get]
func (u *UserAddressApi) GetUserAddressList(c *gin.Context) {
	var req systemReq.UserAddressSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	list, err := userAddressService.GetUserAddressList(userID, req)
	if err != nil {
		global.GVA_LOG.Error("获取地址列表失败!", zap.Error(err))
		response.FailWithMessage("获取地址列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GetUserAddress 获取单个地址
// @Tags UserAddress
// @Summary 获取单个地址
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id path uint true "地址ID"
// @Success 200 {object} response.Response{data=response.UserAddressResponse,msg=string} "获取成功"
// @Router /userAddress/{id} [get]
func (u *UserAddressApi) GetUserAddress(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的地址ID", c)
		return
	}

	userID := utils.GetUserID(c)
	address, err := userAddressService.GetUserAddress(userID, uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取地址失败!", zap.Error(err))
		response.FailWithMessage("获取地址失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(address, "获取成功", c)
}

// DeleteUserAddress 删除地址
// @Tags UserAddress
// @Summary 删除地址
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query uint true "地址ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /userAddress/deleteById [delete]
func (u *UserAddressApi) DeleteUserAddress(c *gin.Context) {
	idStr := c.Query("id")
	if idStr == "" {
		response.FailWithMessage("地址ID不能为空", c)
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的地址ID", c)
		return
	}

	userID := utils.GetUserID(c)
	err = userAddressService.DeleteUserAddress(userID, uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除地址失败!", zap.Error(err))
		response.FailWithMessage("删除地址失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// SetDefaultAddress 设置默认地址
// @Tags UserAddress
// @Summary 设置默认地址
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.SetDefaultAddressRequest true "地址ID"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /userAddress/setDefault [post]
func (u *UserAddressApi) SetDefaultAddress(c *gin.Context) {
	var req systemReq.SetDefaultAddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	err := userAddressService.SetDefaultAddress(userID, req)
	if err != nil {
		global.GVA_LOG.Error("设置默认地址失败!", zap.Error(err))
		response.FailWithMessage("设置默认地址失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("设置成功", c)
}

// GetDefaultAddress 获取默认地址
// @Tags UserAddress
// @Summary 获取默认地址
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.UserAddressResponse,msg=string} "获取成功"
// @Router /userAddress/default [get]
func (u *UserAddressApi) GetDefaultAddress(c *gin.Context) {
	userID := utils.GetUserID(c)
	address, err := userAddressService.GetDefaultAddress(userID)
	if err != nil {
		global.GVA_LOG.Error("获取默认地址失败!", zap.Error(err))
		response.FailWithMessage("获取默认地址失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(address, "获取成功", c)
}

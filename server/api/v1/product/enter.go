package product

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	ProductApi
	MembershipApi
	UserAddressApi
}

var (
	productService     = service.ServiceGroupApp.ProductServiceGroup.ProductService
	purchaseService    = service.ServiceGroupApp.ProductServiceGroup.PurchaseService
	membershipService  = service.ServiceGroupApp.ProductServiceGroup.MembershipService
	userAddressService = service.ServiceGroupApp.ProductServiceGroup.UserAddressService
)

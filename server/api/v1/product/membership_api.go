package product

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	productReq "github.com/flipped-aurora/gin-vue-admin/server/model/product/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MembershipApi struct{}

var MembershipApiApp = new(MembershipApi)

// GetUserMembershipStatus 获取用户会员状态
// @Tags Membership
// @Summary 获取用户会员状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.UserMembershipStatusResponse,msg=string} "获取成功"
// @Router /membership/status [get]
func (m *MembershipApi) GetUserMembershipStatus(c *gin.Context) {
	userID := utils.GetUserID(c)

	status, err := membershipService.GetUserMembershipStatus(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户会员状态失败!", zap.Error(err))
		response.FailWithMessage("获取会员状态失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(status, "获取成功", c)
}

// GetUserMembershipList 获取用户会员列表
// @Tags Membership
// @Summary 获取用户会员列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.UserMembershipSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.UserMembershipListResponse,msg=string} "获取成功"
// @Router /membership/list [get]
func (m *MembershipApi) GetUserMembershipList(c *gin.Context) {
	var req productReq.UserMembershipSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 如果没有指定用户ID，则查询当前用户的会员记录
	if req.UserID == nil {
		userID := utils.GetUserID(c)
		req.UserID = &userID
	}

	list, err := membershipService.GetUserMembershipList(req)
	if err != nil {
		global.GVA_LOG.Error("获取用户会员列表失败!", zap.Error(err))
		response.FailWithMessage("获取会员列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GetAllMembershipList 管理员获取所有会员列表
// @Tags Membership
// @Summary 管理员获取所有会员列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.UserMembershipSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.UserMembershipListResponse,msg=string} "获取成功"
// @Router /membership/admin/list [get]
func (m *MembershipApi) GetAllMembershipList(c *gin.Context) {
	var req productReq.UserMembershipSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, err := membershipService.GetUserMembershipList(req)
	if err != nil {
		global.GVA_LOG.Error("获取会员列表失败!", zap.Error(err))
		response.FailWithMessage("获取会员列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GrantMonthlyPoints 手动发放月度积分（管理员接口）
// @Tags Membership
// @Summary 手动发放月度积分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "发放成功"
// @Router /membership/admin/grant-points [post]
func (m *MembershipApi) GrantMonthlyPoints(c *gin.Context) {
	err := membershipService.GrantMonthlyPoints()
	if err != nil {
		global.GVA_LOG.Error("发放月度积分失败!", zap.Error(err))
		response.FailWithMessage("发放积分失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("发放成功", c)
}

// UpdateExpiredMemberships 手动更新过期会员状态（管理员接口）
// @Tags Membership
// @Summary 手动更新过期会员状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /membership/admin/update-expired [post]
func (m *MembershipApi) UpdateExpiredMemberships(c *gin.Context) {
	err := membershipService.UpdateExpiredMemberships()
	if err != nil {
		global.GVA_LOG.Error("更新过期会员状态失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

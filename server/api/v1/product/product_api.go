package product

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	productReq "github.com/flipped-aurora/gin-vue-admin/server/model/product/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductApi struct{}

// CreateProduct 创建商品
// @Tags Product
// @Summary 创建商品
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.CreateProductRequest true "商品信息"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /product/create [post]
func (p *ProductApi) CreateProduct(c *gin.Context) {
	var req productReq.CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	product, err := productService.CreateProduct(req)
	if err != nil {
		global.GVA_LOG.Error("创建商品失败!", zap.Error(err))
		response.FailWithMessage("创建商品失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(product, "创建成功", c)
}

// UpdateProduct 更新商品
// @Tags Product
// @Summary 更新商品
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.UpdateProductRequest true "商品信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /product/update [put]
func (p *ProductApi) UpdateProduct(c *gin.Context) {
	var req productReq.UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	product, err := productService.UpdateProduct(req)
	if err != nil {
		global.GVA_LOG.Error("更新商品失败!", zap.Error(err))
		response.FailWithMessage("更新商品失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(product, "更新成功", c)
}

// GetProductList 获取商品列表
// @Tags Product
// @Summary 获取商品列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.ProductSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.ProductListResponse,msg=string} "获取成功"
// @Router /product/list [get]
func (p *ProductApi) GetProductList(c *gin.Context) {
	var req productReq.ProductSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, err := productService.GetProductList(req)
	if err != nil {
		global.GVA_LOG.Error("获取商品列表失败!", zap.Error(err))
		response.FailWithMessage("获取商品列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GetProduct 获取单个商品
// @Tags Product
// @Summary 获取单个商品
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id path uint true "商品ID"
// @Success 200 {object} response.Response{data=response.ProductResponse,msg=string} "获取成功"
// @Router /product/{id} [get]
func (p *ProductApi) GetProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的商品ID", c)
		return
	}

	product, err := productService.GetProduct(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取商品失败!", zap.Error(err))
		response.FailWithMessage("获取商品失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(product, "获取成功", c)
}

// DeleteProduct 删除商品
// @Tags Product
// @Summary 删除商品
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id path uint true "商品ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /product/{id} [delete]
func (p *ProductApi) DeleteProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的商品ID", c)
		return
	}

	err = productService.DeleteProduct(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除商品失败!", zap.Error(err))
		response.FailWithMessage("删除商品失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetMembershipProducts 获取会员商品列表
// @Tags Product
// @Summary 获取会员商品列表
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]response.ProductResponse,msg=string} "获取成功"
// @Router /product/membership [get]
func (p *ProductApi) GetMembershipProducts(c *gin.Context) {
	products, err := productService.GetMembershipProducts()
	if err != nil {
		global.GVA_LOG.Error("获取会员商品失败!", zap.Error(err))
		response.FailWithMessage("获取会员商品失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(products, "获取成功", c)
}

// BuyProduct 购买商品
// @Tags Product
// @Summary 购买商品
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.BuyProductRequest true "购买信息"
// @Success 200 {object} response.Response{data=response.BuyProductResponse,msg=string} "购买成功"
// @Router /product/buy [post]
func (p *ProductApi) BuyProduct(c *gin.Context) {
	var req productReq.BuyProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Quantity <= 0 {
		req.Quantity = 1 // 默认数量为1
	}
	if req.ExpireMinutes <= 0 {
		req.ExpireMinutes = 15 // 默认过期时间为15分钟
	}

	// 验证微信支付参数
	if req.PaymentMethod == "wechat" && req.DeviceType == "wechat" && req.OpenID == "" {
		response.FailWithMessage("微信JSAPI支付需要提供用户OpenID。如果不在微信环境中，请将deviceType改为'mobile'或'pc'", c)
		return
	}

	// 设置客户端IP
	req.ClientIP = c.ClientIP()

	userID := utils.GetUserID(c)
	buyResp, err := purchaseService.BuyProduct(userID, req)
	if err != nil {
		global.GVA_LOG.Error("购买商品失败!", zap.Error(err))
		response.FailWithMessage("购买商品失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(buyResp, "购买成功", c)
}

// CancelOrder 取消订单
// @Tags Product
// @Summary 取消订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param orderNo path string true "订单号"
// @Success 200 {object} response.Response{msg=string} "取消成功"
// @Router /product/cancel/{orderNo} [post]
func (p *ProductApi) CancelOrder(c *gin.Context) {
	orderNo := c.Param("orderNo")
	if orderNo == "" {
		response.FailWithMessage("订单号不能为空", c)
		return
	}

	userID := utils.GetUserID(c)
	err := purchaseService.CancelOrder(userID, orderNo)
	if err != nil {
		global.GVA_LOG.Error("取消订单失败!",
			zap.Error(err),
			zap.String("orderNo", orderNo),
			zap.Uint("userId", userID))
		response.FailWithMessage("取消订单失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("订单取消成功", c)
}

package payment

import (
	"encoding/json"
	"io/ioutil"
	"net/http"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/payment"
	paymentUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/payment"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PaymentApi struct{}

//var paymentServiceApp = paymentSvc.PaymentServiceApp

// CreatePayment 创建支付订单
// @Tags Payment
// @Summary 创建支付订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body paymentUtils.CreatePaymentRequest true "创建支付订单请求"
// @Success 200 {object} response.Response{data=paymentUtils.CreatePaymentResponse,msg=string} "创建成功"
// @Router /payment/create [post]
func (p *PaymentApi) CreatePayment(c *gin.Context) {
	var req paymentUtils.CreatePaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置客户端IP
	req.ClientIP = c.ClientIP()

	// 调用服务层
	result, err := paymentService.CreatePayment(req)
	if err != nil {
		global.GVA_LOG.Error("创建支付订单失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// QueryPayment 查询支付状态
// @Tags Payment
// @Summary 查询支付状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param orderNo path string true "订单号"
// @Success 200 {object} response.Response{data=paymentUtils.PaymentQueryResult,msg=string} "查询成功"
// @Router /payment/query/{orderNo} [get]
func (p *PaymentApi) QueryPayment(c *gin.Context) {
	orderNo := c.Query("orderNo")
	if orderNo == "" {
		response.FailWithMessage("订单号不能为空", c)
		return
	}

	// 调用服务层
	result, err := paymentService.QueryPayment(orderNo)
	if err != nil {
		global.GVA_LOG.Error("查询支付状态失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// RefundPayment 退款
// @Tags Payment
// @Summary 退款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body paymentUtils.RefundRequest true "退款请求"
// @Success 200 {object} response.Response{msg=string} "退款成功"
// @Router /payment/refund [post]
func (p *PaymentApi) RefundPayment(c *gin.Context) {
	var req paymentUtils.RefundRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 调用服务层
	err := paymentService.RefundPayment(req)
	if err != nil {
		global.GVA_LOG.Error("退款失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("退款成功", c)
}

// WechatNotify 微信支付异步通知
// @Tags Payment
// @Summary 微信支付异步通知
// @accept application/json
// @Produce application/json
// @Router /payment/notify/wechatNotify [post]
func (p *PaymentApi) WechatNotify(c *gin.Context) {
	// 记录请求信息（调试用）
	global.GVA_LOG.Info("收到微信支付异步通知",
		zap.String("method", c.Request.Method),
		zap.String("url", c.Request.URL.String()),
		zap.Any("headers", c.Request.Header))

	// 读取请求体
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		global.GVA_LOG.Error("读取微信通知数据失败", zap.Error(err))
		// 微信支付要求返回JSON格式的失败响应
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "FAIL",
			"message": "读取通知数据失败",
		})
		return
	}

	// 记录通知数据（调试用）
	global.GVA_LOG.Info("微信支付通知内容",
		zap.String("body", string(body)))

	// 处理通知
	err = paymentService.ProcessNotify(payment.PaymentMethodWechat, body)
	if err != nil {
		global.GVA_LOG.Error("处理微信通知失败", zap.Error(err))
		// 微信支付要求返回JSON格式的失败响应
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    "FAIL",
			"message": "处理通知失败",
		})
		return
	}

	// 返回成功响应 - 微信支付V3要求返回JSON格式
	c.JSON(http.StatusOK, gin.H{
		"code":    "SUCCESS",
		"message": "处理成功",
	})
}

// AlipayNotify 支付宝异步通知
// @Tags Payment
// @Summary 支付宝异步通知
// @accept application/x-www-form-urlencoded
// @Produce text/plain
// @Router /payment/notify/alipayNotify [post]
func (p *PaymentApi) AlipayNotify(c *gin.Context) {
	// 支付宝通知是form格式的数据，需要从PostForm中读取
	if err := c.Request.ParseForm(); err != nil {
		global.GVA_LOG.Error("解析支付宝通知表单数据失败", zap.Error(err))
		c.String(http.StatusBadRequest, "fail")
		return
	}

	// 将form数据转换为map
	notifyParams := make(map[string]string)
	for key, values := range c.Request.PostForm {
		if len(values) > 0 {
			notifyParams[key] = values[0]
		}
	}

	// 记录通知数据（调试用）
	global.GVA_LOG.Info("收到支付宝异步通知", zap.Any("params", notifyParams))

	// 将map转换为JSON格式传递给ProcessNotify
	notifyData, err := json.Marshal(notifyParams)
	if err != nil {
		global.GVA_LOG.Error("序列化支付宝通知数据失败", zap.Error(err))
		c.String(http.StatusInternalServerError, "fail")
		return
	}

	// 处理通知
	err = paymentService.ProcessNotify(payment.PaymentMethodAlipay, notifyData)
	if err != nil {
		global.GVA_LOG.Error("处理支付宝通知失败", zap.Error(err))
		c.String(http.StatusInternalServerError, "fail")
		return
	}

	// 返回成功响应 - 支付宝要求返回"success"字符串
	c.String(http.StatusOK, "success")
}

// GetPaymentConfig 获取支付配置
// @Tags Payment
// @Summary 获取支付配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "获取成功"
// @Router /payment/config [get]
func (p *PaymentApi) GetPaymentConfig(c *gin.Context) {
	config := map[string]interface{}{
		"wechat": map[string]interface{}{
			"enabled":                       global.GVA_CONFIG.Payment.Wechat.Enabled,
			"mp_app_id":                     global.GVA_CONFIG.Payment.Wechat.MpAppID,
			"open_app_id":                   global.GVA_CONFIG.Payment.Wechat.OpenAppID,
			"mch_id":                        global.GVA_CONFIG.Payment.Wechat.MchID,
			"mch_certificate_serial_number": global.GVA_CONFIG.Payment.Wechat.MchCertificateSerialNumber,
			"notify_url":                    global.GVA_CONFIG.Payment.Wechat.NotifyUrl,
			"is_sandbox":                    global.GVA_CONFIG.Payment.Wechat.IsSandbox,
			"h5_domain":                     global.GVA_CONFIG.Payment.Wechat.H5Domain,
			"h5_return_url":                 global.GVA_CONFIG.Payment.Wechat.H5ReturnUrl,
			// 敏感信息不返回
			"apiv3_key":   "***",
			"private_key": "***",
		},
		"alipay": map[string]interface{}{
			"enabled":       global.GVA_CONFIG.Payment.Alipay.Enabled,
			"app_id":        global.GVA_CONFIG.Payment.Alipay.AppID,
			"is_production": global.GVA_CONFIG.Payment.Alipay.IsProduction,
			"notify_url":    global.GVA_CONFIG.Payment.Alipay.NotifyUrl,
			"return_url":    global.GVA_CONFIG.Payment.Alipay.ReturnUrl,
			"sign_type":     global.GVA_CONFIG.Payment.Alipay.SignType,
			// 敏感信息不返回
			"private_key": "***",
			"public_key":  "***",
		},
	}

	response.OkWithData(config, c)
}

// GetPaymentList 获取支付订单列表
// @Tags Payment
// @Summary 获取支付订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param orderNo query string false "订单号"
// @Param status query int false "支付状态"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /payment/list [get]
func (p *PaymentApi) GetPaymentList(c *gin.Context) {
	var pageInfo struct {
		Page     int    `form:"page"`
		PageSize int    `form:"pageSize"`
		OrderNo  string `form:"orderNo"`
		Status   int    `form:"status"`
	}

	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if pageInfo.Page <= 0 {
		pageInfo.Page = 1
	}
	if pageInfo.PageSize <= 0 {
		pageInfo.PageSize = 10
	}

	// 构建查询条件
	db := global.GVA_DB.Model(&payment.PaymentOrder{})

	if pageInfo.OrderNo != "" {
		db = db.Where("order_no LIKE ?", "%"+pageInfo.OrderNo+"%")
	}
	if pageInfo.Status > 0 {
		db = db.Where("status = ?", pageInfo.Status)
	}

	// 查询总数
	var total int64
	db.Count(&total)

	// 分页查询
	var orders []payment.PaymentOrder
	offset := (pageInfo.Page - 1) * pageInfo.PageSize
	err := db.Offset(offset).Limit(pageInfo.PageSize).
		Order("created_at DESC").Find(&orders).Error

	if err != nil {
		global.GVA_LOG.Error("获取支付订单列表失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(response.PageResult{
		List:     orders,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, c)
}

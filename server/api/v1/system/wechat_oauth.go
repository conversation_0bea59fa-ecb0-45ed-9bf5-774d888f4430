package system

import (
	"context"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type WechatOAuthApi struct{}

// GetWechatConfig
// @Tags     WechatOAuth
// @Summary  获取网站应用AppId等信息
// @Produce   application/json
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "获取网站应用AppId等信息"
// @Router   /wechat/wechat-config [get]
func (w *WechatOAuthApi) GetWechatConfig(c *gin.Context) {
	// 生成state参数防止CSRF攻击
	state, err := wechatOAuthService.GenerateState()
	if err != nil {
		response.FailWithMessage("生成授权URL失败: "+err.Error(), c)
	}
	// 将state存储到Redis，设置120分钟过期
	ctx := context.Background()
	err = global.GVA_REDIS.Set(ctx, "wechat_state:"+state, "valid", 120*time.Minute).Err()
	if err != nil {
		global.GVA_LOG.Error("存储微信state失败", zap.Error(err))
		response.FailWithMessage("存储state失败", c)
		return
	}
	response.OkWithDetailed(gin.H{
		"appId":       global.GVA_CONFIG.WechatOAuth.AppID,
		"redirectUri": global.GVA_CONFIG.WechatOAuth.RedirectURI,
		"scope":       global.GVA_CONFIG.WechatOAuth.Scope,
		"state":       state,
		"stylelite":   1,
		"style":       "black",
	}, "获取网站应用AppId等信息", c)
}

// WechatCallback
// @Tags     WechatOAuth
// @Summary  微信授权回调
// @Produce   application/json
// @Param    data  body      systemReq.WechatAuthRequest                                 true  "微信授权code和state"
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "微信授权回调结果"
// @Router   /wechat/callback [post]
func (w *WechatOAuthApi) WechatCallback(c *gin.Context) {
	code := c.Query("code")
	state := c.Query("state")
	if code == "" || state == "" {
		response.FailWithMessage("参数错误: code和state不能为空", c)
		return
	}

	// 将 code 缓存到 Redis，以 state 为 key
	err := wechatOAuthService.CacheCodeByState(state, code)
	if err != nil {
		global.GVA_LOG.Error("缓存微信授权code失败!", zap.Error(err))
		response.FailWithMessage("缓存授权code失败: "+err.Error(), c)
		return
	}
	// 返回成功响应
	response.Ok(c)
}

// PollWechatCode
// @Tags     WechatOAuth
// @Summary  轮询获取微信授权code
// @Produce   application/json
// @Param    state  query     string                                                      true  "state参数"
// @Success  200    {object}  response.Response{data=map[string]interface{},msg=string}  "轮询结果"
// @Router   /wechat/poll-code [get]
func (w *WechatOAuthApi) PollWechatCode(c *gin.Context) {
	state := c.Query("state")
	if state == "" {
		response.FailWithMessage("state参数不能为空", c)
		return
	}
	// 验证state参数
	validateCode, err := wechatOAuthService.ValidateStateWithoutDelete(state)

	if validateCode == "" {
		response.OkWithDetailed(map[string]interface{}{
			"action":    "waiting",
			"user":      nil,
			"token":     "",
			"expiresAt": 0,
			"provider":  "wechat",
			"isNewUser": false,
			"needsBind": false,
		}, "登录成功", c)
		return
	}

	code, err := wechatOAuthService.GetCodeByState(state)
	if err != nil {
		global.GVA_LOG.Error("获取微信授权code失败!", zap.Error(err))
		response.FailWithMessage("获取授权code失败: "+err.Error(), c)
		return
	}

	// 找到 code，处理微信授权回调
	result, err := wechatOAuthService.LoginByWechat(systemReq.WechatAuthRequest{
		Code:  code,
		State: state,
	})
	if err != nil {
		global.GVA_LOG.Error("微信授权回调处理失败!", zap.Error(err))
		response.FailWithMessage("授权失败: "+err.Error(), c)
		return
	}

	// 根据不同的action返回不同的响应，使用与UnifiedLogin一致的格式
	if result.Action == "login" {
		// 直接登录成功，使用与UnifiedLogin相同的格式
		clientIP := c.ClientIP()

		// 获取设备ID（如果是设备登录）
		deviceID := c.GetHeader("Device-ID")
		if deviceID == "" {
			// 尝试从查询参数中获取deviceId
			deviceID = c.Query("deviceId")
		}

		var deviceIDPtr *string
		if deviceID != "" {
			deviceIDPtr = &deviceID
		}

		// 使用新的会话管理系统创建会话和token
		token, err := jwtService.CreateSessionAndToken(result.User, deviceIDPtr, clientIP, c.GetHeader("User-Agent"))
		if err != nil {
			global.GVA_LOG.Error("创建会话和token失败!", zap.Error(err))
			response.FailWithMessage("登录失败: "+err.Error(), c)
			return
		}

		// 解析token获取claims（用于过期时间）
		j := utils.NewJWT()
		claims, err := j.ParseToken(token)
		if err != nil {
			global.GVA_LOG.Error("解析token失败!", zap.Error(err))
			response.FailWithMessage("登录失败: "+err.Error(), c)
			return
		}

		// 设置cookie中的token
		utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))

		// 返回与UnifiedLogin一致的格式
		response.OkWithDetailed(map[string]interface{}{
			"action":    "login",
			"user":      result.User,
			"token":     token,
			"expiresAt": claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
			"provider":  "wechat",
			"isNewUser": false,
			"needsBind": false,
			"tempData":  nil,
		}, "登录成功", c)
	} else {
		// 需要绑定账号，返回与UnifiedLogin一致的格式
		response.OkWithDetailed(map[string]interface{}{
			"action":    "bind",
			"user":      nil,
			"token":     "",
			"expiresAt": 0,
			"provider":  "wechat",
			"isNewUser": false,
			"needsBind": true,
			"tempData":  result.TempData,
		}, result.Message, c)
	}
}

package system

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserSessionApi struct{}

// GetMySessions 获取当前用户的活跃会话列表
// @Tags UserSession
// @Summary 获取当前用户的活跃会话列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]system.SysUserSession} "获取成功"
// @Router /user/sessions [get]
func (sessionApi *UserSessionApi) GetMySessions(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	sessions, err := userSessionService.GetUserActiveSessions(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户会话列表失败!", zap.Error(err))
		response.FailWithMessage("获取会话列表失败: "+err.Error(), c)
		return
	}

	// 隐藏敏感信息（Token字段）
	for i := range sessions {
		sessions[i].Token = ""
	}

	response.OkWithDetailed(gin.H{
		"sessions": sessions,
		"total":    len(sessions),
	}, "获取会话列表成功", c)
}

// LogoutSession 注销指定会话
// @Tags UserSession
// @Summary 注销指定会话
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param sessionId query string true "会话ID"
// @Success 200 {object} response.Response "注销成功"
// @Router /user/sessions/logout [delete]
func (sessionApi *UserSessionApi) LogoutSession(c *gin.Context) {
	sessionIDStr := c.Query("sessionId")
	if sessionIDStr == "" {
		response.FailWithMessage("会话ID不能为空", c)
		return
	}

	sessionID, err := strconv.ParseUint(sessionIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("会话ID格式错误", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 查询会话信息
	var session system.SysUserSession
	err = global.GVA_DB.Where("id = ? AND user_id = ?", uint(sessionID), userID).First(&session).Error
	if err != nil {
		response.FailWithMessage("会话不存在或无权限", c)
		return
	}

	// 注销会话
	err = jwtService.LogoutSession(session.Token)
	if err != nil {
		global.GVA_LOG.Error("注销会话失败!", zap.Error(err))
		response.FailWithMessage("注销会话失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("会话注销成功", c)
}

// LogoutAllOtherSessions 注销所有其他会话
// @Tags UserSession
// @Summary 注销所有其他会话（保留当前会话）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response "注销成功"
// @Router /user/sessions/logout-others [delete]
func (sessionApi *UserSessionApi) LogoutAllOtherSessions(c *gin.Context) {
	// 获取当前用户ID和Token
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 获取当前Token
	currentToken := c.GetHeader("x-token")
	if currentToken == "" {
		response.FailWithMessage("无法获取当前Token", c)
		return
	}

	// 注销所有其他会话
	err := jwtService.LogoutAllSessions(userID, currentToken)
	if err != nil {
		global.GVA_LOG.Error("注销所有会话失败!", zap.Error(err))
		response.FailWithMessage("注销所有会话失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("所有其他会话已注销", c)
}

// RefreshToken 刷新Token接口
// @Tags UserSession
// @Summary 刷新Token
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param deviceId query string false "设备ID（设备登录时提供）"
// @Success 200 {object} response.Response{data=gin.H} "刷新成功"
// @Router /user/refresh-token [post]
func (sessionApi *UserSessionApi) RefreshToken(c *gin.Context) {
	// 获取当前Token
	currentToken := c.GetHeader("x-token")
	if currentToken == "" {
		response.Fail(c)
		return
	}

	// 记录刷新请求的日志，包含客户端IP和User-Agent
	global.GVA_LOG.Info("Token刷新请求",
		zap.String("token", currentToken),
		zap.String("clientIP", c.ClientIP()),
		zap.String("userAgent", c.GetHeader("User-Agent")))

	// 提前检查token是否已在黑名单中，避免不必要的处理
	if jwtService.IsBlacklist(currentToken) {
		global.GVA_LOG.Warn("尝试刷新已在黑名单中的token", zap.String("token", currentToken))
		response.Fail(c)
		return
	}

	deviceID := c.GetHeader("Device-ID")
	if deviceID == "" {
		// 尝试从URL查询参数获取deviceId
		deviceID = c.Query("deviceId")
	}
	if deviceID == "" {
		// 尝试从请求体中获取deviceId
		var req struct {
			DeviceID string `json:"deviceId"`
		}
		c.ShouldBindJSON(&req)
		deviceID = req.DeviceID
	}

	var newToken string
	var err error

	// 根据是否有设备ID判断刷新类型
	if deviceID != "" {
		global.GVA_LOG.Info("执行设备Token刷新",
			zap.String("deviceId", deviceID),
			zap.String("token", currentToken))
		// 设备登录刷新
		newToken, err = jwtService.RefreshTokenByDevice(deviceID, currentToken)
	} else {
		global.GVA_LOG.Info("执行网站Token刷新", zap.String("token", currentToken))
		// 网站登录刷新
		newToken, err = jwtService.RefreshTokenByWeb(currentToken)
	}

	if err != nil {
		global.GVA_LOG.Error("刷新Token失败",
			zap.String("token", currentToken),
			zap.String("deviceId", deviceID),
			zap.Error(err))
		response.Fail(c)
		return
	}

	// 成功刷新后记录日志
	global.GVA_LOG.Info("Token刷新成功",
		zap.String("oldToken", currentToken),
		zap.String("newToken", newToken),
		zap.String("deviceId", deviceID))

	response.OkWithDetailed(gin.H{
		"token": newToken,
	}, "Token刷新成功", c)
}

// GetSessionStatistics 获取用户会话统计信息
// @Tags UserSession
// @Summary 获取用户会话统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=gin.H} "获取成功"
// @Router /user/sessions/statistics [get]
func (sessionApi *UserSessionApi) GetSessionStatistics(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	sessions, err := userSessionService.GetUserActiveSessions(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户会话列表失败!", zap.Error(err))
		response.FailWithMessage("获取会话统计失败: "+err.Error(), c)
		return
	}

	// 统计不同类型的会话
	var deviceSessions, webSessions int
	for _, session := range sessions {
		if session.SessionType == system.SessionTypeDevice {
			deviceSessions++
		} else {
			webSessions++
		}
	}

	response.OkWithDetailed(gin.H{
		"totalSessions":  len(sessions),
		"deviceSessions": deviceSessions,
		"webSessions":    webSessions,
		"maxDevices":     system.MaxDeviceLogins,
		"maxWeb":         system.MaxWebLogins,
	}, "获取会话统计成功", c)
}

package system

import (
	"context"
	"runtime"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
)

type HeartbeatApi struct{}

// HeartbeatResponse 心跳检测响应结构
type HeartbeatResponse struct {
	Status    string            `json:"status"`    // 服务状态
	Timestamp int64             `json:"timestamp"` // 时间戳
	Uptime    string            `json:"uptime"`    // 运行时间
	Version   string            `json:"version"`   // 版本信息
	System    SystemInfo        `json:"system"`    // 系统信息
	Database  DatabaseStatus    `json:"database"`  // 数据库状态
	Redis     RedisStatus       `json:"redis"`     // Redis状态
	Config    map[string]string `json:"config"`    // 配置信息
}

// SystemInfo 系统信息
type SystemInfo struct {
	OS           string `json:"os"`            // 操作系统
	Arch         string `json:"arch"`          // 架构
	GoVersion    string `json:"go_version"`    // Go版本
	NumCPU       int    `json:"num_cpu"`       // CPU核心数
	NumGoroutine int    `json:"num_goroutine"` // 协程数量
}

// DatabaseStatus 数据库状态
type DatabaseStatus struct {
	Connected bool   `json:"connected"` // 是否连接
	Type      string `json:"type"`      // 数据库类型
	Error     string `json:"error"`     // 错误信息
}

// RedisStatus Redis状态
type RedisStatus struct {
	Connected bool   `json:"connected"` // 是否连接
	Error     string `json:"error"`     // 错误信息
}

var startTime = time.Now()

// Heartbeat 心跳检测接口
// @Tags System
// @Summary 心跳检测
// @Description 获取系统运行状态和基本信息，不需要鉴权
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=HeartbeatResponse} "心跳检测成功"
// @Router /heartbeat [get]
func (h *HeartbeatApi) Heartbeat(c *gin.Context) {
	// 构建响应数据
	resp := HeartbeatResponse{
		Status:    "healthy",
		Timestamp: time.Now().Unix(),
		Uptime:    time.Since(startTime).String(),
		Version:   "v2.8.0", // 可以从配置或构建信息中获取
		System: SystemInfo{
			OS:           runtime.GOOS,
			Arch:         runtime.GOARCH,
			GoVersion:    runtime.Version(),
			NumCPU:       runtime.NumCPU(),
			NumGoroutine: runtime.NumGoroutine(),
		},
		Database: getDatabaseStatus(),
		Redis:    getRedisStatus(),
		Config: map[string]string{
			"router_prefix":  global.GVA_CONFIG.System.RouterPrefix,
			"db_type":        global.GVA_CONFIG.System.DbType,
			"use_redis":      getBoolString(global.GVA_CONFIG.System.UseRedis),
			"use_multipoint": getBoolString(global.GVA_CONFIG.System.UseMultipoint),
		},
	}

	response.OkWithData(resp, c)
}

// SimpleHeartbeat 简单心跳检测接口
// @Tags System
// @Summary 简单心跳检测
// @Description 简单的心跳检测，只返回状态和时间戳
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{}} "心跳检测成功"
// @Router /ping [get]
func (h *HeartbeatApi) SimpleHeartbeat(c *gin.Context) {
	response.OkWithData(gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"message":   "pong",
	}, c)
}

// getDatabaseStatus 获取数据库状态
func getDatabaseStatus() DatabaseStatus {
	status := DatabaseStatus{
		Connected: false,
		Type:      global.GVA_CONFIG.System.DbType,
		Error:     "",
	}

	if global.GVA_DB != nil {
		sqlDB, err := global.GVA_DB.DB()
		if err != nil {
			status.Error = err.Error()
		} else {
			err = sqlDB.Ping()
			if err != nil {
				status.Error = err.Error()
			} else {
				status.Connected = true
			}
		}
	} else {
		status.Error = "database not initialized"
	}

	return status
}

// getRedisStatus 获取Redis状态
func getRedisStatus() RedisStatus {
	status := RedisStatus{
		Connected: false,
		Error:     "",
	}

	if global.GVA_CONFIG.System.UseRedis && global.GVA_REDIS != nil {
		_, err := global.GVA_REDIS.Ping(context.Background()).Result()
		if err != nil {
			status.Error = err.Error()
		} else {
			status.Connected = true
		}
	} else if !global.GVA_CONFIG.System.UseRedis {
		status.Error = "redis not enabled"
	} else {
		status.Error = "redis not initialized"
	}

	return status
}

// getBoolString 将布尔值转换为字符串
func getBoolString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

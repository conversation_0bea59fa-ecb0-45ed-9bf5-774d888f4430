package system

import (
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service/integral"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	captchaSdk "github.com/yidun/yidun-golang-sdk/yidun/service/captcha"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Login
// @Tags     Base
// @Summary  用户登录
// @Produce   application/json
// @Param    data  body      systemReq.Login                                             true  "用户名, 密码, 验证码"
// @Success  200   {object}  response.Response{data=systemRes.LoginResponse,msg=string}  "返回包括用户信息,token,过期时间"
// @Router   /base/login [post]
func (b *BaseApi) Login(c *gin.Context) {
	var l systemReq.Login
	err := c.ShouldBindJSON(&l)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = utils.Verify(l, utils.LoginVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	clientIP := c.ClientIP()

	// 验证码验证
	if !b.VerifyCaptchaWithRequest(c, &l, clientIP) {
		return
	}

	// 用户登录验证
	user, err := b.authenticateUser(l)
	if err != nil {
		global.GVA_LOG.Error("登陆失败! 用户名不存在或者密码错误!", zap.Error(err))
		b.incrementFailCount(clientIP)
		response.FailWithMessage("用户名不存在或者密码错误", c)
		return
	}

	// 检查用户状态
	if !b.checkUserStatus(*user) {
		global.GVA_LOG.Error("登陆失败! 用户被禁止登录!")
		b.incrementFailCount(clientIP)
		response.FailWithMessage("用户被禁止登录", c)
		return
	}

	// 登录成功，签发token
	b.TokenNext(c, *user)
}

// VerifyCaptchaWithRequest 通用验证码验证方法，使用CaptchaRequest接口
func (b *BaseApi) VerifyCaptchaWithRequest(c *gin.Context, req systemReq.CaptchaRequest, clientIP string) bool {
	// 易盾验证码验证
	if req.GetValidate() != "" {
		return b.verifyYidunCaptchaWithRequest(c, req.GetValidate())
	}

	// 传统验证码验证
	return b.verifyTraditionalCaptchaWithRequest(c, req, clientIP)
}

// verifyYidunCaptchaWithRequest 易盾验证码验证（通用接口版本）
func (b *BaseApi) verifyYidunCaptchaWithRequest(c *gin.Context, validate string) bool {
	sdkRequest := captchaSdk.NewCaptchaVerifyRequest()
	sdkRequest.Validate = &validate

	yidunResponse, err := yidunCaptchaService.VerifyCaptcha(*sdkRequest)
	if err != nil {
		global.GVA_LOG.Error("易盾验证码验证失败!", zap.Error(err))
		response.FailWithMessage("验证码验证失败", c)
		return false
	}

	// 检查验证结果
	if yidunResponse.Error == nil || *yidunResponse.Error != 0 {
		errorMsg := "验证码验证接口调用失败"
		if yidunResponse.Msg != nil {
			errorMsg = *yidunResponse.Msg
		}
		global.GVA_LOG.Error("易盾验证码验证接口错误", zap.String("msg", errorMsg))
		response.FailWithMessage("验证码验证失败", c)
		return false
	}

	if yidunResponse.Result == nil || !*yidunResponse.Result {
		global.GVA_LOG.Warn("易盾验证码验证失败")
		response.FailWithMessage("验证码验证失败", c)
		return false
	}

	global.GVA_LOG.Info("易盾验证码验证成功")
	return true
}

// verifyTraditionalCaptchaWithRequest 传统验证码验证（通用接口版本）
func (b *BaseApi) verifyTraditionalCaptchaWithRequest(c *gin.Context, req systemReq.CaptchaRequest, clientIP string) bool {
	openCaptcha := global.GVA_CONFIG.Captcha.OpenCaptcha
	openCaptchaTimeOut := global.GVA_CONFIG.Captcha.OpenCaptchaTimeOut

	// 初始化黑名单缓存
	b.initBlackCache(clientIP, openCaptchaTimeOut)

	// 检查是否需要验证码
	v, _ := global.BlackCache.Get(clientIP)
	needCaptcha := openCaptcha != 0 && openCaptcha >= InterfaceToInt(v)

	// 如果不需要验证码或验证码验证通过
	if !needCaptcha || b.validateCaptcha(req.GetCaptchaId(), req.GetCaptcha()) {
		return true
	}

	// 验证码验证失败
	b.incrementFailCount(clientIP)
	response.FailWithMessage("验证码错误", c)
	return false
}

// validateCaptcha 验证传统验证码
func (b *BaseApi) validateCaptcha(captchaId, captcha string) bool {
	return captchaId != "" && captcha != "" && store.Verify(captchaId, captcha, true)
}

// initBlackCache 初始化黑名单缓存
func (b *BaseApi) initBlackCache(clientIP string, timeout int) {
	if _, ok := global.BlackCache.Get(clientIP); !ok {
		global.BlackCache.Set(clientIP, 1, time.Second*time.Duration(timeout))
	}
}

// incrementFailCount 增加失败计数
func (b *BaseApi) incrementFailCount(clientIP string) {
	global.BlackCache.Increment(clientIP, 1)
}

// authenticateUser 用户认证
func (b *BaseApi) authenticateUser(l systemReq.Login) (*system.SysUser, error) {
	u := &system.SysUser{Username: l.Username, Password: l.Password}
	return userService.Login(u)
}

// checkUserStatus 检查用户状态
func (b *BaseApi) checkUserStatus(user system.SysUser) bool {
	const USER_ENABLED = 1
	return user.Enable == USER_ENABLED
}

// TokenNext 登录以后签发jwt
func (b *BaseApi) TokenNext(c *gin.Context, user system.SysUser) {
	// 获取设备ID（如果是设备登录）
	deviceID := c.GetHeader("Device-ID")
	if deviceID == "" {
		// 尝试从请求体中获取deviceId
		var loginReq struct {
			DeviceID string `json:"deviceId"`
		}
		c.ShouldBindJSON(&loginReq)
		deviceID = loginReq.DeviceID
	}

	var deviceIDPtr *string
	if deviceID != "" {
		deviceIDPtr = &deviceID
	}

	// 使用新的会话管理系统创建会话和token
	token, err := jwtService.CreateSessionAndToken(&user, deviceIDPtr, c.ClientIP(), c.GetHeader("User-Agent"))
	if err != nil {
		global.GVA_LOG.Error("创建会话和token失败!", zap.Error(err))
		response.FailWithMessage("登录失败: "+err.Error(), c)
		return
	}

	// 解析token获取claims（用于过期时间）
	j := utils.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		global.GVA_LOG.Error("解析token失败!", zap.Error(err))
		response.FailWithMessage("登录失败: "+err.Error(), c)
		return
	}

	// 检查并发放首次登录奖励
	b.CheckAndGrantFirstLoginReward(user.ID)

	// 设置cookie中的token
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))

	response.OkWithDetailed(systemRes.LoginResponse{
		User:      user,
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
	}, "登录成功", c)
}

// Register
// @Tags     SysUser
// @Summary  用户注册账号
// @Produce   application/json
// @Param    data  body      systemReq.Register                                            true  "用户名, 昵称, 密码, 角色ID"
// @Success  200   {object}  response.Response{data=systemRes.SysUserResponse,msg=string}  "用户注册账号,返回包括用户信息"
// @Router   /user/admin_register [post]
func (b *BaseApi) Register(c *gin.Context) {
	var r systemReq.Register
	err := c.ShouldBindJSON(&r)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 获取设备信息和客户端信息
	deviceID := c.GetHeader("Device-ID")
	//当没传角色ID时，默认注册为mcp用户
	if r.AuthorityId == 0 {
		r.AuthorityId = 666
		r.AuthorityIds = []uint{666}
	}
	err = utils.Verify(r, utils.RegisterVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var authorities []system.SysAuthority
	for _, v := range r.AuthorityIds {
		authorities = append(authorities, system.SysAuthority{
			AuthorityId: v,
		})
	}
	//r.Enable为空时，默认注册为启用状态
	if r.Enable == 0 {
		r.Enable = 1
	}
	user := &system.SysUser{Username: r.Username, NickName: r.NickName, Password: r.Password, HeaderImg: r.HeaderImg, AuthorityId: r.AuthorityId, Authorities: authorities, Enable: r.Enable, Phone: r.Phone, Email: r.Email}
	userReturn, err := userService.Register(*user)
	// 尝试绑定设备（如果有设备ID），绑定失败不影响登录
	if deviceID != "" {
		err = deviceService.BindDevice(deviceID, &userReturn.ID)
		if err != nil {
			// 设备绑定失败，记录日志但不影响登录流程
			global.GVA_LOG.Error("设备绑定失败",
				zap.String("deviceId", deviceID),
				zap.Uint("userId", userReturn.ID),
				zap.Error(err))
		} else {
			global.GVA_LOG.Info("设备绑定成功",
				zap.String("deviceId", deviceID),
				zap.Uint("userId", userReturn.ID))
		}
	}
	if err != nil {
		global.GVA_LOG.Error("注册失败!", zap.Error(err))
		response.FailWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册失败", c)
		return
	}
	response.OkWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册成功", c)
}

// PhoneRegister
// @Tags      Base
// @Summary   手机号注册账号
// @Produce   application/json
// @Param     data  body      systemReq.PhoneRegister                                       true  "手机号, 验证码, 密码, 昵称"
// @Success   200   {object}  response.Response{data=systemRes.LoginResponse,msg=string}    "手机号注册账号,返回包括用户信息和登录Token"
// @Router    /base/phoneRegister [post]
func (b *BaseApi) PhoneRegister(c *gin.Context) {
	var r systemReq.PhoneRegister
	err := c.ShouldBindJSON(&r)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取设备信息和客户端信息
	deviceID := c.GetHeader("Device-ID")
	// 易盾验证码验证
	if !b.verifyYidunCaptchaWithRequest(c, r.Validate) {
		return
	}

	// 使用统一验证规则
	err = utils.Verify(r, utils.PhoneRegisterVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 验证密码强度
	if !utils.ValidatePassword(r.Password) {
		response.FailWithMessage("密码必须包含数字、字母和特殊字符的两种，长度至少6位", c)
		return
	}

	// 验证验证码
	if !authProviderService.VerifyPhoneCode(r.Phone, r.Code) {
		response.FailWithMessage("验证码错误", c)
		return
	}

	// 当没传角色ID时，默认注册为普通用户
	if r.AuthorityId == 0 {
		r.AuthorityId = 666
	}

	// 设置默认启用状态
	if r.Enable == 0 {
		r.Enable = 1
	}

	// 设置默认昵称
	if r.NickName == "" {
		r.NickName = r.Phone
	}

	var authorities []system.SysAuthority
	authorities = append(authorities, system.SysAuthority{
		AuthorityId: r.AuthorityId,
	})

	user := &system.SysUser{
		Username:    r.Phone, // 使用手机号作为用户名
		NickName:    r.NickName,
		Password:    r.Password,
		AuthorityId: r.AuthorityId,
		Authorities: authorities,
		Enable:      r.Enable,
		Phone:       r.Phone,
	}

	// 开启事务处理用户注册和认证提供商绑定
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 注册用户
		userReturn, err := userService.Register(*user)
		if err != nil {
			return err
		}

		// 检查该认证提供商是否已被其他用户使用
		var existing system.SysAuthProvider
		err = global.GVA_DB.Where("provider = ? AND provider_id = ?", system.ProviderTypePhone, r.Phone).First(&existing).Error
		if err == nil {
			return fmt.Errorf("该认证方式已被其他用户绑定")
		}

		// 创建手机号认证提供商记录
		authProvider := &system.SysAuthProvider{
			UserId:     userReturn.ID,
			Provider:   system.ProviderTypePhone,
			ProviderId: r.Phone,
			Phone:      r.Phone,
			Verified:   true, // 通过验证码验证，标记为已验证
			Metadata:   "",
		}
		now := time.Now()
		authProvider.LastUsedAt = &now

		if err := tx.Create(authProvider).Error; err != nil {
			return fmt.Errorf("创建手机号认证提供商记录失败: %w", err)
		}

		// 更新用户信息到事务中的userReturn
		*user = userReturn

		// 尝试绑定设备（如果有设备ID），绑定失败不影响登录
		if deviceID != "" {
			err = deviceService.BindDevice(deviceID, &user.ID)
			if err != nil {
				// 设备绑定失败，记录日志但不影响登录流程
				global.GVA_LOG.Error("设备绑定失败",
					zap.String("deviceId", deviceID),
					zap.Uint("userId", user.ID),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("设备绑定成功",
					zap.String("deviceId", deviceID),
					zap.Uint("userId", user.ID))
			}
		}
		return nil
	})

	if err != nil {
		global.GVA_LOG.Error("手机号注册失败!", zap.Error(err))
		response.FailWithMessage("注册失败: "+err.Error(), c)
		return
	}

	// 注册成功后直接登录
	b.TokenNext(c, *user)
}

// SendSMSCode
// @Tags      Base
// @Summary   发送短信验证码
// @Produce   application/json
// @Param     data  body      systemReq.SendSMSCodeReq                              true  "手机号, 短信类型"
// @Success   200   {object}  response.Response{msg=string}                        "发送成功"
// @Router    /base/sendSMSCode [post]
func (b *BaseApi) SendSMSCode(c *gin.Context) {
	var req systemReq.SendSMSCodeReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		// 记录参数解析失败的安全日志
		global.GVA_LOG.Warn("参数解析失败",
			zap.String("ip", c.ClientIP()),
			zap.String("user_agent", c.GetHeader("User-Agent")),
			zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	//// 账号密码登录验证码验证
	//if !b.verifyYidunCaptchaWithRequest(c, req.Validate) {
	//	return
	//}

	if req.Phone == "" {
		response.FailWithMessage("手机号不能为空", c)
		return
	}

	// 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		global.GVA_LOG.Warn("SMS request with invalid phone format",
			zap.String("ip", c.ClientIP()),
			zap.String("phone", utils.MaskPhone(req.Phone)))
		response.FailWithMessage("手机号格式不正确", c)
		return
	}

	// 验证短信类型
	if req.SmsType != 1 && req.SmsType != 2 && req.SmsType != 3 && req.SmsType != 4 {
		response.FailWithMessage("短信类型参数错误，1-注册验证码，2-登录验证码，3-找回密码验证码,4-绑定手机号", c)
		return
	}

	//注册验证码，先检查手机号有没有被注册过
	if req.SmsType == 1 {
		_, err = userService.GetUserByPhone(req.Phone)
		if err == nil {
			response.FailWithMessage("手机号已注册", c)
			return
		}
	}

	//找回密码验证码，登录，先检查手机号是否已注册，
	if req.SmsType == 3 || req.SmsType == 2 {
		_, err = userService.GetUserByPhone(req.Phone)
		if err != nil {
			response.FailWithMessage("手机号未注册", c)
			return
		}
	}

	// 检查是否存在未过期的验证码（防止重复发送）
	existingCodeKey := "SMS_CODE:" + req.Phone
	if existingCode, err := global.GVA_REDIS.Get(c, existingCodeKey).Result(); err == nil && existingCode != "" {
		if ttl, err := global.GVA_REDIS.TTL(c, existingCodeKey).Result(); err == nil && ttl > time.Minute*4 {
			// 如果验证码还有超过4分钟的有效期，则不允许重新发送
			global.GVA_LOG.Warn("SMS code request too frequent",
				zap.String("ip", c.ClientIP()),
				zap.String("phone", utils.MaskPhone(req.Phone)),
				zap.Duration("remaining_ttl", ttl))
			response.FailWithMessage("验证码发送过于频繁，请稍后再试", c)
			return
		}
	}

	// 生成6位随机验证码
	code := utils.GenerateRandomCode(6)

	// 将验证码存储到Redis，有效期5分钟
	key := "SMS_CODE:" + req.Phone
	err = global.GVA_REDIS.Set(c, key, code, time.Minute*5).Err()
	if err != nil {
		global.GVA_LOG.Error("存储验证码失败!", zap.Error(err))
		response.FailWithMessage("发送验证码失败", c)
		return
	}

	// 发送短信验证码 - 使用新的插件系统，传递短信类型
	err = smsService.SendSMSCodeWithType(req.Phone, code, req.SmsType)
	if err != nil {
		global.GVA_LOG.Error("发送短信验证码失败!",
			zap.String("ip", c.ClientIP()),
			zap.String("phone", utils.MaskPhone(req.Phone)),
			zap.Int("sms_type", req.SmsType),
			zap.Error(err))
		response.FailWithMessage("发送验证码失败", c)
		return
	}

	// 记录成功发送的日志
	global.GVA_LOG.Info("SMS code sent successfully",
		zap.String("ip", c.ClientIP()),
		zap.String("phone", utils.MaskPhone(req.Phone)),
		zap.Int("sms_type", req.SmsType))

	response.OkWithMessage("验证码发送成功", c)
	//response.OkWithData(code, c)
}

// ChangePassword
// @Tags      SysUser
// @Summary   用户修改密码
// @Security  ApiKeyAuth
// @Produce  application/json
// @Param     data  body      systemReq.ChangePasswordReq    true  "用户名, 原密码, 新密码"
// @Success   200   {object}  response.Response{msg=string}  "用户修改密码"
// @Router    /user/changePassword [post]
func (b *BaseApi) ChangePassword(c *gin.Context) {
	var req systemReq.ChangePasswordReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, utils.ChangePasswordVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	uid := utils.GetUserID(c)

	// 获取当前用户信息以检查密码是否为空
	currentUser, err := userService.FindUserById(int(uid))
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	// 检查当前用户密码是否为空或空字符串
	if currentUser.Password == "" {
		// 密码为空时，直接更新新密码，不验证原密码
		currentUser.Password = utils.BcryptHash(req.NewPassword)
		err = global.GVA_DB.Save(currentUser).Error
		if err != nil {
			global.GVA_LOG.Error("修改失败!", zap.Error(err))
			response.FailWithMessage("修改失败", c)
			return
		}
	} else {
		// 密码不为空时，按原逻辑验证原密码
		u := &system.SysUser{GVA_MODEL: global.GVA_MODEL{ID: uid}, Password: req.Password}
		_, err = userService.ChangePassword(u, req.NewPassword)
		if err != nil {
			global.GVA_LOG.Error("修改失败!", zap.Error(err))
			response.FailWithMessage("修改失败，原密码与当前账户不符", c)
			return
		}
	}

	response.OkWithMessage("修改成功", c)
}

// GetUserList
// @Tags      SysUser
// @Summary   分页获取用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.GetUserList                                        true  "页码, 每页大小"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "分页获取用户列表,返回包括列表,总数,页码,每页数量"
// @Router    /user/getUserList [post]
func (b *BaseApi) GetUserList(c *gin.Context) {
	var pageInfo systemReq.GetUserList
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(pageInfo, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := userService.GetUserInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// SetUserAuthority
// @Tags      SysUser
// @Summary   更改用户权限
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.SetUserAuth          true  "用户UUID, 角色ID"
// @Success   200   {object}  response.Response{msg=string}  "设置用户权限"
// @Router    /user/setUserAuthority [post]
func (b *BaseApi) SetUserAuthority(c *gin.Context) {
	var sua systemReq.SetUserAuth
	err := c.ShouldBindJSON(&sua)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if UserVerifyErr := utils.Verify(sua, utils.SetUserAuthorityVerify); UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	err = userService.SetUserAuthority(userID, sua.AuthorityId)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	claims := utils.GetUserInfo(c)
	claims.AuthorityId = sua.AuthorityId
	token, err := utils.NewJWT().CreateToken(*claims)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	c.Header("new-token", token)
	c.Header("new-expires-at", strconv.FormatInt(claims.ExpiresAt.Unix(), 10))
	utils.SetToken(c, token, int((claims.ExpiresAt.Unix()-time.Now().Unix())/60))
	response.OkWithMessage("修改成功", c)
}

// SetUserAuthorities
// @Tags      SysUser
// @Summary   设置用户权限
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.SetUserAuthorities   true  "用户UUID, 角色ID"
// @Success   200   {object}  response.Response{msg=string}  "设置用户权限"
// @Router    /user/setUserAuthorities [post]
func (b *BaseApi) SetUserAuthorities(c *gin.Context) {
	var sua systemReq.SetUserAuthorities
	err := c.ShouldBindJSON(&sua)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	authorityID := utils.GetUserAuthorityId(c)
	err = userService.SetUserAuthorities(authorityID, sua.ID, sua.AuthorityIds)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
		return
	}
	response.OkWithMessage("修改成功", c)
}

// DeleteUser
// @Tags      SysUser
// @Summary   删除用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.GetById                true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}  "删除用户"
// @Router    /user/deleteUser [delete]
func (b *BaseApi) DeleteUser(c *gin.Context) {
	var reqId request.GetById
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(reqId, utils.IdVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	jwtId := utils.GetUserID(c)
	if jwtId == uint(reqId.ID) {
		response.FailWithMessage("删除失败, 无法删除自己。", c)
		return
	}
	err = userService.DeleteUser(reqId.ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	//删除 Authing 用户
	authingId := utils.GetUserAuthingId(c)
	// 如果 authingId 为空，则不删除 Authing 用户,不抛出异常
	if authingId != "" {
		userIds := []string{authingId}
		if deleteErr := managementService.DeleteUsers(userIds); deleteErr != nil {
			global.GVA_LOG.Error(fmt.Sprintf("Failed to rollback Authing user: %v", deleteErr))
		}
	}
	response.OkWithMessage("删除成功", c)
}

// SetUserInfo
// @Tags      SysUser
// @Summary   设置用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      system.SysUser                                             true  "ID, 用户名, 昵称, 头像链接"
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "设置用户信息"
// @Router    /user/setUserInfo [put]
func (b *BaseApi) SetUserInfo(c *gin.Context) {
	var user systemReq.ChangeUserInfo
	err := c.ShouldBindJSON(&user)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(user, utils.IdVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if len(user.AuthorityIds) != 0 {
		authorityID := utils.GetUserAuthorityId(c)
		err = userService.SetUserAuthorities(authorityID, user.ID, user.AuthorityIds)
		if err != nil {
			global.GVA_LOG.Error("设置失败!", zap.Error(err))
			response.FailWithMessage("设置失败", c)
			return
		}
	}
	err = userService.SetUserInfo(system.SysUser{
		GVA_MODEL: global.GVA_MODEL{
			ID: user.ID,
		},
		NickName:  user.NickName,
		HeaderImg: user.HeaderImg,
		Phone:     user.Phone,
		Email:     user.Email,
		Enable:    user.Enable,
	})
	if err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
		return
	}
	response.OkWithMessage("设置成功", c)
}

// SetSelfInfo
// @Tags      SysUser
// @Summary   设置用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      system.SysUser                                             true  "ID, 用户名, 昵称, 头像链接"
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "设置用户信息"
// @Router    /user/SetSelfInfo [put]
func (b *BaseApi) SetSelfInfo(c *gin.Context) {
	var user systemReq.ChangeUserInfo
	err := c.ShouldBindJSON(&user)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	user.ID = utils.GetUserID(c)
	err = userService.SetSelfInfo(system.SysUser{
		GVA_MODEL: global.GVA_MODEL{
			ID: user.ID,
		},
		NickName:  user.NickName,
		HeaderImg: user.HeaderImg,
		Phone:     user.Phone,
		Email:     user.Email,
		Enable:    user.Enable,
	})
	if err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
		return
	}

	// 更新 Authing 用户资料
	_ = authV3Service.UpdateProfile(user)
	response.OkWithMessage("设置成功", c)
}

// SetSelfSetting
// @Tags      SysUser
// @Summary   设置用户配置
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      map[string]interface{}  true  "用户配置数据"
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "设置用户配置"
// @Router    /user/SetSelfSetting [put]
func (b *BaseApi) SetSelfSetting(c *gin.Context) {
	var req common.JSONMap
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = userService.SetSelfSetting(req, utils.GetUserID(c))
	if err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
		return
	}
	response.OkWithMessage("设置成功", c)
}

// GetUserInfo
// @Tags      SysUser
// @Summary   获取用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200  {object}  response.Response{data=map[string]interface{},msg=string}  "获取用户信息"
// @Router    /user/getUserInfo [get]
func (b *BaseApi) GetUserInfo(c *gin.Context) {
	uuid := utils.GetUserUuid(c)
	ReqUser, err := userService.GetUserInfo(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	//返回一个apiKey
	apiKey, err := apiKeyService.FindLatestApiKey(ReqUser.ID)
	if err != nil {
		global.GVA_LOG.Error("获取API密钥失败", zap.Error(err))
		ReqUser.ApiKey = "" // 设置默认值
	} else if apiKey != nil {
		ReqUser.ApiKey = apiKey.ApiKey
	} else {
		ReqUser.ApiKey = "" // apiKey为nil时设置默认值
	}
	response.OkWithDetailed(gin.H{"userInfo": ReqUser}, "获取成功", c)
}

// ResetPassword
// @Tags      SysUser
// @Summary   重置用户密码
// @Security  ApiKeyAuth
// @Produce  application/json
// @Param     data  body      system.SysUser                 true  "ID"
// @Success   200   {object}  response.Response{msg=string}  "重置用户密码"
// @Router    /user/resetPassword [post]
func (b *BaseApi) ResetPassword(c *gin.Context) {
	var req systemReq.ChangePasswordReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = userService.ResetPassword(req.ID)
	if err != nil {
		global.GVA_LOG.Error("重置失败!", zap.Error(err))
		response.FailWithMessage("重置失败"+err.Error(), c)
		return
	}
	response.OkWithMessage("重置成功", c)
}

// ResetPasswordByPhone
// @Tags      Base
// @Summary   通过手机号找回密码
// @Produce   application/json
// @Param     data  body      systemReq.ResetPasswordReq                    true  "手机号, 短信验证码, 新密码"
// @Success   200   {object}  response.Response{msg=string}                "找回密码成功"
// @Router    /base/resetPassword [post]
func (b *BaseApi) ResetPasswordByPhone(c *gin.Context) {
	var req systemReq.ResetPasswordReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用统一验证规则
	err = utils.Verify(req, utils.ResetPasswordVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	//// 验证密码强度
	//if !utils.ValidatePassword(req.NewPassword) {
	//	response.FailWithMessage("密码必须包含数字、字母和特殊字符的两种，长度至少6位", c)
	//	return
	//}

	// 验证短信验证码
	if !authProviderService.VerifyPhoneCode(req.Phone, req.Code) {
		response.FailWithMessage("验证码错误", c)
		return
	}

	// 重置密码
	err = userService.ResetPasswordByPhone(req.Phone, req.NewPassword)
	if err != nil {
		global.GVA_LOG.Error("找回密码失败!", zap.Error(err))
		response.FailWithMessage("找回密码失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("密码重置成功", c)
}

// GetUserPoints
// @Tags     SysUser
// @Summary  获取用户积分
// @Security ApiKeyAuth
// @accept   application/json
// @Produce  application/json
// @Success  200  {object}  response.Response{data=systemRes.UserPointsResponse,msg=string}  "获取用户积分信息,返回包括用户积分详情"
// @Router   /user/getUserPoints [get]
func (b *BaseApi) GetUserPoints(c *gin.Context) {
	userUuid := utils.GetUserUuid(c)
	if userUuid == (uuid.UUID{}) {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	points, freePoints, totalPoints, vipLevel, err := userService.GetUserPoints(userUuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户积分失败!", zap.Error(err))
		response.FailWithMessage("获取用户积分失败", c)
		return
	}

	pointsData := systemRes.UserPointsResponse{
		Points:      points,
		FreePoints:  freePoints,
		TotalPoints: totalPoints,
		VipLevel:    vipLevel,
	}

	response.OkWithDetailed(pointsData, "获取用户积分成功", c)
}

// CheckAndGrantFirstLoginReward 检查并发放首次登录奖励
func (b *BaseApi) CheckAndGrantFirstLoginReward(userID uint) {
	// 检查用户是否已经完成过首次登录任务
	var count int64
	err := global.GVA_DB.Table("sys_user_tasks").
		Where("user_id = ? AND task_id = ? AND status = ?",
			userID, config.TASK_FIRST_LOGIN, "completed").
		Count(&count).Error

	if err != nil {
		global.GVA_LOG.Error("检查首次登录任务失败",
			zap.Uint("userID", userID),
			zap.Error(err))
		return
	}

	// 如果已经完成过首次登录任务，直接返回
	if count > 0 {
		return
	}

	// 发放首次登录奖励
	err = integral.TaskServiceApp.AddPointsWithTaskRecord(
		userID,
		200,
		"首次登录奖励",
		"task",
		config.TASK_FIRST_LOGIN,
	)

	if err != nil {
		global.GVA_LOG.Error("发放首次登录奖励失败",
			zap.Uint("userID", userID),
			zap.Error(err))
		return
	}

	global.GVA_LOG.Info("首次登录奖励发放成功",
		zap.Uint("userID", userID),
		zap.Int("reward", 200))
}

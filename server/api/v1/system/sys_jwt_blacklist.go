package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type JwtApi struct{}

// JsonInBlacklist
// @Tags      Jwt
// @Summary   jwt加入黑名单
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200  {object}  response.Response{msg=string}  "jwt加入黑名单"
// @Router    /jwt/jsonInBlacklist [post]
func (j *JwtApi) JsonInBlacklist(c *gin.Context) {
	token := utils.GetToken(c)

	// 获取用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 停用当前token对应的会话（该方法内部已包含JWT黑名单操作）
	err := userSessionService.DeactivateSession(token)
	if err != nil {
		global.GVA_LOG.Error("停用用户会话失败!", zap.Error(err))
		response.FailWithMessage("退出登录失败", c)
		return
	}
	utils.ClearToken(c)
	response.OkWithMessage("jwt作废成功", c)
}

// ExpireToken
// @Tags      Jwt
// @Summary   使指定token过期
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      map[string]string                                   true  "token过期请求"
// @Success   200   {object}  response.Response{msg=string}                       "token过期成功"
// @Router    /jwt/expireToken [post]
func (j *JwtApi) ExpireToken(c *gin.Context) {
	var req struct {
		Token string `json:"token" binding:"required"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("请求参数错误: "+err.Error(), c)
		return
	}

	// 停用指定token对应的会话（该方法内部已包含JWT黑名单操作）
	err = userSessionService.DeactivateSession(req.Token)
	if err != nil {
		global.GVA_LOG.Error("停用指定token会话失败!", zap.Error(err))
		response.FailWithMessage("token过期失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("token过期成功", c)
}

// VerifyToken
// @Tags      Jwt
// @Summary   验证token是否有效
// @accept    application/json
// @Produce   application/json
// @Success   200  {object}  response.Response{data=systemRes.JwtVerifyResponse,msg=string}  "token验证结果"
// @Router    /jwt/verify [post]
func (j *JwtApi) VerifyToken(c *gin.Context) {
	token := utils.GetToken(c)

	// 创建响应结构
	var result systemRes.JwtVerifyResponse

	if token == "" {
		result = systemRes.JwtVerifyResponse{
			Valid:   false,
			Code:    systemRes.TokenEmpty,
			Message: systemRes.GetCodeMessage(systemRes.TokenEmpty),
		}
		response.OkWithDetailed(result, "token验证完成", c)
		return
	}

	// 检查token是否在黑名单中
	if jwtService.IsBlacklist(token) {
		result = systemRes.JwtVerifyResponse{
			Valid:   false,
			Code:    systemRes.TokenBlacklisted,
			Message: systemRes.GetCodeMessage(systemRes.TokenBlacklisted),
		}
		response.OkWithDetailed(result, "token验证完成", c)
		return
	}

	// 验证会话是否有效
	session, sessionErr := userSessionService.ValidateSession(token)
	if sessionErr != nil {
		var code string

		// 根据会话验证错误类型设置不同的code
		if sessionErr.Error() == "会话不存在或已过期" {
			code = systemRes.SessionExpired
		} else {
			code = systemRes.SessionInvalid
		}

		result = systemRes.JwtVerifyResponse{
			Valid:   false,
			Code:    code,
			Message: systemRes.GetCodeMessage(code),
		}
		response.OkWithDetailed(result, "token验证完成", c)
		return
	}

	// 解析token
	jwtObj := utils.NewJWT()
	claims, err := jwtObj.ParseToken(token)
	if err != nil {
		var code string
		var canRefresh bool
		var refreshHint string

		switch err {
		case utils.TokenExpired:
			code = systemRes.TokenExpired
			// Token过期但会话仍在缓冲期内，可以刷新
			canRefresh = true
			refreshHint = "Token已过期，但可以通过刷新接口获取新Token"
		case utils.TokenNotValidYet:
			code = systemRes.TokenNotValidYet
		case utils.TokenMalformed:
			code = systemRes.TokenMalformed
		case utils.TokenSignatureInvalid:
			code = systemRes.TokenSignatureInvalid
		default:
			code = systemRes.TokenInvalid
		}

		result = systemRes.JwtVerifyResponse{
			Valid:       false,
			Code:        code,
			Message:     systemRes.GetCodeMessage(code),
			CanRefresh:  canRefresh,
			NeedsReauth: systemRes.RequiresReauth(code),
			RefreshHint: refreshHint,
		}
		response.OkWithDetailed(result, "token验证完成", c)
		return
	}

	// token有效
	result = systemRes.JwtVerifyResponse{
		Valid:       true,
		Code:        systemRes.TokenValid,
		Message:     systemRes.GetCodeMessage(systemRes.TokenValid),
		Claims:      claims,
		ExpiresAt:   claims.ExpiresAt.Unix(),
		Session:     session,
		BufferAt:    session.BufferAt.Unix(),
		CanRefresh:  false, // 有效的token不需要刷新
		NeedsReauth: false,
	}
	response.OkWithDetailed(result, "token验证完成", c)
}

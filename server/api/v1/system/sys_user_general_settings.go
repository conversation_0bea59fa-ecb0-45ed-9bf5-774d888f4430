package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserGeneralSettingsApi struct{}

// CreateUserGeneralSettings 创建用户通用设置
// @Tags UserGeneralSettings
// @Summary 创建用户通用设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body system.UserGeneralSettings true "用户通用设置"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /userGeneralSettings/createUserGeneralSettings [post]
func (userGeneralSettingsApi *UserGeneralSettingsApi) CreateUserGeneralSettings(c *gin.Context) {
	var userGeneralSettings system.UserGeneralSettings
	err := c.ShouldBindJSON(&userGeneralSettings)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userGeneralSettings.UserID = utils.GetUserID(c)

	if err := userGeneralSettingsService.CreateUserGeneralSettings(&userGeneralSettings); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteUserGeneralSettings 删除用户通用设置
// @Tags UserGeneralSettings
// @Summary 删除用户通用设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.IdsReq true "ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /userGeneralSettings/deleteUserGeneralSettings [delete]
func (userGeneralSettingsApi *UserGeneralSettingsApi) DeleteUserGeneralSettings(c *gin.Context) {
	ID := c.Query("ID")
	if err := userGeneralSettingsService.DeleteUserGeneralSettings(ID); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteUserGeneralSettingsByIds 批量删除用户通用设置
// @Tags UserGeneralSettings
// @Summary 批量删除用户通用设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /userGeneralSettings/deleteUserGeneralSettingsByIds [delete]
func (userGeneralSettingsApi *UserGeneralSettingsApi) DeleteUserGeneralSettingsByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	if err := userGeneralSettingsService.DeleteUserGeneralSettingsByIds(IDs); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateUserGeneralSettings 更新用户通用设置
// @Tags UserGeneralSettings
// @Summary 更新用户通用设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body system.UserGeneralSettings true "用户通用设置"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /userGeneralSettings/updateUserGeneralSettings [put]
func (userGeneralSettingsApi *UserGeneralSettingsApi) UpdateUserGeneralSettings(c *gin.Context) {
	var userGeneralSettings system.UserGeneralSettings
	err := c.ShouldBindJSON(&userGeneralSettings)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := userGeneralSettingsService.UpdateUserGeneralSettings(userGeneralSettings); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindUserGeneralSettings 用ID查询用户通用设置
// @Tags UserGeneralSettings
// @Summary 用ID查询用户通用设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query systemReq.UserGeneralSettingsSearch true "ID"
// @Success 200 {object} response.Response{data=system.UserGeneralSettings,msg=string} "查询成功"
// @Router /userGeneralSettings/findUserGeneralSettings [get]
func (userGeneralSettingsApi *UserGeneralSettingsApi) FindUserGeneralSettings(c *gin.Context) {
	ID := c.Query("ID")
	if userGeneralSettings, err := userGeneralSettingsService.GetUserGeneralSettings(ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(userGeneralSettings, c)
	}
}

// GetUserGeneralSettingsList 分页获取用户通用设置列表
// @Tags UserGeneralSettings
// @Summary 分页获取用户通用设置列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query systemReq.UserGeneralSettingsSearch true "页码, 每页大小, 搜索条件"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /userGeneralSettings/getUserGeneralSettingsList [get]
func (userGeneralSettingsApi *UserGeneralSettingsApi) GetUserGeneralSettingsList(c *gin.Context) {
	var pageInfo systemReq.UserGeneralSettingsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := userGeneralSettingsService.GetUserGeneralSettingsInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetMyGeneralSettings 获取当前用户的通用设置
// @Tags UserGeneralSettings
// @Summary 获取当前用户的通用设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=systemRes.UserGeneralSettingsResponse,msg=string} "获取成功"
// @Router /desktop/settings [get]
func (userGeneralSettingsApi *UserGeneralSettingsApi) GetMyGeneralSettings(c *gin.Context) {
	userID := utils.GetUserID(c)
	if settings, err := userGeneralSettingsService.GetUserGeneralSettingsByUserID(userID); err != nil {
		global.GVA_LOG.Error("获取用户设置失败!", zap.Error(err))
		response.FailWithMessage("获取用户设置失败", c)
	} else {
		// 构造响应数据
		resp := systemRes.UserGeneralSettingsResponse{
			Settings: settings,
			Permissions: systemRes.PermissionsInfo{
				AccessibilityEnabled:   settings.AccessibilityEnabled,
				ScreenRecordingEnabled: settings.ScreenRecordingEnabled,
				FullDiskAccessEnabled:  settings.FullDiskAccessEnabled,
			},
		}
		response.OkWithData(resp, c)
	}
}

// UpdateMyGeneralSettings 更新当前用户的通用设置
// @Tags UserGeneralSettings
// @Summary 更新当前用户的通用设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UpdateUserGeneralSettingsReq true "用户设置更新请求"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /desktop/settings [put]
func (userGeneralSettingsApi *UserGeneralSettingsApi) UpdateMyGeneralSettings(c *gin.Context) {
	var req systemReq.UpdateUserGeneralSettingsReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	if err := userGeneralSettingsService.UpdateUserGeneralSettingsByUserID(userID, req); err != nil {
		global.GVA_LOG.Error("更新用户设置失败!", zap.Error(err))
		response.FailWithMessage("更新用户设置失败", c)
	} else {
		response.OkWithMessage("设置更新成功", c)
	}
}

// UpdateMyPermissions 更新当前用户的权限状态
// @Tags UserGeneralSettings
// @Summary 更新当前用户的权限状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UpdatePermissionsReq true "权限状态更新请求"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /desktop/permissions [put]
func (userGeneralSettingsApi *UserGeneralSettingsApi) UpdateMyPermissions(c *gin.Context) {
	var req systemReq.UpdatePermissionsReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	if err := userGeneralSettingsService.UpdatePermissionsByUserID(userID, req); err != nil {
		global.GVA_LOG.Error("更新权限状态失败!", zap.Error(err))
		response.FailWithMessage("更新权限状态失败", c)
	} else {
		response.OkWithMessage("权限状态更新成功", c)
	}
}

// GetGeneralSettingsOptions 获取通用设置选项
// @Tags UserGeneralSettings
// @Summary 获取通用设置选项
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=systemRes.GeneralSettingsOptionsResponse,msg=string} "获取成功"
// @Router /desktop/options [get]
func (userGeneralSettingsApi *UserGeneralSettingsApi) GetGeneralSettingsOptions(c *gin.Context) {
	options := userGeneralSettingsService.GetGeneralSettingsOptions()
	response.OkWithData(options, c)
}

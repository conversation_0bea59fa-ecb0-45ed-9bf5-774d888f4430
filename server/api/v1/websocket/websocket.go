package websocket

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service/websocket"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type WebSocketApi struct{}

// HandleWebSocket 处理WebSocket连接
// @Tags     WebSocket
// @Summary  建立WebSocket连接
// @Accept   websocket
// @Produce  websocket
// @Router   /websocket [get]
func (w *WebSocketApi) HandleWebSocket(c *gin.Context) {
	// 从JWT token获取用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 检查用户是否已连接企业微信客服
	var user system.SysUser
	err := global.GVA_DB.Where("id = ?", userID).First(&user).Error
	if err != nil {
		response.FailWithMessage("用户不存在", c)
		return
	}

	if !user.ConnectedKf {
		response.FailWithMessage("用户未连接企业微信客服，请先通过企业微信进入会话", c)
		return
	}

	// 设置用户ID到上下文
	c.Set("userID", userID)

	// 处理WebSocket连接
	if websocket.GlobalWebSocketService != nil {
		websocket.GlobalWebSocketService.HandleWebSocket(c)
	} else {
		response.FailWithMessage("WebSocket服务未启动", c)
	}
}

// GetConnectedUsers 获取当前连接的用户列表
// @Tags     WebSocket
// @Summary  获取当前连接的用户列表
// @Produce  json
// @Success  200  {object}  response.Response{data=[]uint,msg=string}  "获取成功"
// @Router   /websocket/users [get]
func (w *WebSocketApi) GetConnectedUsers(c *gin.Context) {
	if websocket.GlobalWebSocketService == nil {
		response.FailWithMessage("WebSocket服务未启动", c)
		return
	}

	users := websocket.GlobalWebSocketService.GetConnectedUsers()
	response.OkWithDetailed(gin.H{"users": users, "count": len(users)}, "获取成功", c)
}

// CheckUserConnection 检查用户是否连接
// @Tags     WebSocket
// @Summary  检查指定用户是否有WebSocket连接
// @Produce  json
// @Param    userId  query     int                                      true  "用户ID"
// @Success  200     {object}  response.Response{data=bool,msg=string}  "检查成功"
// @Router   /websocket/check [get]
func (w *WebSocketApi) CheckUserConnection(c *gin.Context) {
	if websocket.GlobalWebSocketService == nil {
		response.FailWithMessage("WebSocket服务未启动", c)
		return
	}

	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	isConnected := websocket.GlobalWebSocketService.IsUserConnected(userID)
	response.OkWithDetailed(gin.H{"connected": isConnected}, "检查成功", c)
}

// CheckKfConnectionStatus 检查用户企业微信客服连接状态
// @Tags     WebSocket
// @Summary  检查用户是否已连接企业微信客服
// @Produce  json
// @Success  200  {object}  response.Response{data=gin.H,msg=string}  "检查成功"
// @Router   /websocket/kf-status [get]
func (w *WebSocketApi) CheckKfConnectionStatus(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	var user system.SysUser
	err := global.GVA_DB.Where("id = ?", userID).First(&user).Error
	if err != nil {
		response.FailWithMessage("用户不存在", c)
		return
	}

	// 检查WebSocket连接状态
	var wsConnected bool
	if websocket.GlobalWebSocketService != nil {
		wsConnected = websocket.GlobalWebSocketService.IsUserConnected(userID)
	}

	response.OkWithDetailed(gin.H{
		"connected_kf":   user.ConnectedKf,
		"ws_connected":   wsConnected,
		"can_connect_ws": user.ConnectedKf, // 只有connected_kf为true才能建立WebSocket连接
		"unionid":        user.McpcnWechatUnionid,
	}, "检查成功", c)
}

// GetUserConnectedDevices 获取用户已连接的设备列表
// @Tags     WebSocket
// @Summary  获取用户已连接的设备列表
// @Produce  json
// @Success  200  {object}  response.Response{data=[]string,msg=string}  "获取成功"
// @Router   /websocket/devices [get]
func (w *WebSocketApi) GetUserConnectedDevices(c *gin.Context) {
	if websocket.GlobalWebSocketService == nil {
		response.FailWithMessage("WebSocket服务未启动", c)
		return
	}

	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	devices := websocket.GlobalWebSocketService.GetUserConnectedDevices(userID)
	response.OkWithDetailed(gin.H{"devices": devices, "count": len(devices)}, "获取成功", c)
}

// CheckDeviceConnection 检查指定设备是否连接
// @Tags     WebSocket
// @Summary  检查指定设备是否有WebSocket连接
// @Produce  json
// @Param    deviceId  query     string                                   true  "设备ID"
// @Success  200       {object}  response.Response{data=bool,msg=string}  "检查成功"
// @Router   /websocket/device/check [get]
func (w *WebSocketApi) CheckDeviceConnection(c *gin.Context) {
	if websocket.GlobalWebSocketService == nil {
		response.FailWithMessage("WebSocket服务未启动", c)
		return
	}

	deviceID := c.Query("deviceId")
	if deviceID == "" {
		response.FailWithMessage("设备ID不能为空", c)
		return
	}

	isConnected := websocket.GlobalWebSocketService.IsDeviceConnected(deviceID)
	response.OkWithDetailed(gin.H{"connected": isConnected}, "检查成功", c)
}

package wecom

import (
	"fmt"
	"io"
	"net/http"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"github.com/sbzhu/weworkapi_golang/wxbizmsgcrypt"
)

type WechatWorkApi struct{}

// GetAccessTokenHandler 获取企业微信access_token
func (a *WechatWorkApi) GetAccessToken(c *gin.Context) {

	accessToken, err := wechatWorkService.GetAccessToken()
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"access_token": accessToken}, c)
}

// VerifyURL 处理企业微信回调的GET请求，验证URL有效性
func (a *WechatWorkApi) VerifyURL(c *gin.Context) {
	msgSignature := c.Query("msg_signature")
	timestamp := c.Query("timestamp")
	nonce := c.Query("nonce")
	echostr := c.Query("echostr")

	callbackToken := global.GVA_CONFIG.Wecom.CallbackToken
	callbackEncodingAESK := global.GVA_CONFIG.Wecom.CallbackEncodingAESK
	corpID := global.GVA_CONFIG.Wecom.CorpID
	wxcpt := wxbizmsgcrypt.NewWXBizMsgCrypt(callbackToken, callbackEncodingAESK, corpID, wxbizmsgcrypt.XmlType)
	echo, cryptErr := wxcpt.VerifyURL(msgSignature, timestamp, nonce, echostr)
	if cryptErr != nil {
		errMsg := fmt.Sprintf("code=%d, msg=%s", cryptErr.ErrCode, cryptErr.ErrMsg)
		response.FailWithMessage("URL验证失败: "+errMsg, c)
		global.GVA_LOG.Error("创建失败!" + errMsg)
		return
	}
	c.String(http.StatusOK, string(echo))
}

// CallbackEvent 处理企业微信回调的POST请求
func (a *WechatWorkApi) CallbackEvent(c *gin.Context) {
	msgSignature := c.Query("msg_signature")
	timestamp := c.Query("timestamp")
	nonce := c.Query("nonce")
	body, err := io.ReadAll(c.Request.Body)
	fmt.Println("body:", string(body))
	if err != nil {
		response.FailWithMessage("读取请求体失败", c)
		return
	}
	callbackToken := global.GVA_CONFIG.Wecom.CallbackToken
	callbackEncodingAESK := global.GVA_CONFIG.Wecom.CallbackEncodingAESK
	corpID := global.GVA_CONFIG.Wecom.CorpID
	wxcpt := wxbizmsgcrypt.NewWXBizMsgCrypt(callbackToken, callbackEncodingAESK, corpID, wxbizmsgcrypt.XmlType)
	msg, cryptErr := wxcpt.DecryptMsg(msgSignature, timestamp, nonce, body)
	if cryptErr != nil {
		errMsg := fmt.Sprintf("code=%d, msg=%s", cryptErr.ErrCode, cryptErr.ErrMsg)
		response.FailWithMessage("消息解密失败: "+errMsg, c)
		return
	}
	// 解析XML，判断事件类型
	if err := wechatWorkService.HandleKfMsgOrEvent([]byte(msg)); err != nil {
		response.FailWithMessage("事件处理失败: "+err.Error(), c)
		return
	}
	c.String(http.StatusOK, "success")
}

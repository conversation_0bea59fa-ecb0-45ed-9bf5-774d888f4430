package llm

import (
	"encoding/json"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	llmReq "github.com/flipped-aurora/gin-vue-admin/server/model/llm/request"
	llmResp "github.com/flipped-aurora/gin-vue-admin/server/model/llm/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type LLMApi struct {
}

// ChatStream handler: 支持流式响应，支持多模型调用，根据请求参数自动选择模型
func (l *LLMApi) ChatStream(c *gin.Context) {
	var req llmReq.LLMRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	userId := utils.GetUserID(c)
	// 设置 SSE 响应头
	c.<PERSON>.Header().Set("Content-Type", "text/event-stream")
	c.<PERSON>.Header().Set("Cache-Control", "no-cache")
	c.<PERSON>.Header().Set("Connection", "keep-alive")
	c.Writer.Flush()

	var err error
	if len(req.Tools) > 0 {
		err = llmService.FunctionCallStream(c.Request.Context(), &req, userId, func(resp *llmResp.LLMResponse) error {
			if resp == nil {
				return nil
			}
			data, err := json.Marshal(resp)
			if err != nil {
				return err
			}
			c.Writer.Write([]byte("data: "))
			c.Writer.Write(data)
			c.Writer.Write([]byte("\n\n"))
			c.Writer.Flush()
			return nil
		})
	} else {
		err = llmService.ChatStream(c.Request.Context(), &req, userId, func(resp *llmResp.LLMResponse) error {
			if resp == nil {
				return nil
			}
			data, err := json.Marshal(resp)
			if err != nil {
				return err
			}
			c.Writer.Write([]byte("data: "))
			c.Writer.Write(data)
			c.Writer.Write([]byte("\n\n"))
			c.Writer.Flush()
			return nil
		})
	}
	if err != nil {
		// SSE流已开始，不能用FailWithMessage，直接写错误事件
		c.Writer.Write([]byte("event: error\ndata: {\"error\": \"" + err.Error() + "\"}\n\n"))
		c.Writer.Flush()
		return
	}
}

// FreeChatStream handler: 不需要登录的流式响应
func (l *LLMApi) FreeChatStream(c *gin.Context) {
	var req llmReq.LLMRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	userId := uint(0) // 游客/未登录用户
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	c.Writer.Flush()
	err := llmService.ChatStream(c.Request.Context(), &req, userId, func(resp *llmResp.LLMResponse) error {
		if resp == nil {
			return nil
		}
		data, err := json.Marshal(resp)
		if err != nil {
			return err
		}
		c.Writer.Write([]byte("data: "))
		c.Writer.Write(data)
		c.Writer.Write([]byte("\n\n"))
		c.Writer.Flush()
		return nil
	})
	if err != nil {
		c.Writer.Write([]byte("event: error\ndata: {\"error\": \"" + err.Error() + "\"}\n\n"))
		c.Writer.Flush()
		return
	}
}

package global

import (
	"sync"
)

// EventHandler 事件处理函数类型
type EventHandler func(data interface{}) error

// EventManager 事件管理器
type EventManager struct {
	handlers map[string][]EventHandler
	mutex    sync.RWMutex
}

// NewEventManager 创建事件管理器
func NewEventManager() *EventManager {
	return &EventManager{
		handlers: make(map[string][]EventHandler),
	}
}

// Subscribe 订阅事件
func (em *EventManager) Subscribe(eventType string, handler EventHandler) {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	em.handlers[eventType] = append(em.handlers[eventType], handler)
}

// Publish 发布事件
func (em *EventManager) Publish(eventType string, data interface{}) error {
	em.mutex.RLock()
	handlers := em.handlers[eventType]
	em.mutex.RUnlock()

	for _, handler := range handlers {
		if err := handler(data); err != nil {
			return err
		}
	}

	return nil
}

// 全局事件管理器
var GVA_EVENT_MANAGER = NewEventManager()

// PaymentSuccessEvent 支付成功事件数据
type PaymentSuccessEvent struct {
	OrderNo string `json:"orderNo"`
}

// ProjectUpdateEvent 项目更新事件数据
type ProjectUpdateEvent struct {
	ProjectUUID string `json:"projectUUID"`
	CallMethod  string `json:"callMethod"`
	ServerName  string `json:"serverName"`
}

// 事件类型常量
const (
	EventTypePaymentSuccess = "payment.success"
	EventTypeProjectUpdate  = "project.update"
)

package global

import (
	"fmt"
	"time"

	"database/sql/driver"

	"gorm.io/gorm"
)

type DateTime time.Time

func (t DateTime) MarshalJSON() ([]byte, error) {
	if time.Time(t).IsZero() {
		return []byte("null"), nil
	}
	formatted := fmt.Sprintf("\"%s\"", time.Time(t).Format("2006-01-02 15:04:05"))
	return []byte(formatted), nil
}

func (t DateTime) Format(layout string) string {
	return time.Time(t).Format(layout)
}

func (t DateTime) Value() (driver.Value, error) {
	return time.Time(t), nil
}

func (t *DateTime) Scan(value interface{}) error {
	if value == nil {
		*t = DateTime(time.Time{})
		return nil
	}
	switch v := value.(type) {
	case time.Time:
		*t = DateTime(v)
	case []byte:
		tm, err := time.Parse("2006-01-02 15:04:05", string(v))
		if err != nil {
			return err
		}
		*t = DateTime(tm)
	case string:
		tm, err := time.Parse("2006-01-02 15:04:05", v)
		if err != nil {
			return err
		}
		*t = DateTime(tm)
	default:
		return fmt.Errorf("cannot convert %v to DateTime", value)
	}
	return nil
}

func (t *DateTime) UnmarshalJSON(data []byte) error {
	// 处理 null
	if string(data) == "null" {
		*t = DateTime(time.Time{})
		return nil
	}
	// 去掉引号
	str := string(data)
	if len(str) >= 2 && str[0] == '"' && str[len(str)-1] == '"' {
		str = str[1 : len(str)-1]
	}
	// 解析时间，优先用你的格式
	tm, err := time.Parse("2006-01-02 15:04:05", str)
	if err != nil {
		// 兼容 RFC3339
		tm, err = time.Parse(time.RFC3339, str)
		if err != nil {
			return err
		}
	}
	*t = DateTime(tm)
	return nil
}

type GVA_MODEL struct {
	ID        uint           `gorm:"primarykey" json:"ID"` // 主键ID
	CreatedAt DateTime       // 创建时间
	UpdatedAt DateTime       // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"` // 删除时间
}
